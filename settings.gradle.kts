rootProject.name = "zlim"

// 公共模块
include("common:common-core")
include("common:common-proto")
include("common:common-security")
include("common:common-web")

// 微服务模块
include("gateway-service")
include("user-service")
include("social-service")
include("message-service")

// 设置项目目录
project(":common:common-core").projectDir = file("common/common-core")
project(":common:common-proto").projectDir = file("common/common-proto")
project(":common:common-security").projectDir = file("common/common-security")
project(":common:common-web").projectDir = file("common/common-web")

project(":gateway-service").projectDir = file("gateway-service")
project(":user-service").projectDir = file("user-service")
project(":social-service").projectDir = file("social-service")
project(":message-service").projectDir = file("message-service")
