server:
  port: 8082

spring:
  application:
    name: message-service
  
  profiles:
    active: dev
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
  
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *****************************************************************************************************************
    username: zlim
    password: zlim123
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: MessageServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 2
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
    
    elasticsearch:
      repositories:
        enabled: true
      uris: http://localhost:9200

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# gRPC服务端配置
grpc:
  server:
    port: 9092
  client:
    user-service:
      address: discovery:///user-service
      negotiationType: plaintext
    gateway-service:
      address: discovery:///gateway-service
      negotiationType: plaintext

# RocketMQ配置
rocketmq:
  name-server: localhost:9876
  producer:
    group: message-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
  consumer:
    group: message-consumer-group
    consume-timeout: 15000

# 消息配置
message:
  # 消息历史保留天数
  history-retention-days: 90
  # 单次查询最大消息数
  max-query-limit: 100
  # 消息内容最大长度
  max-content-length: 10000

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.zlim: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.apache.rocketmq: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
