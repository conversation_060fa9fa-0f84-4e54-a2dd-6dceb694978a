package com.zlim.message

import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cloud.client.discovery.EnableDiscoveryClient

/**
 * 消息服务启动类
 */
@SpringBootApplication(scanBasePackages = ["com.zlim"])
@EnableDiscoveryClient
@MapperScan("com.zlim.message.mapper")
class MessageServiceApplication

fun main(args: Array<String>) {
    runApplication<MessageServiceApplication>(*args)
}
