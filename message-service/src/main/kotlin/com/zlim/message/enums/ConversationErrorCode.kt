package com.zlim.message.enums

import com.zlim.common.core.enums.BaseErrorCode

/**
 * 会话模块错误码 (04xxxx)
 */
enum class ConversationErrorCode(
    override val code: Int,
    override val messageKey: String,
    override val defaultMessage: String
) : BaseErrorCode {
    
    // ========== 会话基础错误 (040xxx) ==========
    CONVERSATION_NOT_FOUND(404001, "conversation.not.found", "Conversation not found"),
    CONVERSATION_CREATE_FAILED(404002, "conversation.create.failed", "Failed to create conversation"),
    CONVERSATION_UPDATE_FAILED(404003, "conversation.update.failed", "Failed to update conversation"),
    CONVERSATION_DELETE_FAILED(404004, "conversation.delete.failed", "Failed to delete conversation"),
    CONVERSATION_ALREADY_EXISTS(404005, "conversation.already.exists", "Conversation already exists"),
    CONVERSATION_TYPE_INVALID(404006, "conversation.type.invalid", "Invalid conversation type"),
    
    // ========== 会话状态错误 (041xxx) ==========
    CONVERSATION_INACTIVE(404101, "conversation.inactive", "Conversation is inactive"),
    CONVERSATION_ARCHIVED(404102, "conversation.archived", "Conversation is archived"),
    CONVERSATION_DISBANDED(404103, "conversation.disbanded", "Conversation has been disbanded"),
    CONVERSATION_SUSPENDED(404104, "conversation.suspended", "Conversation is suspended"),
    CONVERSATION_READONLY(404105, "conversation.readonly", "Conversation is read-only"),
    
    // ========== 会话成员错误 (042xxx) ==========
    CONVERSATION_MEMBER_NOT_FOUND(404201, "conversation.member.not.found", "Conversation member not found"),
    CONVERSATION_MEMBER_EXISTS(404202, "conversation.member.exists", "Member already exists in conversation"),
    CONVERSATION_MEMBER_ADD_FAILED(404203, "conversation.member.add.failed", "Failed to add conversation member"),
    CONVERSATION_MEMBER_REMOVE_FAILED(404204, "conversation.member.remove.failed", "Failed to remove conversation member"),
    CONVERSATION_MEMBER_UPDATE_FAILED(404205, "conversation.member.update.failed", "Failed to update conversation member"),
    NOT_CONVERSATION_MEMBER(404206, "conversation.not.member", "You are not a member of this conversation"),
    CONVERSATION_MEMBER_LIMIT_EXCEEDED(404207, "conversation.member.limit.exceeded", "Conversation member limit exceeded"),
    
    // ========== 会话权限错误 (043xxx) ==========
    CONVERSATION_PERMISSION_DENIED(404301, "conversation.permission.denied", "No permission for this conversation"),
    CONVERSATION_ADMIN_REQUIRED(404302, "conversation.admin.required", "Conversation admin permission required"),
    CONVERSATION_OWNER_REQUIRED(404303, "conversation.owner.required", "Conversation owner permission required"),
    CONVERSATION_MEMBER_ROLE_INVALID(404304, "conversation.member.role.invalid", "Invalid conversation member role"),
    CONVERSATION_ROLE_CHANGE_DENIED(404305, "conversation.role.change.denied", "Conversation role change denied"),
    CONVERSATION_OPERATION_NOT_ALLOWED(404306, "conversation.operation.not.allowed", "Conversation operation not allowed"),
    
    // ========== 会话设置错误 (044xxx) ==========
    CONVERSATION_SETTINGS_INVALID(404401, "conversation.settings.invalid", "Invalid conversation settings"),
    CONVERSATION_SETTINGS_UPDATE_FAILED(404402, "conversation.settings.update.failed", "Failed to update conversation settings"),
    CONVERSATION_JOIN_MODE_INVALID(404403, "conversation.join.mode.invalid", "Invalid conversation join mode"),
    CONVERSATION_INVITE_DISABLED(404404, "conversation.invite.disabled", "Conversation invite is disabled"),
    CONVERSATION_JOIN_DISABLED(404405, "conversation.join.disabled", "Conversation join is disabled"),
    
    // ========== 会话邀请错误 (045xxx) ==========
    CONVERSATION_INVITE_NOT_FOUND(404501, "conversation.invite.not.found", "Conversation invite not found"),
    CONVERSATION_INVITE_EXPIRED(404502, "conversation.invite.expired", "Conversation invite expired"),
    CONVERSATION_INVITE_INVALID(404503, "conversation.invite.invalid", "Invalid conversation invite"),
    CONVERSATION_INVITE_ALREADY_USED(404504, "conversation.invite.already.used", "Conversation invite already used"),
    CONVERSATION_INVITE_CREATE_FAILED(404505, "conversation.invite.create.failed", "Failed to create conversation invite"),
    CONVERSATION_INVITE_LIMIT_EXCEEDED(404506, "conversation.invite.limit.exceeded", "Conversation invite limit exceeded"),
    
    // ========== 会话加入/退出错误 (046xxx) ==========
    CONVERSATION_JOIN_FAILED(404601, "conversation.join.failed", "Failed to join conversation"),
    CONVERSATION_LEAVE_FAILED(404602, "conversation.leave.failed", "Failed to leave conversation"),
    CONVERSATION_FULL(404603, "conversation.full", "Conversation is full"),
    CONVERSATION_JOIN_APPROVAL_REQUIRED(404604, "conversation.join.approval.required", "Conversation join approval required"),
    CONVERSATION_OWNER_CANNOT_LEAVE(404605, "conversation.owner.cannot.leave", "Conversation owner cannot leave"),
    CONVERSATION_LAST_ADMIN_CANNOT_LEAVE(404606, "conversation.last.admin.cannot.leave", "Last admin cannot leave conversation"),
    
    // ========== 会话信息错误 (047xxx) ==========
    CONVERSATION_NAME_EMPTY(404701, "conversation.name.empty", "Conversation name is empty"),
    CONVERSATION_NAME_TOO_LONG(404702, "conversation.name.too.long", "Conversation name is too long"),
    CONVERSATION_NAME_INVALID(404703, "conversation.name.invalid", "Invalid conversation name"),
    CONVERSATION_DESCRIPTION_TOO_LONG(404704, "conversation.description.too.long", "Conversation description is too long"),
    CONVERSATION_AVATAR_UPLOAD_FAILED(404705, "conversation.avatar.upload.failed", "Conversation avatar upload failed"),
    CONVERSATION_AVATAR_SIZE_EXCEEDED(404706, "conversation.avatar.size.exceeded", "Conversation avatar size exceeded"),
    
    // ========== 会话查询错误 (048xxx) ==========
    CONVERSATION_QUERY_FAILED(404801, "conversation.query.failed", "Conversation query failed"),
    CONVERSATION_LIST_QUERY_FAILED(404802, "conversation.list.query.failed", "Conversation list query failed"),
    CONVERSATION_SEARCH_FAILED(404803, "conversation.search.failed", "Conversation search failed"),
    CONVERSATION_FILTER_INVALID(404804, "conversation.filter.invalid", "Invalid conversation filter"),
    
    // ========== 会话同步错误 (049xxx) ==========
    CONVERSATION_SYNC_FAILED(404901, "conversation.sync.failed", "Conversation sync failed"),
    CONVERSATION_VERSION_CONFLICT(404902, "conversation.version.conflict", "Conversation version conflict"),
    CONVERSATION_STATE_INCONSISTENT(404903, "conversation.state.inconsistent", "Conversation state inconsistent"),
    CONVERSATION_MEMBER_SYNC_FAILED(404904, "conversation.member.sync.failed", "Conversation member sync failed");
}
