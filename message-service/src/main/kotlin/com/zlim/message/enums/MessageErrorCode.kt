package com.zlim.message.enums

import com.zlim.common.core.enums.BaseErrorCode

/**
 * 消息模块错误码 (03xxxx)
 */
enum class MessageErrorCode(
    override val code: Int,
    override val messageKey: String,
    override val defaultMessage: String
) : BaseErrorCode {
    
    // ========== 消息基础错误 (030xxx) ==========
    MESSAGE_NOT_FOUND(403001, "message.not.found", "Message not found"),
    MESSAGE_SEND_FAILED(403002, "message.send.failed", "Failed to send message"),
    MESSAGE_RECEIVE_FAILED(403003, "message.receive.failed", "Failed to receive message"),
    MESSAGE_DELIVERY_FAILED(403004, "message.delivery.failed", "Message delivery failed"),
    MESSAGE_PROCESSING_FAILED(403005, "message.processing.failed", "Message processing failed"),
    
    // ========== 消息内容错误 (031xxx) ==========
    MESSAGE_CONTENT_EMPTY(403101, "message.content.empty", "Message content is empty"),
    MESSAGE_CONTENT_TOO_LONG(403102, "message.content.too.long", "Message content is too long"),
    MESSAGE_CONTENT_INVALID(403103, "message.content.invalid", "Invalid message content"),
    MESSAGE_TYPE_NOT_SUPPORTED(403104, "message.type.not.supported", "Message type not supported"),
    MESSAGE_FORMAT_INVALID(403105, "message.format.invalid", "Invalid message format"),
    MESSAGE_ENCODING_FAILED(403106, "message.encoding.failed", "Message encoding failed"),
    MESSAGE_DECODING_FAILED(403107, "message.decoding.failed", "Message decoding failed"),
    
    // ========== 消息状态错误 (032xxx) ==========
    MESSAGE_ALREADY_READ(403201, "message.already.read", "Message already read"),
    MESSAGE_ALREADY_DELETED(403202, "message.already.deleted", "Message already deleted"),
    MESSAGE_ALREADY_RECALLED(403203, "message.already.recalled", "Message already recalled"),
    MESSAGE_STATUS_INVALID(403204, "message.status.invalid", "Invalid message status"),
    MESSAGE_STATUS_UPDATE_FAILED(403205, "message.status.update.failed", "Message status update failed"),
    
    // ========== 消息操作错误 (033xxx) ==========
    MESSAGE_RECALL_FAILED(403301, "message.recall.failed", "Failed to recall message"),
    MESSAGE_RECALL_TIMEOUT(403302, "message.recall.timeout", "Message recall timeout"),
    MESSAGE_RECALL_NOT_ALLOWED(403303, "message.recall.not.allowed", "Message recall not allowed"),
    MESSAGE_DELETE_FAILED(403304, "message.delete.failed", "Failed to delete message"),
    MESSAGE_DELETE_NOT_ALLOWED(403305, "message.delete.not.allowed", "Message delete not allowed"),
    MESSAGE_EDIT_FAILED(403306, "message.edit.failed", "Failed to edit message"),
    MESSAGE_EDIT_NOT_ALLOWED(403307, "message.edit.not.allowed", "Message edit not allowed"),
    MESSAGE_EDIT_TIMEOUT(403308, "message.edit.timeout", "Message edit timeout"),
    MESSAGE_FORWARD_FAILED(403309, "message.forward.failed", "Failed to forward message"),
    MESSAGE_FORWARD_NOT_ALLOWED(403310, "message.forward.not.allowed", "Message forward not allowed"),
    
    // ========== 消息查询错误 (034xxx) ==========
    MESSAGE_QUERY_FAILED(403401, "message.query.failed", "Message query failed"),
    MESSAGE_HISTORY_NOT_FOUND(403402, "message.history.not.found", "Message history not found"),
    MESSAGE_SEARCH_FAILED(403403, "message.search.failed", "Message search failed"),
    MESSAGE_PAGINATION_INVALID(403404, "message.pagination.invalid", "Invalid message pagination"),
    MESSAGE_FILTER_INVALID(403405, "message.filter.invalid", "Invalid message filter"),
    
    // ========== 消息权限错误 (035xxx) ==========
    MESSAGE_ACCESS_DENIED(403501, "message.access.denied", "Message access denied"),
    MESSAGE_PERMISSION_INSUFFICIENT(403502, "message.permission.insufficient", "Insufficient message permission"),
    MESSAGE_SENDER_MISMATCH(403503, "message.sender.mismatch", "Message sender mismatch"),
    MESSAGE_RECIPIENT_INVALID(403504, "message.recipient.invalid", "Invalid message recipient"),
    
    // ========== 消息限制错误 (036xxx) ==========
    MESSAGE_RATE_LIMITED(403601, "message.rate.limited", "Message rate limited"),
    MESSAGE_QUOTA_EXCEEDED(403602, "message.quota.exceeded", "Message quota exceeded"),
    MESSAGE_SIZE_EXCEEDED(403603, "message.size.exceeded", "Message size exceeded"),
    MESSAGE_FREQUENCY_LIMITED(403604, "message.frequency.limited", "Message frequency limited"),
    MESSAGE_DAILY_LIMIT_EXCEEDED(403605, "message.daily.limit.exceeded", "Daily message limit exceeded"),
    
    // ========== 消息媒体错误 (037xxx) ==========
    MEDIA_UPLOAD_FAILED(403701, "message.media.upload.failed", "Media upload failed"),
    MEDIA_DOWNLOAD_FAILED(403702, "message.media.download.failed", "Media download failed"),
    MEDIA_SIZE_EXCEEDED(403703, "message.media.size.exceeded", "Media size exceeded"),
    MEDIA_TYPE_NOT_SUPPORTED(403704, "message.media.type.not.supported", "Media type not supported"),
    MEDIA_PROCESSING_FAILED(403705, "message.media.processing.failed", "Media processing failed"),
    MEDIA_COMPRESSION_FAILED(403706, "message.media.compression.failed", "Media compression failed"),
    MEDIA_THUMBNAIL_GENERATION_FAILED(403707, "message.media.thumbnail.failed", "Media thumbnail generation failed"),
    
    // ========== 消息加密错误 (038xxx) ==========
    MESSAGE_ENCRYPTION_FAILED(403801, "message.encryption.failed", "Message encryption failed"),
    MESSAGE_DECRYPTION_FAILED(403802, "message.decryption.failed", "Message decryption failed"),
    MESSAGE_KEY_NOT_FOUND(403803, "message.key.not.found", "Message encryption key not found"),
    MESSAGE_KEY_EXPIRED(403804, "message.key.expired", "Message encryption key expired"),
    MESSAGE_SIGNATURE_INVALID(403805, "message.signature.invalid", "Invalid message signature"),
    
    // ========== 消息同步错误 (039xxx) ==========
    MESSAGE_SYNC_FAILED(403901, "message.sync.failed", "Message sync failed"),
    MESSAGE_CONFLICT_DETECTED(403902, "message.conflict.detected", "Message conflict detected"),
    MESSAGE_VERSION_MISMATCH(403903, "message.version.mismatch", "Message version mismatch"),
    MESSAGE_SEQUENCE_INVALID(403904, "message.sequence.invalid", "Invalid message sequence"),
    MESSAGE_DUPLICATE_DETECTED(403905, "message.duplicate.detected", "Duplicate message detected");
}
