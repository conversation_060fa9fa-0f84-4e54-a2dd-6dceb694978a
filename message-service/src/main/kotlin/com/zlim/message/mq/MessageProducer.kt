package com.zlim.message.mq

import com.fasterxml.jackson.databind.ObjectMapper
import com.zlim.message.config.RocketMQConfig
import org.apache.rocketmq.spring.core.RocketMQTemplate
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * 消息生产者
 */
@Component
class MessageProducer(
    private val rocketMQTemplate: RocketMQTemplate,
    private val objectMapper: ObjectMapper
) {
    
    private val logger = LoggerFactory.getLogger(MessageProducer::class.java)
    
    /**
     * 发送消息到投递队列
     */
    fun sendMessageForDelivery(messageData: MessageDeliveryData) {
        try {
            val destination = "${RocketMQConfig.TOPIC_MESSAGE_DELIVERY}:${messageData.conversationId}"
            rocketMQTemplate.convertAndSend(destination, messageData)
            logger.debug("发送消息投递事件: {}", messageData.messageId)
        } catch (e: Exception) {
            logger.error("发送消息投递事件失败: {}", messageData.messageId, e)
        }
    }
    
    /**
     * 发送消息确认
     */
    fun sendMessageAck(ackData: MessageAckData) {
        try {
            val destination = "${RocketMQConfig.TOPIC_MESSAGE_ACK}:${ackData.senderId}"
            rocketMQTemplate.convertAndSend(destination, ackData)
            logger.debug("发送消息确认: {}", ackData.messageId)
        } catch (e: Exception) {
            logger.error("发送消息确认失败: {}", ackData.messageId, e)
        }
    }
    
    /**
     * 发送输入状态
     */
    fun sendTypingStatus(typingData: TypingStatusData) {
        try {
            val destination = "${RocketMQConfig.TOPIC_TYPING_STATUS}:${typingData.conversationId}"
            rocketMQTemplate.convertAndSend(destination, typingData)
            logger.debug("发送输入状态: {}", typingData.conversationId)
        } catch (e: Exception) {
            logger.error("发送输入状态失败: {}", typingData.conversationId, e)
        }
    }
    
    /**
     * 发送在线状态
     */
    fun sendPresenceStatus(presenceData: PresenceStatusData) {
        try {
            val destination = "${RocketMQConfig.TOPIC_PRESENCE_STATUS}:${presenceData.userId}"
            rocketMQTemplate.convertAndSend(destination, presenceData)
            logger.debug("发送在线状态: {}", presenceData.userId)
        } catch (e: Exception) {
            logger.error("发送在线状态失败: {}", presenceData.userId, e)
        }
    }
}

/**
 * 消息投递数据
 */
data class MessageDeliveryData(
    val messageId: String,
    val conversationId: String,
    val senderId: Long,
    val messageType: Int,
    val content: Any,
    val timestamp: Long,
    val targetUserIds: List<Long>
)

/**
 * 消息确认数据
 */
data class MessageAckData(
    val messageId: String,
    val conversationId: String,
    val senderId: Long,
    val userId: Long,
    val status: Int, // 2:已送达 4:已读
    val timestamp: Long
)

/**
 * 输入状态数据
 */
data class TypingStatusData(
    val conversationId: String,
    val userId: Long,
    val typing: Boolean,
    val timestamp: Long
)

/**
 * 在线状态数据
 */
data class PresenceStatusData(
    val userId: Long,
    val status: Int, // 1:在线 2:离开 3:忙碌 4:离线
    val lastSeen: Long,
    val timestamp: Long
)
