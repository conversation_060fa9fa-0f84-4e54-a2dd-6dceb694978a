package com.zlim.message.mq

import com.zlim.message.config.RocketMQConfig
import com.zlim.message.service.MessageService
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener
import org.apache.rocketmq.spring.core.RocketMQListener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * 消息投递消费者
 */
@Component
@RocketMQMessageListener(
    topic = RocketMQConfig.TOPIC_MESSAGE_DELIVERY,
    consumerGroup = RocketMQConfig.CONSUMER_GROUP_DELIVERY
)
class MessageDeliveryConsumer(
    private val messageService: MessageService
) : RocketMQListener<MessageDeliveryData> {
    
    private val logger = LoggerFactory.getLogger(MessageDeliveryConsumer::class.java)
    
    override fun onMessage(message: MessageDeliveryData) {
        try {
            logger.debug("处理消息投递: {}", message.messageId)
            messageService.deliverMessage(message)
        } catch (e: Exception) {
            logger.error("处理消息投递失败: {}", message.messageId, e)
            throw e // 重新抛出异常，触发重试
        }
    }
}

/**
 * 消息确认消费者
 */
@Component
@RocketMQMessageListener(
    topic = RocketMQConfig.TOPIC_MESSAGE_ACK,
    consumerGroup = RocketMQConfig.CONSUMER_GROUP_ACK
)
class MessageAckConsumer(
    private val messageService: MessageService
) : RocketMQListener<MessageAckData> {
    
    private val logger = LoggerFactory.getLogger(MessageAckConsumer::class.java)
    
    override fun onMessage(message: MessageAckData) {
        try {
            logger.debug("处理消息确认: {}", message.messageId)
            messageService.processMessageAck(message)
        } catch (e: Exception) {
            logger.error("处理消息确认失败: {}", message.messageId, e)
            throw e
        }
    }
}

/**
 * 输入状态消费者
 */
@Component
@RocketMQMessageListener(
    topic = RocketMQConfig.TOPIC_TYPING_STATUS,
    consumerGroup = RocketMQConfig.CONSUMER_GROUP_TYPING
)
class TypingStatusConsumer(
    private val messageService: MessageService
) : RocketMQListener<TypingStatusData> {
    
    private val logger = LoggerFactory.getLogger(TypingStatusConsumer::class.java)
    
    override fun onMessage(message: TypingStatusData) {
        try {
            logger.debug("处理输入状态: {}", message.conversationId)
            messageService.processTypingStatus(message)
        } catch (e: Exception) {
            logger.error("处理输入状态失败: {}", message.conversationId, e)
            // 输入状态失败不需要重试
        }
    }
}

/**
 * 在线状态消费者
 */
@Component
@RocketMQMessageListener(
    topic = RocketMQConfig.TOPIC_PRESENCE_STATUS,
    consumerGroup = RocketMQConfig.CONSUMER_GROUP_PRESENCE
)
class PresenceStatusConsumer(
    private val messageService: MessageService
) : RocketMQListener<PresenceStatusData> {
    
    private val logger = LoggerFactory.getLogger(PresenceStatusConsumer::class.java)
    
    override fun onMessage(message: PresenceStatusData) {
        try {
            logger.debug("处理在线状态: {}", message.userId)
            messageService.processPresenceStatus(message)
        } catch (e: Exception) {
            logger.error("处理在线状态失败: {}", message.userId, e)
            // 在线状态失败不需要重试
        }
    }
}
