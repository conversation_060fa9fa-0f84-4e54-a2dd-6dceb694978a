package com.zlim.message.entity

import com.baomidou.mybatisplus.annotation.*
import java.time.LocalDateTime

/**
 * 会话成员实体
 */
@TableName("conversation_members")
data class ConversationMember(
    @TableId(type = IdType.ASSIGN_ID)
    val id: Long? = null,
    
    @TableField("conversation_id")
    val conversationId: String,
    
    @TableField("user_id")
    val userId: Long,
    
    @TableField("role")
    val role: Int = 1, // 1:普通成员 2:管理员 3:群主/创建者
    
    @TableField("joined_at")
    val joinedAt: LocalDateTime = LocalDateTime.now(),
    
    @TableField("last_read_message_id")
    val lastReadMessageId: String? = null,
    
    @TableField("muted")
    val muted: Boolean = false,
    
    @TableField("pinned")
    val pinned: Boolean = false,
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    val createdAt: LocalDateTime? = null,
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    val updatedAt: LocalDateTime? = null
) {
    companion object {
        // 成员角色
        const val ROLE_MEMBER = 1    // 普通成员
        const val ROLE_ADMIN = 2     // 管理员
        const val ROLE_OWNER = 3     // 群主/创建者
    }
    
    /**
     * 是否为管理员或群主
     */
    fun isAdminOrOwner(): Boolean = role >= ROLE_ADMIN
    
    /**
     * 是否为群主
     */
    fun isOwner(): Boolean = role == ROLE_OWNER
}
