package com.zlim.message.entity

import com.baomidou.mybatisplus.annotation.*
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler
import java.time.LocalDateTime

/**
 * 消息实体
 */
@TableName("messages", autoResultMap = true)
data class Message(
    @TableId(type = IdType.ASSIGN_ID)
    val id: Long? = null,
    
    @TableField("message_id")
    val messageId: String,
    
    @TableField("conversation_id")
    val conversationId: String,
    
    @TableField("sender_id")
    val senderId: Long,
    
    @TableField("message_type")
    val messageType: Int, // 1:文本 2:图片 3:音频 4:视频 5:文件 6:位置 7:系统
    
    @TableField(value = "content", typeHandler = JacksonTypeHandler::class)
    val content: MessageContent,
    
    @TableField("status")
    val status: Int = 1, // 1:发送中 2:已发送 3:已送达 4:已读 5:失败
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    val createdAt: LocalDateTime? = null,
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    val updatedAt: LocalDateTime? = null,
    
    @TableLogic
    @TableField("deleted")
    val deleted: Boolean = false
) {
    companion object {
        // 消息类型
        const val TYPE_TEXT = 1
        const val TYPE_IMAGE = 2
        const val TYPE_AUDIO = 3
        const val TYPE_VIDEO = 4
        const val TYPE_FILE = 5
        const val TYPE_LOCATION = 6
        const val TYPE_SYSTEM = 7
        
        // 消息状态
        const val STATUS_SENDING = 1
        const val STATUS_SENT = 2
        const val STATUS_DELIVERED = 3
        const val STATUS_READ = 4
        const val STATUS_FAILED = 5
    }
}

/**
 * 消息内容
 */
data class MessageContent(
    val text: String? = null,
    val url: String? = null,
    val thumbnailUrl: String? = null,
    val size: Long? = null,
    val width: Int? = null,
    val height: Int? = null,
    val duration: Int? = null,
    val mimeType: String? = null,
    val filename: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null,
    val address: String? = null,
    val title: String? = null,
    val mentions: List<MentionInfo>? = null,
    val extra: Map<String, Any>? = null
)

/**
 * @提及信息
 */
data class MentionInfo(
    val userId: Long,
    val username: String,
    val offset: Int,
    val length: Int
)
