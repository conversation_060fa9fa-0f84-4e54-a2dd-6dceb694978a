package com.zlim.message.entity

import com.baomidou.mybatisplus.annotation.*
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler
import java.time.LocalDateTime

/**
 * 会话实体
 */
@TableName("conversations", autoResultMap = true)
data class Conversation(
    @TableId(type = IdType.ASSIGN_ID)
    val id: Long? = null,
    
    @TableField("conversation_id")
    val conversationId: String,
    
    @TableField("type")
    val type: Int, // 1:私聊 2:群聊 3:频道 4:系统会话
    
    @TableField("title")
    val title: String? = null,
    
    @TableField("avatar")
    val avatar: String? = null,
    
    @TableField("description")
    val description: String? = null,
    
    @TableField(value = "settings", typeHandler = JacksonTypeHandler::class)
    val settings: ConversationSettings? = null,
    
    @TableField("created_by")
    val createdBy: Long? = null,
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    val createdAt: LocalDateTime? = null,
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    val updatedAt: LocalDateTime? = null,
    
    @TableLogic
    @TableField("deleted")
    val deleted: Boolean = false
) {
    companion object {
        // 会话类型
        const val TYPE_PRIVATE = 1  // 私聊
        const val TYPE_GROUP = 2    // 群聊
        const val TYPE_CHANNEL = 3  // 频道
        const val TYPE_SYSTEM = 4   // 系统会话
    }
}

/**
 * 会话设置
 */
data class ConversationSettings(
    val maxMembers: Int? = null,
    val joinMode: Int? = null, // 1:自由加入 2:需要验证 3:禁止加入
    val allowMemberInvite: Boolean = true,
    val allowMemberModifyInfo: Boolean = false,
    val muteAll: Boolean = false,
    val extra: Map<String, Any>? = null
)
