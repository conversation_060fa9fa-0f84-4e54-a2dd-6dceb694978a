package com.zlim.message.config

import com.zlim.common.core.enums.ErrorCodeFactory
import com.zlim.message.enums.ConversationErrorCode
import com.zlim.message.enums.MessageErrorCode
import org.springframework.context.annotation.Configuration
import jakarta.annotation.PostConstruct

/**
 * 消息服务错误码注册配置
 */
@Configuration
class ErrorCodeConfig {
    
    @PostConstruct
    fun registerErrorCodes() {
        // 注册消息模块错误码
        ErrorCodeFactory.register(MessageErrorCode.values())
        
        // 注册会话模块错误码
        ErrorCodeFactory.register(ConversationErrorCode.values())
    }
}
