package com.zlim.message.config

import org.springframework.context.annotation.Configuration

/**
 * RocketMQ配置
 */
@Configuration
class RocketMQConfig {
    
    companion object {
        // 消息主题
        const val TOPIC_MESSAGE_SEND = "message_send"
        const val TOPIC_MESSAGE_DELIVERY = "message_delivery"
        const val TOPIC_MESSAGE_ACK = "message_ack"
        const val TOPIC_TYPING_STATUS = "typing_status"
        const val TOPIC_PRESENCE_STATUS = "presence_status"
        
        // 消费者组
        const val CONSUMER_GROUP_MESSAGE = "message_consumer_group"
        const val CONSUMER_GROUP_DELIVERY = "delivery_consumer_group"
        const val CONSUMER_GROUP_ACK = "ack_consumer_group"
        const val CONSUMER_GROUP_TYPING = "typing_consumer_group"
        const val CONSUMER_GROUP_PRESENCE = "presence_consumer_group"
        
        // 生产者组
        const val PRODUCER_GROUP_MESSAGE = "message_producer_group"
    }
}
