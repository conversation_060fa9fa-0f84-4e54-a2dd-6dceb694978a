package com.zlim.message.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zlim.message.entity.ConversationMember
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * 会话成员数据访问层
 */
@Mapper
interface ConversationMemberMapper : BaseMapper<ConversationMember> {
    
    /**
     * 根据会话ID和用户ID查找成员
     */
    @Select("""
        SELECT * FROM conversation_members 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun findByConversationIdAndUserId(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long
    ): ConversationMember?
    
    /**
     * 查询会话所有成员
     */
    @Select("""
        SELECT * FROM conversation_members 
        WHERE conversation_id = #{conversationId}
        ORDER BY role DESC, joined_at ASC
    """)
    fun findByConversationId(@Param("conversationId") conversationId: String): List<ConversationMember>
    
    /**
     * 查询会话成员数量
     */
    @Select("SELECT COUNT(*) FROM conversation_members WHERE conversation_id = #{conversationId}")
    fun countByConversationId(@Param("conversationId") conversationId: String): Int
    
    /**
     * 检查用户是否为会话成员
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM conversation_members 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun isMember(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long
    ): Boolean
    
    /**
     * 更新最后已读消息
     */
    @Update("""
        UPDATE conversation_members 
        SET last_read_message_id = #{messageId}, updated_at = CURRENT_TIMESTAMP 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun updateLastReadMessage(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long,
        @Param("messageId") messageId: String
    ): Int
    
    /**
     * 更新成员角色
     */
    @Update("""
        UPDATE conversation_members 
        SET role = #{role}, updated_at = CURRENT_TIMESTAMP 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun updateMemberRole(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long,
        @Param("role") role: Int
    ): Int
    
    /**
     * 更新静音状态
     */
    @Update("""
        UPDATE conversation_members 
        SET muted = #{muted}, updated_at = CURRENT_TIMESTAMP 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun updateMuteStatus(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long,
        @Param("muted") muted: Boolean
    ): Int
    
    /**
     * 更新置顶状态
     */
    @Update("""
        UPDATE conversation_members 
        SET pinned = #{pinned}, updated_at = CURRENT_TIMESTAMP 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun updatePinStatus(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long,
        @Param("pinned") pinned: Boolean
    ): Int
    
    /**
     * 删除会话成员
     */
    @Update("""
        DELETE FROM conversation_members 
        WHERE conversation_id = #{conversationId} AND user_id = #{userId}
    """)
    fun removeMember(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long
    ): Int
    
    /**
     * 删除会话所有成员
     */
    @Update("DELETE FROM conversation_members WHERE conversation_id = #{conversationId}")
    fun removeAllMembers(@Param("conversationId") conversationId: String): Int
}
