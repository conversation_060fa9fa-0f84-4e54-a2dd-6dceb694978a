package com.zlim.message.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.zlim.message.entity.Message
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * 消息数据访问层
 */
@Mapper
interface MessageMapper : BaseMapper<Message> {
    
    /**
     * 根据消息ID查找消息
     */
    @Select("SELECT * FROM messages WHERE message_id = #{messageId} AND deleted = false")
    fun findByMessageId(@Param("messageId") messageId: String): Message?
    
    /**
     * 分页查询会话消息
     */
    @Select("""
        SELECT * FROM messages 
        WHERE conversation_id = #{conversationId} AND deleted = false 
        ORDER BY created_at DESC
    """)
    fun findByConversationId(
        page: Page<Message>,
        @Param("conversationId") conversationId: String
    ): IPage<Message>
    
    /**
     * 查询会话消息（游标分页）
     */
    @Select("""
        <script>
        SELECT * FROM messages 
        WHERE conversation_id = #{conversationId} AND deleted = false
        <if test="cursor != null and cursor != ''">
            AND created_at &lt; (SELECT created_at FROM messages WHERE message_id = #{cursor})
        </if>
        ORDER BY created_at DESC
        LIMIT #{limit}
        </script>
    """)
    fun findByConversationIdWithCursor(
        @Param("conversationId") conversationId: String,
        @Param("cursor") cursor: String?,
        @Param("limit") limit: Int
    ): List<Message>
    
    /**
     * 查询用户在会话中的未读消息数
     */
    @Select("""
        SELECT COUNT(*) FROM messages m
        LEFT JOIN conversation_members cm ON m.conversation_id = cm.conversation_id
        WHERE m.conversation_id = #{conversationId} 
        AND cm.user_id = #{userId}
        AND m.deleted = false
        AND (cm.last_read_message_id IS NULL OR m.created_at > (
            SELECT created_at FROM messages WHERE message_id = cm.last_read_message_id
        ))
    """)
    fun countUnreadMessages(
        @Param("conversationId") conversationId: String,
        @Param("userId") userId: Long
    ): Int
    
    /**
     * 更新消息状态
     */
    @Update("""
        UPDATE messages 
        SET status = #{status}, updated_at = CURRENT_TIMESTAMP 
        WHERE message_id = #{messageId}
    """)
    fun updateMessageStatus(
        @Param("messageId") messageId: String,
        @Param("status") status: Int
    ): Int
    
    /**
     * 批量更新消息状态
     */
    @Update("""
        <script>
        UPDATE messages 
        SET status = #{status}, updated_at = CURRENT_TIMESTAMP 
        WHERE message_id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        </script>
    """)
    fun batchUpdateMessageStatus(
        @Param("messageIds") messageIds: List<String>,
        @Param("status") status: Int
    ): Int
    
    /**
     * 搜索消息
     */
    @Select("""
        <script>
        SELECT * FROM messages 
        WHERE deleted = false
        <if test="conversationId != null and conversationId != ''">
            AND conversation_id = #{conversationId}
        </if>
        <if test="senderId != null">
            AND sender_id = #{senderId}
        </if>
        <if test="messageType != null">
            AND message_type = #{messageType}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (content::text ILIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="startTime != null">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at <= #{endTime}
        </if>
        ORDER BY created_at DESC
        </script>
    """)
    fun searchMessages(
        page: Page<Message>,
        @Param("conversationId") conversationId: String?,
        @Param("senderId") senderId: Long?,
        @Param("messageType") messageType: Int?,
        @Param("keyword") keyword: String?,
        @Param("startTime") startTime: String?,
        @Param("endTime") endTime: String?
    ): IPage<Message>
    
    /**
     * 获取会话最后一条消息
     */
    @Select("""
        SELECT * FROM messages 
        WHERE conversation_id = #{conversationId} AND deleted = false 
        ORDER BY created_at DESC 
        LIMIT 1
    """)
    fun findLastMessageByConversationId(@Param("conversationId") conversationId: String): Message?
    
    /**
     * 删除会话所有消息（软删除）
     */
    @Update("""
        UPDATE messages 
        SET deleted = true, updated_at = CURRENT_TIMESTAMP 
        WHERE conversation_id = #{conversationId}
    """)
    fun deleteByConversationId(@Param("conversationId") conversationId: String): Int
}
