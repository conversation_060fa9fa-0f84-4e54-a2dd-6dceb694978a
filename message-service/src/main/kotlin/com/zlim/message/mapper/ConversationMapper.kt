package com.zlim.message.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.zlim.message.entity.Conversation
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * 会话数据访问层
 */
@Mapper
interface ConversationMapper : BaseMapper<Conversation> {
    
    /**
     * 根据会话ID查找会话
     */
    @Select("SELECT * FROM conversations WHERE conversation_id = #{conversationId} AND deleted = false")
    fun findByConversationId(@Param("conversationId") conversationId: String): Conversation?
    
    /**
     * 查询用户参与的会话列表
     */
    @Select("""
        SELECT c.* FROM conversations c
        INNER JOIN conversation_members cm ON c.conversation_id = cm.conversation_id
        WHERE cm.user_id = #{userId} AND c.deleted = false
        ORDER BY c.updated_at DESC
    """)
    fun findByUserId(page: Page<Conversation>, @Param("userId") userId: Long): IPage<Conversation>
    
    /**
     * 检查会话是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM conversations WHERE conversation_id = #{conversationId} AND deleted = false")
    fun existsByConversationId(@Param("conversationId") conversationId: String): Boolean
    
    /**
     * 根据类型和创建者查找会话
     */
    @Select("""
        SELECT * FROM conversations 
        WHERE type = #{type} AND created_by = #{createdBy} AND deleted = false
        ORDER BY created_at DESC
    """)
    fun findByTypeAndCreatedBy(
        @Param("type") type: Int,
        @Param("createdBy") createdBy: Long
    ): List<Conversation>
}
