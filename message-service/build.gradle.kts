dependencies {
    implementation(project(":common:common-core"))
    implementation(project(":common:common-proto"))
    implementation(project(":common:common-security"))
    implementation(project(":common:common-web"))
    
    // Spring Boot Web
    implementation("org.springframework.boot:spring-boot-starter-web") {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-tomcat")
    }
    implementation("org.springframework.boot:spring-boot-starter-undertow")
    
    // 数据库
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("com.baomidou:mybatis-plus-boot-starter:${property("mybatisPlusVersion")}")
    implementation("org.postgresql:postgresql")
    implementation("com.zaxxer:HikariCP")
    
    // Redis
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.redisson:redisson-spring-boot-starter:${property("redissonVersion")}")
    
    // RocketMQ
    implementation("org.apache.rocketmq:rocketmq-spring-boot-starter:${property("rocketmqVersion")}")
    
    // gRPC服务端
    implementation("net.devh:grpc-server-spring-boot-starter:3.1.0.RELEASE")
    
    // gRPC客户端（调用其他服务）
    implementation("net.devh:grpc-client-spring-boot-starter:3.1.0.RELEASE")
    
    // 全文搜索
    implementation("org.springframework.boot:spring-boot-starter-data-elasticsearch")
    
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("org.testcontainers:kafka")
}
