-- 消息表
CREATE TABLE messages (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) UNIQUE NOT NULL,
    conversation_id VARCHAR(100) NOT NULL,
    sender_id BIGINT NOT NULL,
    message_type SMALLINT NOT NULL, -- 1:文本 2:图片 3:音频 4:视频 5:文件 6:位置 7:系统
    content JSONB NOT NULL,
    status SMALLINT DEFAULT 1, -- 1:发送中 2:已发送 3:已送达 4:已读 5:失败
    reply_to_message_id VARCHAR(100), -- 回复的消息ID
    thread_id VARCHAR(100), -- 话题ID
    edited_at TIMESTAMP WITH TIME ZONE, -- 编辑时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 会话表
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(100) UNIQUE NOT NULL,
    type SMALLINT NOT NULL, -- 1:私聊 2:群聊 3:频道 4:系统会话
    title VARCHAR(200),
    avatar VARCHAR(500),
    description TEXT,
    settings JSONB,
    created_by BIGINT,
    last_message_id VARCHAR(100), -- 最后一条消息ID
    last_message_at TIMESTAMP WITH TIME ZONE, -- 最后消息时间
    message_count BIGINT DEFAULT 0, -- 消息总数
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 会话成员表
CREATE TABLE conversation_members (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(100) NOT NULL,
    user_id BIGINT NOT NULL,
    role SMALLINT DEFAULT 1, -- 1:普通成员 2:管理员 3:群主
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_read_message_id VARCHAR(100),
    last_read_at TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    muted BOOLEAN DEFAULT FALSE,
    pinned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(conversation_id, user_id)
);

-- 消息接收状态表（用于群聊消息的接收状态跟踪）
CREATE TABLE message_receipts (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL,
    user_id BIGINT NOT NULL,
    status SMALLINT NOT NULL, -- 2:已送达 4:已读
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id)
);

-- 消息反应表（点赞、表情等）
CREATE TABLE message_reactions (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL,
    user_id BIGINT NOT NULL,
    reaction_type VARCHAR(50) NOT NULL, -- like, love, laugh, angry, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id, reaction_type)
);

-- 会话邀请表
CREATE TABLE conversation_invites (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(100) NOT NULL,
    invite_code VARCHAR(50) UNIQUE NOT NULL,
    created_by BIGINT NOT NULL,
    max_uses INTEGER DEFAULT 1,
    used_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 消息草稿表
CREATE TABLE message_drafts (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(100) NOT NULL,
    user_id BIGINT NOT NULL,
    content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(conversation_id, user_id)
);

-- 创建索引
-- 消息表索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_status ON messages(status);
CREATE INDEX idx_messages_type ON messages(message_type);
CREATE INDEX idx_messages_reply_to ON messages(reply_to_message_id) WHERE reply_to_message_id IS NOT NULL;
CREATE INDEX idx_messages_thread_id ON messages(thread_id) WHERE thread_id IS NOT NULL;

-- 会话表索引
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_created_by ON conversations(created_by);
CREATE INDEX idx_conversations_last_message_at ON conversations(last_message_at);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);

-- 会话成员表索引
CREATE INDEX idx_conversation_members_user_id ON conversation_members(user_id);
CREATE INDEX idx_conversation_members_conversation_id ON conversation_members(conversation_id);
CREATE INDEX idx_conversation_members_role ON conversation_members(role);
CREATE INDEX idx_conversation_members_joined_at ON conversation_members(joined_at);

-- 消息接收状态表索引
CREATE INDEX idx_message_receipts_message_id ON message_receipts(message_id);
CREATE INDEX idx_message_receipts_user_id ON message_receipts(user_id);
CREATE INDEX idx_message_receipts_status ON message_receipts(status);

-- 消息反应表索引
CREATE INDEX idx_message_reactions_message_id ON message_reactions(message_id);
CREATE INDEX idx_message_reactions_user_id ON message_reactions(user_id);
CREATE INDEX idx_message_reactions_type ON message_reactions(reaction_type);

-- 会话邀请表索引
CREATE INDEX idx_conversation_invites_conversation_id ON conversation_invites(conversation_id);
CREATE INDEX idx_conversation_invites_created_by ON conversation_invites(created_by);
CREATE INDEX idx_conversation_invites_expires_at ON conversation_invites(expires_at);

-- 消息草稿表索引
CREATE INDEX idx_message_drafts_user_id ON message_drafts(user_id);

-- 全文搜索索引（用于消息内容搜索）
CREATE INDEX idx_messages_content_gin ON messages USING gin((content->>'text') gin_trgm_ops);

-- 创建更新时间触发器
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversation_members_updated_at BEFORE UPDATE ON conversation_members
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_message_drafts_updated_at BEFORE UPDATE ON message_drafts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建消息计数更新函数
CREATE OR REPLACE FUNCTION update_conversation_message_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE conversations 
        SET message_count = message_count + 1,
            last_message_id = NEW.message_id,
            last_message_at = NEW.created_at
        WHERE conversation_id = NEW.conversation_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE conversations 
        SET message_count = message_count - 1
        WHERE conversation_id = OLD.conversation_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- 创建消息计数触发器
CREATE TRIGGER update_conversation_message_count_trigger
    AFTER INSERT OR DELETE ON messages
    FOR EACH ROW EXECUTE FUNCTION update_conversation_message_count();
