#!/bin/bash

# 开发环境快速启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查并启动基础设施
start_infrastructure() {
    log_info "启动开发环境基础设施..."
    
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_warn "Docker未运行，请先启动Docker"
        exit 1
    fi
    
    # 启动基础设施服务
    docker compose up -d postgres redis nacos nacos-mysql rocketmq-nameserver rocketmq-broker
    
    log_info "等待服务启动..."
    sleep 20
    
    # 检查关键服务
    check_service "PostgreSQL" "localhost" "5432"
    check_service "Redis" "localhost" "6379"
    check_service "Nacos" "localhost" "8848"
    check_service "RocketMQ NameServer" "localhost" "9876"
    
    log_info "基础设施启动完成"
}

# 检查服务是否可用
check_service() {
    local name=$1
    local host=$2
    local port=$3
    local max_attempts=15
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            log_info "$name 服务已就绪"
            return 0
        fi
        log_debug "等待 $name 服务... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_warn "$name 服务可能未完全启动，但继续执行"
    return 0
}

# 构建项目
build_project() {
    log_info "构建项目..."
    ./gradlew build -x test --parallel
    log_info "项目构建完成"
}

# 启动核心服务
start_core_services() {
    log_info "启动核心微服务..."
    
    # 创建日志目录
    mkdir -p logs
    mkdir -p pids
    
    # 启动用户服务
    log_info "启动用户服务..."
    nohup ./gradlew :user-service:bootRun > logs/user-service.log 2>&1 &
    echo $! > pids/user-service.pid
    sleep 8
    
    # 启动网关服务
    log_info "启动网关服务..."
    nohup ./gradlew :gateway-service:bootRun > logs/gateway-service.log 2>&1 &
    echo $! > pids/gateway-service.pid
    sleep 8
    
    # 启动消息服务
    log_info "启动消息服务..."
    nohup ./gradlew :message-service:bootRun > logs/message-service.log 2>&1 &
    echo $! > pids/message-service.pid
    sleep 8
    
    log_info "核心服务启动完成"
}

# 显示服务信息
show_service_info() {
    echo ""
    echo "=========================================="
    echo "           ZLIM 开发环境已启动"
    echo "=========================================="
    echo ""
    echo "🌐 服务访问地址："
    echo "  • API网关:        http://localhost:8080"
    echo "  • 用户服务:        http://localhost:8081"
    echo "  • 消息服务:        http://localhost:8082"
    echo ""
    echo "🔧 管理控制台："
    echo "  • Nacos:          http://localhost:8848/nacos (nacos/nacos)"
    echo "  • RocketMQ:       http://localhost:8180"
    echo ""
    echo "📊 数据库连接："
    echo "  • PostgreSQL:     localhost:5432 (zlim/zlim123)"
    echo "  • Redis:          localhost:6379"
    echo ""
    echo "📝 API测试示例："
    echo "  # 用户注册"
    echo "  curl -X POST http://localhost:8080/api/v1/auth/register \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"username\":\"test\",\"password\":\"123456\",\"email\":\"<EMAIL>\"}'"
    echo ""
    echo "  # 用户登录"
    echo "  curl -X POST http://localhost:8080/api/v1/auth/login \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"identifier\":\"test\",\"password\":\"123456\"}'"
    echo ""
    echo "📋 常用命令："
    echo "  • 查看日志:        tail -f logs/user-service.log"
    echo "  • 停止服务:        ./scripts/dev-stop.sh"
    echo "  • 重启服务:        ./scripts/dev-restart.sh"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始启动ZLIM开发环境..."
    
    start_infrastructure
    build_project
    start_core_services
    show_service_info
    
    log_info "开发环境启动完成！"
}

# 执行主函数
main "$@"
