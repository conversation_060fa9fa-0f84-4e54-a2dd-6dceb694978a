-- 创建数据库
CREATE DATABASE zlim_user;
CREATE DATABASE zlim_message;
CREATE DATABASE zlim_social;
CREATE DATABASE zlim_media;
CREATE DATABASE zlim_admin;

-- 创建用户
CREATE USER zlim WITH PASSWORD 'zlim123';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE zlim_user TO zlim;
GRANT ALL PRIVILEGES ON DATABASE zlim_message TO zlim;
GRANT ALL PRIVILEGES ON DATABASE zlim_social TO zlim;
GRANT ALL PRIVILEGES ON DATABASE zlim_media TO zlim;
GRANT ALL PRIVILEGES ON DATABASE zlim_admin TO zlim;

-- 连接到用户数据库
\c zlim_user;

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    nickname VARCHAR(100),
    avatar VARCHAR(500),
    bio TEXT,
    status SMALLINT DEFAULT 1, -- 1:正常 2:禁用 3:删除
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 用户设备表
CREATE TABLE user_devices (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    device_id VARCHAR(100) NOT NULL,
    device_type VARCHAR(20) NOT NULL, -- ios, android, web, desktop
    app_version VARCHAR(20),
    os_version VARCHAR(50),
    push_token VARCHAR(500),
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, device_id)
);

-- 用户会话表
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    device_id VARCHAR(100),
    access_token VARCHAR(1000),
    refresh_token VARCHAR(1000),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 验证码表
CREATE TABLE verification_codes (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL, -- 邮箱或手机号
    code VARCHAR(10) NOT NULL,
    type VARCHAR(20) NOT NULL, -- register, login, reset_password
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_verification_codes_identifier ON verification_codes(identifier);

-- 连接到消息数据库
\c zlim_message;

-- 消息表
CREATE TABLE messages (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) UNIQUE NOT NULL,
    conversation_id VARCHAR(100) NOT NULL,
    sender_id BIGINT NOT NULL,
    message_type SMALLINT NOT NULL, -- 1:文本 2:图片 3:音频 4:视频 5:文件
    content JSONB NOT NULL,
    status SMALLINT DEFAULT 1, -- 1:发送中 2:已发送 3:已送达 4:已读 5:失败
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 会话表
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(100) UNIQUE NOT NULL,
    type SMALLINT NOT NULL, -- 1:私聊 2:群聊 3:频道
    title VARCHAR(200),
    avatar VARCHAR(500),
    description TEXT,
    settings JSONB,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 会话成员表
CREATE TABLE conversation_members (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(100) NOT NULL,
    user_id BIGINT NOT NULL,
    role SMALLINT DEFAULT 1, -- 1:普通成员 2:管理员 3:群主
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_message_id VARCHAR(100),
    muted BOOLEAN DEFAULT FALSE,
    pinned BOOLEAN DEFAULT FALSE,
    UNIQUE(conversation_id, user_id)
);

-- 创建索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversation_members_user_id ON conversation_members(user_id);
CREATE INDEX idx_conversation_members_conversation_id ON conversation_members(conversation_id);

-- 连接到社交数据库
\c zlim_social;

-- 好友关系表
CREATE TABLE friendships (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    friend_id BIGINT NOT NULL,
    status SMALLINT DEFAULT 1, -- 1:待确认 2:已同意 3:已拒绝 4:已删除
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, friend_id)
);

-- 群组表
CREATE TABLE groups (
    id BIGSERIAL PRIMARY KEY,
    group_id BIGINT UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    avatar VARCHAR(500),
    description TEXT,
    max_members INTEGER DEFAULT 500,
    join_mode SMALLINT DEFAULT 1, -- 1:自由加入 2:需要验证 3:禁止加入
    settings JSONB,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 群组成员表
CREATE TABLE group_members (
    id BIGSERIAL PRIMARY KEY,
    group_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role SMALLINT DEFAULT 1, -- 1:普通成员 2:管理员 3:群主
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(group_id, user_id)
);

-- 创建索引
CREATE INDEX idx_friendships_user_id ON friendships(user_id);
CREATE INDEX idx_friendships_friend_id ON friendships(friend_id);
CREATE INDEX idx_groups_created_by ON groups(created_by);
CREATE INDEX idx_group_members_group_id ON group_members(group_id);
CREATE INDEX idx_group_members_user_id ON group_members(user_id);
