# Redis配置文件

# 绑定地址
bind 0.0.0.0

# 端口
port 6379

# 后台运行
daemonize no

# 数据库数量
databases 16

# 持久化配置
save 900 1
save 300 10
save 60 10000

# RDB文件名
dbfilename dump.rdb

# 工作目录
dir /data

# 日志级别
loglevel notice

# 最大内存
maxmemory 1gb

# 内存淘汰策略
maxmemory-policy allkeys-lru

# 启用AOF
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端连接
maxclients 10000
timeout 300

# TCP keepalive
tcp-keepalive 300

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_9a8b7c6d5e4f"
