# RocketMQ Broker配置

# Broker集群名称
brokerClusterName = DefaultCluster

# Broker名称
brokerName = broker-a

# Broker ID (0表示Master，>0表示Slave)
brokerId = 0

# 删除文件时间点，默认凌晨4点
deleteWhen = 04

# 文件保留时间，默认72小时
fileReservedTime = 72

# Broker角色
brokerRole = ASYNC_MASTER

# 刷盘方式
flushDiskType = ASYNC_FLUSH

# 监听端口
listenPort = 10911

# NameServer地址
namesrvAddr = rocketmq-nameserver:9876

# 存储路径
storePathRootDir = /home/<USER>/store
storePathCommitLog = /home/<USER>/store/commitlog

# 自动创建Topic
autoCreateTopicEnable = true

# 自动创建订阅组
autoCreateSubscriptionGroup = true

# 发送消息线程池数量
sendMessageThreadPoolNums = 128

# 拉取消息线程池数量
pullMessageThreadPoolNums = 128

# 查询消息线程池数量
queryMessageThreadPoolNums = 8

# 管理Broker线程池数量
adminBrokerThreadPoolNums = 16

# 客户端管理线程池数量
clientManageThreadPoolNums = 32

# 消费管理线程池数量
consumerManageThreadPoolNums = 32

# 心跳线程池数量
heartbeatThreadPoolNums = 8

# 结束事务线程池数量
endTransactionThreadPoolNums = 8

# 磁盘使用率阈值
diskMaxUsedSpaceRatio = 88

# 开启字节缓冲区重用
transientStorePoolEnable = true

# 快速失败
fastFailIfNoBufferInStorePool = false

# 启用DLeger
enableDLegerCommitLog = false

# 消息索引安全
messageIndexSafe = false

# HA监听端口
haListenPort = 10912

# 访问消息内存比率
accessMessageInMemoryMaxRatio = 40
