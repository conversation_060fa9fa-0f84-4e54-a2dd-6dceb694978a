#!/bin/bash

# 开发环境停止脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 停止微服务
stop_microservices() {
    log_info "停止微服务..."
    
    if [ -d "pids" ]; then
        for pid_file in pids/*.pid; do
            if [ -f "$pid_file" ]; then
                service_name=$(basename "$pid_file" .pid)
                pid=$(cat "$pid_file")
                
                if kill -0 "$pid" 2>/dev/null; then
                    log_info "停止 $service_name (PID: $pid)"
                    kill "$pid"
                    
                    # 等待进程结束
                    local count=0
                    while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                        sleep 1
                        ((count++))
                    done
                    
                    # 如果进程仍在运行，强制杀死
                    if kill -0 "$pid" 2>/dev/null; then
                        log_warn "强制停止 $service_name"
                        kill -9 "$pid" 2>/dev/null || true
                    fi
                else
                    log_warn "$service_name 进程已不存在"
                fi
                
                rm -f "$pid_file"
            fi
        done
        
        # 清理PID目录
        rmdir pids 2>/dev/null || true
    else
        log_info "没有运行的微服务"
    fi
}

# 停止基础设施（可选）
stop_infrastructure() {
    local stop_infra=$1
    
    if [ "$stop_infra" = "true" ]; then
        log_info "停止基础设施服务..."
        docker-compose down
        log_info "基础设施服务已停止"
    else
        log_info "保留基础设施服务运行"
    fi
}

# 清理日志文件（可选）
clean_logs() {
    local clean=$1
    
    if [ "$clean" = "true" ]; then
        log_info "清理日志文件..."
        rm -rf logs/*.log
        log_info "日志文件已清理"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --infrastructure  同时停止基础设施服务"
    echo "  -c, --clean-logs      清理日志文件"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 仅停止微服务"
    echo "  $0 -i                 # 停止微服务和基础设施"
    echo "  $0 -i -c              # 停止所有服务并清理日志"
}

# 主函数
main() {
    local stop_infra=false
    local clean=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -i|--infrastructure)
                stop_infra=true
                shift
                ;;
            -c|--clean-logs)
                clean=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始停止ZLIM开发环境..."
    
    stop_microservices
    stop_infrastructure $stop_infra
    clean_logs $clean
    
    echo ""
    echo "=========================================="
    echo "         ZLIM 开发环境已停止"
    echo "=========================================="
    echo ""
    
    if [ "$stop_infra" = "false" ]; then
        echo "💡 提示: 基础设施服务仍在运行"
        echo "   如需完全停止，请使用: $0 -i"
    fi
    
    echo ""
    log_info "停止完成！"
}

# 执行主函数
main "$@"
