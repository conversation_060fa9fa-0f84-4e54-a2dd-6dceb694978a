#!/bin/bash

# ZLIM微服务部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装JDK 21"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 21 ]; then
        log_error "Java版本过低，需要JDK 21或更高版本"
        exit 1
    fi
    
    log_info "Java环境检查通过 (版本: $JAVA_VERSION)"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    # 清理之前的构建
    ./gradlew clean
    
    # 构建所有模块
    ./gradlew build -x test
    
    log_info "项目构建完成"
}

# 启动基础设施
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 创建网络
    docker network create zlim-network 2>/dev/null || true
    
    # 启动基础设施服务
    docker-compose up -d postgres redis nacos nacos-mysql rocketmq-nameserver rocketmq-broker minio elasticsearch
    
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_service_health "postgres" "5432"
    check_service_health "redis" "6379"
    check_service_health "nacos" "8848"
    check_service_health "rocketmq-nameserver" "9876"
    check_service_health "minio" "9000"
    check_service_health "elasticsearch" "9200"
    
    log_info "基础设施服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "检查 $service_name 服务状态..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            log_info "$service_name 服务已就绪"
            return 0
        fi
        
        log_debug "等待 $service_name 服务启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name 服务启动失败"
    return 1
}

# 启动微服务
start_microservices() {
    log_info "启动微服务..."
    
    # 按顺序启动服务
    services=("user-service" "gateway-service" "message-service" "social-service" "media-service" "push-service" "admin-service" "notification-service")
    
    for service in "${services[@]}"; do
        log_info "启动 $service..."
        
        # 在后台启动服务
        nohup ./gradlew :$service:bootRun > logs/$service.log 2>&1 &
        
        # 记录PID
        echo $! > pids/$service.pid
        
        # 等待服务启动
        sleep 10
        
        log_info "$service 启动完成"
    done
    
    log_info "所有微服务启动完成"
}

# 停止所有服务
stop_services() {
    log_info "停止所有服务..."
    
    # 停止微服务
    if [ -d "pids" ]; then
        for pid_file in pids/*.pid; do
            if [ -f "$pid_file" ]; then
                pid=$(cat "$pid_file")
                if kill -0 "$pid" 2>/dev/null; then
                    log_info "停止进程 $pid"
                    kill "$pid"
                fi
                rm -f "$pid_file"
            fi
        done
    fi
    
    # 停止Docker服务
    docker-compose down
    
    log_info "所有服务已停止"
}

# 查看服务状态
status_services() {
    log_info "服务状态检查..."
    
    echo "=== Docker服务状态 ==="
    docker-compose ps
    
    echo ""
    echo "=== 微服务状态 ==="
    if [ -d "pids" ]; then
        for pid_file in pids/*.pid; do
            if [ -f "$pid_file" ]; then
                service_name=$(basename "$pid_file" .pid)
                pid=$(cat "$pid_file")
                if kill -0 "$pid" 2>/dev/null; then
                    echo -e "${GREEN}$service_name${NC}: 运行中 (PID: $pid)"
                else
                    echo -e "${RED}$service_name${NC}: 已停止"
                fi
            fi
        done
    else
        echo "没有运行的微服务"
    fi
}

# 查看日志
view_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        log_error "请指定服务名称"
        echo "可用服务: user-service, gateway-service, message-service, social-service, media-service, push-service, admin-service, notification-service"
        exit 1
    fi
    
    if [ -f "logs/$service.log" ]; then
        tail -f "logs/$service.log"
    else
        log_error "日志文件不存在: logs/$service.log"
        exit 1
    fi
}

# 初始化目录
init_directories() {
    mkdir -p logs
    mkdir -p pids
}

# 主函数
main() {
    local command=$1
    
    case $command in
        "start")
            init_directories
            check_docker
            check_java
            build_project
            start_infrastructure
            start_microservices
            log_info "ZLIM系统启动完成！"
            log_info "访问地址："
            log_info "  - API网关: http://localhost:8080"
            log_info "  - Nacos控制台: http://localhost:8848/nacos"
            log_info "  - RocketMQ控制台: http://localhost:8180"
            log_info "  - MinIO控制台: http://localhost:9001"
            log_info "  - Kibana: http://localhost:5601"
            ;;
        "stop")
            stop_services
            ;;
        "status")
            status_services
            ;;
        "restart")
            stop_services
            sleep 5
            main "start"
            ;;
        "logs")
            view_logs $2
            ;;
        "build")
            check_java
            build_project
            ;;
        "infrastructure")
            check_docker
            start_infrastructure
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs|build|infrastructure}"
            echo ""
            echo "命令说明："
            echo "  start         - 启动完整系统"
            echo "  stop          - 停止所有服务"
            echo "  restart       - 重启系统"
            echo "  status        - 查看服务状态"
            echo "  logs <service> - 查看指定服务日志"
            echo "  build         - 构建项目"
            echo "  infrastructure - 仅启动基础设施"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
