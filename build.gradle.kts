import org.springframework.boot.gradle.tasks.bundling.BootJar

plugins {
    id("org.springframework.boot") version "3.3.2" apply false
    id("io.spring.dependency-management") version "1.1.6" apply false
    id("com.google.protobuf") version "0.9.4" apply false
    kotlin("jvm") version "1.9.24" apply false
    kotlin("plugin.spring") version "1.9.24" apply false
    kotlin("plugin.jpa") version "1.9.24" apply false
}

// 全局版本管理
extra["springCloudVersion"] = "2024.0.1"
extra["nacosVersion"] = "2024.0.1"
extra["protobufVersion"] = "3.25.3"
extra["grpcVersion"] = "1.63.0"
extra["mybatisPlusVersion"] = "3.5.7"
extra["redissonVersion"] = "3.32.0"
extra["rocketmqVersion"] = "5.3.0"
extra["jjwtVersion"] = "0.12.6"
extra["minioVersion"] = "8.5.11"
extra["nettyVersion"] = "4.1.110.Final"
extra["flywayVersion"] = "10.15.0"

allprojects {
    group = "com.zlim"
    version = "1.0.0"

    repositories {
        mavenCentral()
        maven { url = uri("https://repo.spring.io/milestone") }
        maven { url = uri("https://repo.spring.io/snapshot") }
    }
}

subprojects {
    apply(plugin = "java")
    apply(plugin = "org.jetbrains.kotlin.jvm")

    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        kotlinOptions {
            freeCompilerArgs = listOf("-Xjsr305=strict")
            jvmTarget = "21"
        }
    }

    dependencies {
        "implementation"("org.jetbrains.kotlin:kotlin-reflect:1.9.24")
        "implementation"("org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.24")
        "implementation"("com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2")
        "implementation"("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2")

        "testImplementation"("org.springframework.boot:spring-boot-starter-test:3.3.2")
        "testImplementation"("org.testcontainers:junit-jupiter:1.19.8")
        "testImplementation"("org.testcontainers:postgresql:1.19.8")
        "testImplementation"("org.testcontainers:kafka:1.19.8")
    }

    tasks.withType<Test> {
        useJUnitPlatform()
    }

    // 只对服务模块应用Spring Boot
    if (!project.name.startsWith("common")) {
        apply(plugin = "org.springframework.boot")
        apply(plugin = "io.spring.dependency-management")
        apply(plugin = "org.jetbrains.kotlin.plugin.spring")

        the<io.spring.gradle.dependencymanagement.dsl.DependencyManagementExtension>().apply {
            imports {
                mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
                mavenBom("com.alibaba.cloud:spring-cloud-alibaba-dependencies:${property("nacosVersion")}")
            }
        }

        dependencies {
            "implementation"("org.springframework.boot:spring-boot-starter")
            "implementation"("org.springframework.boot:spring-boot-starter-actuator")
            "implementation"("org.springframework.boot:spring-boot-starter-validation")

            // Nacos服务发现和配置
            "implementation"("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery")
            "implementation"("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config")

            // 监控和日志
            "implementation"("io.micrometer:micrometer-registry-prometheus")
            "implementation"("net.logstash.logback:logstash-logback-encoder:7.4")
        }
    } else {
        // 对于common模块，也需要依赖管理但不需要Spring Boot插件
        apply(plugin = "io.spring.dependency-management")

        the<io.spring.gradle.dependencymanagement.dsl.DependencyManagementExtension>().apply {
            imports {
                mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
                mavenBom("com.alibaba.cloud:spring-cloud-alibaba-dependencies:${property("nacosVersion")}")
                mavenBom("org.springframework.boot:spring-boot-dependencies:3.3.2")
            }
        }

        // 对于common模块，禁用bootJar任务
        tasks.whenTaskAdded {
            if (name == "bootJar") {
                enabled = false
            }
        }
        tasks.named<Jar>("jar") {
            enabled = true
            archiveClassifier = ""
        }
    }
}
