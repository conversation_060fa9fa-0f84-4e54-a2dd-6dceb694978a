# ZLIM 开发指南

## 开发环境搭建

### 环境要求

- **JDK 21+** - 推荐使用 OpenJDK 21
- **Docker & Docker Compose** - 用于运行基础设施服务
- **Gradle 8.x** - 构建工具（项目已包含wrapper）
- **IDE** - 推荐 IntelliJ IDEA 或 VS Code

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd zlim
```

2. **启动开发环境**
```bash
# 快速启动（推荐）
./scripts/dev-start.sh

# 或者手动启动
./scripts/deploy.sh infrastructure  # 启动基础设施
./gradlew build                     # 构建项目
./gradlew :user-service:bootRun     # 启动用户服务
./gradlew :gateway-service:bootRun  # 启动网关服务
```

3. **验证环境**
```bash
# 检查服务状态
curl http://localhost:8080/actuator/health

# 测试用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H 'Content-Type: application/json' \
  -d '{"username":"test","password":"123456","email":"<EMAIL>"}'
```

## 项目结构

```
zlim/
├── build.gradle.kts              # 根构建文件
├── settings.gradle.kts           # 项目设置
├── docker-compose.yml            # 开发环境容器编排
├── common/                       # 公共模块
│   ├── common-core/             # 核心工具类
│   ├── common-proto/            # Protocol Buffers定义
│   ├── common-security/         # 安全相关
│   └── common-web/              # Web相关
├── gateway-service/             # API网关服务
├── user-service/               # 用户管理服务
├── message-service/            # 消息核心服务
├── social-service/             # 社交关系服务
├── media-service/              # 媒体文件服务
├── push-service/               # 消息推送服务
├── admin-service/              # 管理后台服务
├── notification-service/       # 通知服务
├── scripts/                    # 部署脚本
└── docs/                       # 文档
```

## 开发规范

### 代码规范

1. **包命名规范**
```
com.zlim.{service}.{layer}.{module}

示例:
com.zlim.user.controller.AuthController
com.zlim.user.service.UserService
com.zlim.user.mapper.UserMapper
```

2. **类命名规范**
- Controller: `{功能}Controller`
- Service: `{功能}Service`
- Mapper: `{实体}Mapper`
- Entity: `{实体名}`
- DTO: `{功能}{操作}Request/Response`

3. **方法命名规范**
- 查询: `get{实体}`, `find{实体}`, `list{实体}`
- 创建: `create{实体}`, `add{实体}`
- 更新: `update{实体}`, `modify{实体}`
- 删除: `delete{实体}`, `remove{实体}`

### API设计规范

1. **RESTful API规范**
```
GET    /api/v1/users          # 获取用户列表
GET    /api/v1/users/{id}     # 获取指定用户
POST   /api/v1/users          # 创建用户
PUT    /api/v1/users/{id}     # 更新用户
DELETE /api/v1/users/{id}     # 删除用户
```

2. **响应格式规范**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1704067200
}
```

3. **错误码规范**
- 200: 成功
- 400: 业务错误
- 401: 认证失败
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

### 数据库设计规范

1. **表命名规范**
- 使用复数形式: `users`, `messages`, `conversations`
- 关联表: `user_devices`, `conversation_members`

2. **字段命名规范**
- 主键: `id`
- 外键: `{表名}_id`
- 时间字段: `created_at`, `updated_at`
- 软删除: `deleted`

3. **索引规范**
- 主键自动创建索引
- 外键字段创建索引
- 查询频繁的字段创建索引
- 复合索引按查询频率排序

## 开发流程

### 1. 功能开发流程

1. **创建分支**
```bash
git checkout -b feature/user-login
```

2. **编写代码**
- 先写测试用例
- 实现业务逻辑
- 编写API文档

3. **本地测试**
```bash
./gradlew test
./gradlew :user-service:bootRun
```

4. **提交代码**
```bash
git add .
git commit -m "feat: 实现用户登录功能"
git push origin feature/user-login
```

### 2. Protocol Buffers开发

1. **定义消息格式**
```protobuf
// common/common-proto/src/main/proto/user.proto
message LoginRequest {
  string identifier = 1;
  string password = 2;
  string login_type = 3;
}
```

2. **生成代码**
```bash
./gradlew :common:common-proto:generateProto
```

3. **使用生成的类**
```kotlin
val request = LoginRequest.newBuilder()
    .setIdentifier("test")
    .setPassword("123456")
    .setLoginType("password")
    .build()
```

### 3. gRPC服务开发

1. **定义服务接口**
```protobuf
service UserService {
  rpc Login(LoginRequest) returns (LoginResponse);
}
```

2. **实现服务端**
```kotlin
@GrpcService
class UserGrpcService : UserServiceGrpc.UserServiceImplBase() {
    override fun login(request: LoginRequest, responseObserver: StreamObserver<LoginResponse>) {
        // 实现登录逻辑
    }
}
```

3. **实现客户端**
```kotlin
@Component
class UserGrpcClient {
    @GrpcClient("user-service")
    private lateinit var userServiceStub: UserServiceGrpc.UserServiceBlockingStub
    
    fun login(request: LoginRequest): LoginResponse {
        return userServiceStub.login(request)
    }
}
```

## 测试指南

### 单元测试

```kotlin
@SpringBootTest
class UserServiceTest {
    
    @Autowired
    private lateinit var userService: UserService
    
    @Test
    fun `should create user successfully`() {
        // given
        val request = CreateUserRequest(...)
        
        // when
        val result = userService.createUser(request)
        
        // then
        assertThat(result.isSuccess()).isTrue()
    }
}
```

### 集成测试

```kotlin
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
class UserControllerIntegrationTest {
    
    @Container
    companion object {
        @JvmStatic
        val postgres = PostgreSQLContainer<Nothing>("postgres:16-alpine")
    }
    
    @Test
    fun `should register user via API`() {
        // 测试完整的API调用流程
    }
}
```

## 调试指南

### 1. 查看日志

```bash
# 查看特定服务日志
tail -f logs/user-service.log

# 查看所有服务日志
docker-compose logs -f
```

### 2. 连接数据库

```bash
# PostgreSQL
docker exec -it zlim-postgres psql -U zlim -d zlim_user

# Redis
docker exec -it zlim-redis redis-cli
```

### 3. 监控服务

- **Nacos控制台**: http://localhost:8848/nacos
- **RocketMQ控制台**: http://localhost:8180
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000

### 4. 性能分析

```bash
# JVM性能分析
jstack <pid>
jmap -dump:format=b,file=heap.hprof <pid>

# 应用指标
curl http://localhost:8081/actuator/metrics
```

## 常见问题

### 1. 服务启动失败

**问题**: 服务启动时报端口被占用
**解决**: 
```bash
# 查看端口占用
lsof -i :8081
# 杀死进程
kill -9 <pid>
```

### 2. 数据库连接失败

**问题**: 无法连接PostgreSQL
**解决**:
```bash
# 检查容器状态
docker-compose ps postgres
# 重启数据库
docker-compose restart postgres
```

### 3. gRPC调用失败

**问题**: gRPC服务调用超时
**解决**:
- 检查服务注册状态
- 验证网络连通性
- 查看服务日志

### 4. 构建失败

**问题**: Gradle构建失败
**解决**:
```bash
# 清理构建缓存
./gradlew clean
# 刷新依赖
./gradlew --refresh-dependencies
```

## 部署指南

### 开发环境部署

```bash
# 启动完整环境
./scripts/dev-start.sh

# 停止环境
./scripts/dev-stop.sh

# 重启环境
./scripts/dev-stop.sh && ./scripts/dev-start.sh
```

### 生产环境部署

```bash
# 构建Docker镜像
./gradlew bootBuildImage

# 部署到生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 贡献指南

1. **提交规范**
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

2. **代码审查**
- 确保测试通过
- 代码符合规范
- 功能完整可用
- 文档更新及时

3. **发布流程**
- 创建发布分支
- 更新版本号
- 生成变更日志
- 创建发布标签
