# 客户端国际化使用指南

## 概述

服务端返回的响应包含两个关键字段：
- `message`: 服务端根据用户语言环境本地化的消息
- `error_code`: 错误码，客户端可用此进行本地化

## 响应格式示例

### 成功响应
```json
{
  "success": true,
  "message": "注册成功",
  "user_id": 12345,
  "error_code": 200000
}
```

### 错误响应
```json
{
  "success": false,
  "message": "用户名已存在",
  "user_id": 0,
  "error_code": 401101
}
```

## 客户端处理方式

### 方式1：直接使用服务端消息
```javascript
// 直接显示服务端返回的message
function handleResponse(response) {
    if (response.success) {
        showSuccess(response.message); // "注册成功"
    } else {
        showError(response.message);   // "用户名已存在"
    }
}
```

### 方式2：客户端本地化
```javascript
// 客户端维护错误码映射表
const ERROR_MESSAGES = {
    200000: {
        'en': 'Success',
        'zh-CN': '成功',
        'zh-TW': '成功'
    },
    401101: {
        'en': 'Username already exists',
        'zh-CN': '用户名已存在',
        'zh-TW': '用戶名已存在'
    }
    // ... 更多错误码
};

function handleResponse(response) {
    const userLang = getUserLanguage(); // 获取用户语言偏好
    const localMessage = ERROR_MESSAGES[response.error_code]?.[userLang] || response.message;
    
    if (response.success) {
        showSuccess(localMessage);
    } else {
        showError(localMessage);
    }
}
```

### 方式3：混合模式
```javascript
// 优先使用客户端本地化，回退到服务端消息
function handleResponse(response) {
    const userLang = getUserLanguage();
    const clientMessage = ERROR_MESSAGES[response.error_code]?.[userLang];
    const finalMessage = clientMessage || response.message;
    
    if (response.success) {
        showSuccess(finalMessage);
    } else {
        showError(finalMessage);
    }
}
```

## 错误码分类

### 成功码
- `200000`: 通用成功

### 用户模块 (401xxx)
- `401001`: 用户不存在
- `401002`: 用户已存在
- `401101`: 用户名已存在
- `401201`: 邮箱已存在
- `401301`: 手机号已存在
- `401401`: 密码错误

### 认证模块 (402xxx)
- `402001`: 未授权
- `402101`: 令牌无效
- `402102`: 令牌过期

### 消息模块 (403xxx)
- `403001`: 消息不存在
- `403002`: 消息发送失败
- `403101`: 消息内容为空

### 会话模块 (404xxx)
- `404001`: 会话不存在
- `404201`: 会话成员不存在
- `404301`: 会话权限不足

## 最佳实践

### 1. 错误处理优先级
1. 客户端本地化消息（最佳用户体验）
2. 服务端本地化消息（备选方案）
3. 默认错误消息（兜底方案）

### 2. 错误码映射更新
- 定期从服务端获取最新的错误码映射
- 支持增量更新，减少网络传输
- 本地缓存映射表，提高响应速度

### 3. 语言切换
```javascript
// 用户切换语言时重新渲染界面
function switchLanguage(newLang) {
    setUserLanguage(newLang);
    // 重新渲染当前显示的错误消息
    refreshCurrentMessages();
}
```

### 4. 错误码处理
```javascript
// 根据错误码进行特殊处理
function handleErrorCode(errorCode) {
    switch (errorCode) {
        case 402101: // 令牌无效
        case 402102: // 令牌过期
            // 自动跳转到登录页
            redirectToLogin();
            break;
        case 401101: // 用户名已存在
            // 高亮用户名输入框
            highlightUsernameField();
            break;
        default:
            // 通用错误处理
            showGenericError();
    }
}
```

## 服务端语言检测

服务端支持多种方式检测用户语言偏好：

### 1. URL参数
```
GET /api/v1/users?lang=zh-CN
```

### 2. 自定义头部
```http
X-Language: zh-CN
```

### 3. 标准Accept-Language头
```http
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
```

优先级：URL参数 > X-Language头 > Accept-Language头 > 默认语言(en)
