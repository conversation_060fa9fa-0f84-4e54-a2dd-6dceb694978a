dependencies {
    implementation(project(":common:common-core"))
    implementation(project(":common:common-proto"))
    implementation(project(":common:common-security"))
    implementation(project(":common:common-web"))
    
    // Spring Boot Web
    implementation("org.springframework.boot:spring-boot-starter-web") {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-tomcat")
    }
    implementation("org.springframework.boot:spring-boot-starter-undertow")
    
    // 数据库
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("com.baomidou:mybatis-plus-boot-starter:${property("mybatisPlusVersion")}")
    implementation("org.postgresql:postgresql")
    implementation("com.zaxxer:HikariCP")
    
    // Redis
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.redisson:redisson-spring-boot-starter:${property("redissonVersion")}")
    
    // gRPC服务端
    implementation("net.devh:grpc-server-spring-boot-starter:3.1.0.RELEASE")
    
    // JWT
    implementation("io.jsonwebtoken:jjwt-api:${property("jjwtVersion")}")
    implementation("io.jsonwebtoken:jjwt-impl:${property("jjwtVersion")}")
    implementation("io.jsonwebtoken:jjwt-jackson:${property("jjwtVersion")}")
    
    // 密码加密
    implementation("org.springframework.security:spring-security-crypto")
    
    // 验证码
    implementation("com.github.penggle:kaptcha:2.3.2")
    
    // 邮件发送
    implementation("org.springframework.boot:spring-boot-starter-mail")
    
    // 短信发送 (阿里云)
    implementation("com.aliyun:dysmsapi20170525:3.0.0")
    
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.testcontainers:postgresql")
}
