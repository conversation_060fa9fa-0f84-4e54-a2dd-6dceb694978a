# User Module Error Messages (Chinese Simplified)

# User Success Messages
user.registration.success=注册成功
user.login.success=登录成功
user.logout.success=登出成功

# User Basic Errors
user.not.found=用户不存在
user.already.exists=用户已存在
user.disabled=用户账户已被禁用
user.locked=用户账户已被锁定
user.deleted=用户账户已被删除
user.not.activated=用户账户未激活
user.registration.failed=用户注册失败
user.update.failed=用户更新失败

# Username Errors
user.username.already.exists=用户名已存在
user.username.invalid=用户名格式无效
user.username.too.short=用户名太短
user.username.too.long=用户名太长
user.username.invalid.chars=用户名包含无效字符

# Email Errors
user.email.already.exists=邮箱已存在
user.email.invalid=邮箱格式无效
user.email.not.verified=邮箱未验证
user.email.verification.failed=邮箱验证失败
user.email.verification.expired=邮箱验证码已过期

# Phone Errors
user.phone.already.exists=手机号已存在
user.phone.invalid=手机号格式无效
user.phone.not.verified=手机号未验证
user.phone.verification.failed=手机号验证失败
user.phone.verification.expired=手机验证码已过期

# Password Errors
user.password.incorrect=密码错误
user.password.too.weak=密码强度太弱
user.password.too.short=密码太短
user.password.too.long=密码太长
user.password.missing.uppercase=密码必须包含大写字母
user.password.missing.lowercase=密码必须包含小写字母
user.password.missing.digit=密码必须包含数字
user.password.missing.special=密码必须包含特殊字符
user.old.password.incorrect=原密码错误
user.password.same.as.old=新密码不能与原密码相同
user.password.change.failed=密码修改失败

# Login Errors
user.login.failed=登录失败
user.login.identifier.invalid=登录标识无效
user.login.too.many.attempts=登录尝试次数过多
user.login.account.locked=账户因多次登录失败被锁定
user.login.device.not.allowed=不允许从此设备登录
user.login.location.not.allowed=不允许从此位置登录

# Device Errors
user.device.not.found=设备不存在
user.device.already.exists=设备已存在
user.device.limit.exceeded=设备数量超限
user.device.not.trusted=设备不受信任
user.device.registration.failed=设备注册失败

# Session Errors
user.session.not.found=会话不存在
user.session.expired=会话已过期
user.session.invalid=会话无效
user.session.create.failed=会话创建失败
user.session.limit.exceeded=会话数量超限

# Profile Errors
user.profile.update.failed=个人资料更新失败
user.avatar.upload.failed=头像上传失败
user.avatar.size.exceeded=头像大小超限
user.avatar.format.not.supported=头像格式不支持
user.nickname.already.exists=昵称已存在
user.nickname.invalid=昵称格式无效
user.bio.too.long=个人简介太长

# Verification Code Errors
user.verification.code.invalid=验证码无效
user.verification.code.expired=验证码已过期
user.verification.code.send.failed=验证码发送失败
user.verification.code.too.frequent=验证码发送过于频繁
user.verification.code.limit.exceeded=验证码数量超限

# Auth Module Error Messages

# Authentication Errors
auth.unauthorized=未授权
auth.authentication.failed=认证失败
auth.authentication.required=需要认证
auth.authentication.expired=认证已过期

# Token Errors
auth.token.invalid=令牌无效
auth.token.expired=令牌已过期
auth.token.malformed=令牌格式错误
auth.token.signature.invalid=令牌签名无效
auth.token.claims.empty=令牌声明为空
auth.token.issuer.invalid=令牌颁发者无效
auth.token.audience.invalid=令牌受众无效
auth.token.not.yet.valid=令牌尚未生效
auth.token.generation.failed=令牌生成失败
auth.token.blacklisted=令牌已被列入黑名单

# Refresh Token Errors
auth.refresh.token.invalid=刷新令牌无效
auth.refresh.token.expired=刷新令牌已过期
auth.refresh.token.not.found=刷新令牌不存在
auth.refresh.token.revoked=刷新令牌已被撤销
auth.refresh.token.limit.exceeded=刷新令牌数量超限

# Permission Errors
auth.permission.denied=权限被拒绝
auth.insufficient.privileges=权限不足
auth.role.not.found=角色不存在
auth.role.not.assigned=角色未分配给用户
auth.permission.not.found=权限不存在
auth.resource.access.denied=资源访问被拒绝
auth.operation.not.permitted=操作不被允许

# OAuth Errors
auth.oauth.provider.not.supported=不支持的OAuth提供商
auth.oauth.code.invalid=OAuth授权码无效
auth.oauth.state.mismatch=OAuth状态不匹配
auth.oauth.token.exchange.failed=OAuth令牌交换失败
auth.oauth.user.info.fetch.failed=获取OAuth用户信息失败
auth.oauth.account.linking.failed=OAuth账户关联失败

# MFA Errors
auth.mfa.required=需要多因素认证
auth.mfa.code.invalid=多因素认证码无效
auth.mfa.code.expired=多因素认证码已过期
auth.mfa.setup.required=需要设置多因素认证
auth.mfa.device.not.found=多因素认证设备不存在
auth.mfa.backup.code.invalid=多因素认证备用码无效

# Login/Logout Errors
auth.login.failed=登录失败
auth.login.blocked=登录被阻止
auth.login.rate.limited=登录频率受限
auth.logout.failed=登出失败
auth.logout.already.logged.out=已经登出

# Account Status Errors
auth.account.not.activated=账户未激活
auth.account.suspended=账户已暂停
auth.account.banned=账户已被封禁
auth.account.pending.verification=账户待验证
auth.account.requires.password.reset=账户需要重置密码

# API Key Errors
auth.api.key.invalid=API密钥无效
auth.api.key.expired=API密钥已过期
auth.api.key.revoked=API密钥已被撤销
auth.api.key.limit.exceeded=API密钥使用量超限
auth.api.key.scope.insufficient=API密钥权限不足

# Security Errors
auth.security.violation=检测到安全违规
auth.suspicious.activity=检测到可疑活动
auth.ip.blocked=IP地址被阻止
auth.geolocation.blocked=地理位置被阻止
auth.device.fingerprint.mismatch=设备指纹不匹配
