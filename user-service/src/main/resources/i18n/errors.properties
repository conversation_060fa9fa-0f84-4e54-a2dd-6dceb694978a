# User Module Error Messages (English)

# User Success Messages
user.registration.success=Registration successful
user.login.success=Login successful
user.logout.success=Logout successful

# User Basic Errors
user.not.found=User not found
user.already.exists=User already exists
user.disabled=User account is disabled
user.locked=User account is locked
user.deleted=User account has been deleted
user.not.activated=User account is not activated
user.registration.failed=User registration failed
user.update.failed=User update failed

# Username Errors
user.username.already.exists=Username already exists
user.username.invalid=Invalid username format
user.username.too.short=Username is too short
user.username.too.long=Username is too long
user.username.invalid.chars=Username contains invalid characters

# Email Errors
user.email.already.exists=Email already exists
user.email.invalid=Invalid email format
user.email.not.verified=Email is not verified
user.email.verification.failed=Email verification failed
user.email.verification.expired=Email verification code expired

# Phone Errors
user.phone.already.exists=Phone number already exists
user.phone.invalid=Invalid phone number format
user.phone.not.verified=Phone number is not verified
user.phone.verification.failed=Phone verification failed
user.phone.verification.expired=Phone verification code expired

# Password Errors
user.password.incorrect=Incorrect password
user.password.too.weak=Password is too weak
user.password.too.short=Password is too short
user.password.too.long=Password is too long
user.password.missing.uppercase=Password must contain uppercase letters
user.password.missing.lowercase=Password must contain lowercase letters
user.password.missing.digit=Password must contain digits
user.password.missing.special=Password must contain special characters
user.old.password.incorrect=Old password is incorrect
user.password.same.as.old=New password cannot be the same as old password
user.password.change.failed=Password change failed

# Login Errors
user.login.failed=Login failed
user.login.identifier.invalid=Invalid login identifier
user.login.too.many.attempts=Too many login attempts
user.login.account.locked=Account locked due to too many failed attempts
user.login.device.not.allowed=Login from this device is not allowed
user.login.location.not.allowed=Login from this location is not allowed

# Device Errors
user.device.not.found=Device not found
user.device.already.exists=Device already exists
user.device.limit.exceeded=Device limit exceeded
user.device.not.trusted=Device is not trusted
user.device.registration.failed=Device registration failed

# Session Errors
user.session.not.found=Session not found
user.session.expired=Session expired
user.session.invalid=Invalid session
user.session.create.failed=Session creation failed
user.session.limit.exceeded=Session limit exceeded

# Profile Errors
user.profile.update.failed=Profile update failed
user.avatar.upload.failed=Avatar upload failed
user.avatar.size.exceeded=Avatar size exceeded
user.avatar.format.not.supported=Avatar format not supported
user.nickname.already.exists=Nickname already exists
user.nickname.invalid=Invalid nickname format
user.bio.too.long=Bio is too long

# Verification Code Errors
user.verification.code.invalid=Invalid verification code
user.verification.code.expired=Verification code expired
user.verification.code.send.failed=Failed to send verification code
user.verification.code.too.frequent=Verification code sent too frequently
user.verification.code.limit.exceeded=Verification code limit exceeded

# Auth Module Error Messages

# Authentication Errors
auth.unauthorized=Unauthorized
auth.authentication.failed=Authentication failed
auth.authentication.required=Authentication required
auth.authentication.expired=Authentication expired

# Token Errors
auth.token.invalid=Invalid token
auth.token.expired=Token expired
auth.token.malformed=Malformed token
auth.token.signature.invalid=Invalid token signature
auth.token.claims.empty=Token claims are empty
auth.token.issuer.invalid=Invalid token issuer
auth.token.audience.invalid=Invalid token audience
auth.token.not.yet.valid=Token is not yet valid
auth.token.generation.failed=Token generation failed
auth.token.blacklisted=Token has been blacklisted

# Refresh Token Errors
auth.refresh.token.invalid=Invalid refresh token
auth.refresh.token.expired=Refresh token expired
auth.refresh.token.not.found=Refresh token not found
auth.refresh.token.revoked=Refresh token has been revoked
auth.refresh.token.limit.exceeded=Refresh token limit exceeded

# Permission Errors
auth.permission.denied=Permission denied
auth.insufficient.privileges=Insufficient privileges
auth.role.not.found=Role not found
auth.role.not.assigned=Role not assigned to user
auth.permission.not.found=Permission not found
auth.resource.access.denied=Resource access denied
auth.operation.not.permitted=Operation not permitted

# OAuth Errors
auth.oauth.provider.not.supported=OAuth provider not supported
auth.oauth.code.invalid=Invalid OAuth authorization code
auth.oauth.state.mismatch=OAuth state mismatch
auth.oauth.token.exchange.failed=OAuth token exchange failed
auth.oauth.user.info.fetch.failed=Failed to fetch OAuth user info
auth.oauth.account.linking.failed=OAuth account linking failed

# MFA Errors
auth.mfa.required=Multi-factor authentication required
auth.mfa.code.invalid=Invalid MFA code
auth.mfa.code.expired=MFA code expired
auth.mfa.setup.required=MFA setup required
auth.mfa.device.not.found=MFA device not found
auth.mfa.backup.code.invalid=Invalid MFA backup code

# Login/Logout Errors
auth.login.failed=Login failed
auth.login.blocked=Login blocked
auth.login.rate.limited=Login rate limited
auth.logout.failed=Logout failed
auth.logout.already.logged.out=Already logged out

# Account Status Errors
auth.account.not.activated=Account not activated
auth.account.suspended=Account suspended
auth.account.banned=Account banned
auth.account.pending.verification=Account pending verification
auth.account.requires.password.reset=Account requires password reset

# API Key Errors
auth.api.key.invalid=Invalid API key
auth.api.key.expired=API key expired
auth.api.key.revoked=API key revoked
auth.api.key.limit.exceeded=API key usage limit exceeded
auth.api.key.scope.insufficient=API key scope insufficient

# Security Errors
auth.security.violation=Security violation detected
auth.suspicious.activity=Suspicious activity detected
auth.ip.blocked=IP address blocked
auth.geolocation.blocked=Geolocation blocked
auth.device.fingerprint.mismatch=Device fingerprint mismatch
