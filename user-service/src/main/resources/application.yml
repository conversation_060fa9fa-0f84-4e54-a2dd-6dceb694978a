server:
  port: 8081

spring:
  application:
    name: user-service
  
  profiles:
    active: dev
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
  
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **************************************************************************************************************
    username: zlim
    password: zlim123
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: UserServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 1
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# gRPC服务端配置
grpc:
  server:
    port: 9091

# JWT配置
jwt:
  secret: zlim-jwt-secret-key-2024-very-long-and-secure
  access-token-expiration: 1800000  # 30分钟
  refresh-token-expiration: 604800000  # 7天

# 验证码配置
captcha:
  width: 120
  height: 40
  length: 4
  expiration: 300  # 5分钟

# 短信配置
sms:
  aliyun:
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    sign-name: 即时通讯
    template-code: SMS_123456789

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.zlim: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
