package com.zlim.user.entity

import com.baomidou.mybatisplus.annotation.*
import java.time.LocalDateTime

/**
 * 用户设备实体
 */
@TableName("user_devices")
data class UserDevice(
    @TableId(type = IdType.ASSIGN_ID)
    val id: Long? = null,
    
    @TableField("user_id")
    val userId: Long,
    
    @TableField("device_id")
    val deviceId: String,
    
    @TableField("device_type")
    val deviceType: String, // ios, android, web, desktop
    
    @TableField("app_version")
    val appVersion: String? = null,
    
    @TableField("os_version")
    val osVersion: String? = null,
    
    @TableField("push_token")
    val pushToken: String? = null,
    
    @TableField("last_login_at")
    val lastLoginAt: LocalDateTime? = null,
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    val createdAt: LocalDateTime? = null,
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    val updatedAt: LocalDateTime? = null
) {
    companion object {
        const val DEVICE_TYPE_IOS = "ios"
        const val DEVICE_TYPE_ANDROID = "android"
        const val DEVICE_TYPE_WEB = "web"
        const val DEVICE_TYPE_DESKTOP = "desktop"
    }
}
