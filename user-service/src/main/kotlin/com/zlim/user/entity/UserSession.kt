package com.zlim.user.entity

import com.baomidou.mybatisplus.annotation.*
import java.time.LocalDateTime

/**
 * 用户会话实体
 */
@TableName("user_sessions")
data class UserSession(
    @TableId(type = IdType.ASSIGN_ID)
    val id: Long? = null,
    
    @TableField("user_id")
    val userId: Long,
    
    @TableField("session_id")
    val sessionId: String,
    
    @TableField("device_id")
    val deviceId: String? = null,
    
    @TableField("access_token")
    val accessToken: String? = null,
    
    @TableField("refresh_token")
    val refreshToken: String? = null,
    
    @TableField("expires_at")
    val expiresAt: LocalDateTime? = null,
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    val createdAt: LocalDateTime? = null,
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    val updatedAt: LocalDateTime? = null
)
