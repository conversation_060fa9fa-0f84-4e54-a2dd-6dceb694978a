package com.zlim.user.entity

import com.baomidou.mybatisplus.annotation.*
import java.time.LocalDateTime

/**
 * 用户实体
 */
@TableName("users")
data class User(
    @TableId(type = IdType.ASSIGN_ID)
    val id: Long? = null,
    
    @TableField("username")
    val username: String,
    
    @TableField("password")
    val password: String,
    
    @TableField("email")
    val email: String? = null,
    
    @TableField("phone")
    val phone: String? = null,
    
    @TableField("nickname")
    val nickname: String? = null,
    
    @TableField("avatar")
    val avatar: String? = null,
    
    @TableField("bio")
    val bio: String? = null,
    
    @TableField("status")
    val status: Int = 1, // 1:正常 2:禁用 3:删除
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    val createdAt: LocalDateTime? = null,
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    val updatedAt: LocalDateTime? = null,
    
    @TableLogic
    @TableField("deleted")
    val deleted: Boolean = false
) {
    companion object {
        const val STATUS_ACTIVE = 1
        const val STATUS_INACTIVE = 2
        const val STATUS_BANNED = 3
        const val STATUS_DELETED = 4
    }
    
    /**
     * 是否为活跃用户
     */
    fun isActive(): Boolean = status == STATUS_ACTIVE
    
    /**
     * 是否被禁用
     */
    fun isBanned(): Boolean = status == STATUS_BANNED
}
