package com.zlim.user.enums

import com.zlim.common.core.enums.BaseErrorCode

/**
 * 认证授权模块错误码 (02xxxx)
 */
enum class AuthErrorCode(
    override val code: Int,
    override val messageKey: String,
    override val defaultMessage: String
) : BaseErrorCode {
    
    // ========== 认证基础错误 (020xxx) ==========
    UNAUTHORIZED(402001, "auth.unauthorized", "Unauthorized"),
    AUTHENTICATION_FAILED(402002, "auth.authentication.failed", "Authentication failed"),
    AUTHENTICATION_REQUIRED(402003, "auth.authentication.required", "Authentication required"),
    AUTHENTICATION_EXPIRED(402004, "auth.authentication.expired", "Authentication expired"),
    
    // ========== Token相关错误 (021xxx) ==========
    TOKEN_INVALID(402101, "auth.token.invalid", "Invalid token"),
    TOKEN_EXPIRED(402102, "auth.token.expired", "Token expired"),
    TOKEN_MALFORMED(402103, "auth.token.malformed", "Malformed token"),
    TOKEN_SIGNATURE_INVALID(402104, "auth.token.signature.invalid", "Invalid token signature"),
    TOKEN_CLAIMS_EMPTY(402105, "auth.token.claims.empty", "Token claims are empty"),
    TOKEN_ISSUER_INVALID(402106, "auth.token.issuer.invalid", "Invalid token issuer"),
    TOKEN_AUDIENCE_INVALID(402107, "auth.token.audience.invalid", "Invalid token audience"),
    TOKEN_NOT_YET_VALID(402108, "auth.token.not.yet.valid", "Token is not yet valid"),
    TOKEN_GENERATION_FAILED(402109, "auth.token.generation.failed", "Token generation failed"),
    TOKEN_BLACKLISTED(402110, "auth.token.blacklisted", "Token has been blacklisted"),
    
    // ========== Refresh Token相关错误 (022xxx) ==========
    REFRESH_TOKEN_INVALID(402201, "auth.refresh.token.invalid", "Invalid refresh token"),
    REFRESH_TOKEN_EXPIRED(402202, "auth.refresh.token.expired", "Refresh token expired"),
    REFRESH_TOKEN_NOT_FOUND(402203, "auth.refresh.token.not.found", "Refresh token not found"),
    REFRESH_TOKEN_REVOKED(402204, "auth.refresh.token.revoked", "Refresh token has been revoked"),
    REFRESH_TOKEN_LIMIT_EXCEEDED(402205, "auth.refresh.token.limit.exceeded", "Refresh token limit exceeded"),
    
    // ========== 权限相关错误 (023xxx) ==========
    PERMISSION_DENIED(402301, "auth.permission.denied", "Permission denied"),
    INSUFFICIENT_PRIVILEGES(402302, "auth.insufficient.privileges", "Insufficient privileges"),
    ROLE_NOT_FOUND(402303, "auth.role.not.found", "Role not found"),
    ROLE_NOT_ASSIGNED(402304, "auth.role.not.assigned", "Role not assigned to user"),
    PERMISSION_NOT_FOUND(402305, "auth.permission.not.found", "Permission not found"),
    RESOURCE_ACCESS_DENIED(402306, "auth.resource.access.denied", "Resource access denied"),
    OPERATION_NOT_PERMITTED(402307, "auth.operation.not.permitted", "Operation not permitted"),
    
    // ========== OAuth相关错误 (024xxx) ==========
    OAUTH_PROVIDER_NOT_SUPPORTED(402401, "auth.oauth.provider.not.supported", "OAuth provider not supported"),
    OAUTH_CODE_INVALID(402402, "auth.oauth.code.invalid", "Invalid OAuth authorization code"),
    OAUTH_STATE_MISMATCH(402403, "auth.oauth.state.mismatch", "OAuth state mismatch"),
    OAUTH_TOKEN_EXCHANGE_FAILED(402404, "auth.oauth.token.exchange.failed", "OAuth token exchange failed"),
    OAUTH_USER_INFO_FETCH_FAILED(402405, "auth.oauth.user.info.fetch.failed", "Failed to fetch OAuth user info"),
    OAUTH_ACCOUNT_LINKING_FAILED(402406, "auth.oauth.account.linking.failed", "OAuth account linking failed"),
    
    // ========== 多因素认证错误 (025xxx) ==========
    MFA_REQUIRED(402501, "auth.mfa.required", "Multi-factor authentication required"),
    MFA_CODE_INVALID(402502, "auth.mfa.code.invalid", "Invalid MFA code"),
    MFA_CODE_EXPIRED(402503, "auth.mfa.code.expired", "MFA code expired"),
    MFA_SETUP_REQUIRED(402504, "auth.mfa.setup.required", "MFA setup required"),
    MFA_DEVICE_NOT_FOUND(402505, "auth.mfa.device.not.found", "MFA device not found"),
    MFA_BACKUP_CODE_INVALID(402506, "auth.mfa.backup.code.invalid", "Invalid MFA backup code"),
    
    // ========== 登录/登出错误 (026xxx) ==========
    LOGIN_FAILED(402601, "auth.login.failed", "Login failed"),
    LOGIN_BLOCKED(402602, "auth.login.blocked", "Login blocked"),
    LOGIN_RATE_LIMITED(402603, "auth.login.rate.limited", "Login rate limited"),
    LOGOUT_FAILED(402604, "auth.logout.failed", "Logout failed"),
    LOGOUT_ALREADY_LOGGED_OUT(402605, "auth.logout.already.logged.out", "Already logged out"),
    
    // ========== 账户状态错误 (027xxx) ==========
    ACCOUNT_NOT_ACTIVATED(402701, "auth.account.not.activated", "Account not activated"),
    ACCOUNT_SUSPENDED(402702, "auth.account.suspended", "Account suspended"),
    ACCOUNT_BANNED(402703, "auth.account.banned", "Account banned"),
    ACCOUNT_PENDING_VERIFICATION(402704, "auth.account.pending.verification", "Account pending verification"),
    ACCOUNT_REQUIRES_PASSWORD_RESET(402705, "auth.account.requires.password.reset", "Account requires password reset"),
    
    // ========== API Key相关错误 (028xxx) ==========
    API_KEY_INVALID(402801, "auth.api.key.invalid", "Invalid API key"),
    API_KEY_EXPIRED(402802, "auth.api.key.expired", "API key expired"),
    API_KEY_REVOKED(402803, "auth.api.key.revoked", "API key revoked"),
    API_KEY_LIMIT_EXCEEDED(402804, "auth.api.key.limit.exceeded", "API key usage limit exceeded"),
    API_KEY_SCOPE_INSUFFICIENT(402805, "auth.api.key.scope.insufficient", "API key scope insufficient"),
    
    // ========== 安全相关错误 (029xxx) ==========
    SECURITY_VIOLATION(402901, "auth.security.violation", "Security violation detected"),
    SUSPICIOUS_ACTIVITY(402902, "auth.suspicious.activity", "Suspicious activity detected"),
    IP_BLOCKED(402903, "auth.ip.blocked", "IP address blocked"),
    GEOLOCATION_BLOCKED(402904, "auth.geolocation.blocked", "Geolocation blocked"),
    DEVICE_FINGERPRINT_MISMATCH(402905, "auth.device.fingerprint.mismatch", "Device fingerprint mismatch");
}
