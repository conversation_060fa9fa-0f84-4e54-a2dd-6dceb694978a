package com.zlim.user.enums

import com.zlim.common.core.enums.BaseErrorCode

/**
 * 用户模块错误码 (01xxxx)
 */
enum class UserErrorCode(
    override val code: Int,
    override val messageKey: String,
    override val defaultMessage: String
) : BaseErrorCode {
    
    // ========== 用户基础错误 (010xxx) ==========
    USER_NOT_FOUND(401001, "user.not.found", "User not found"),
    USER_ALREADY_EXISTS(401002, "user.already.exists", "User already exists"),
    USER_DISABLED(401003, "user.disabled", "User account is disabled"),
    USER_LOCKED(401004, "user.locked", "User account is locked"),
    USER_DELETED(401005, "user.deleted", "User account has been deleted"),
    USER_NOT_ACTIVATED(401006, "user.not.activated", "User account is not activated"),
    USER_REGISTRATION_FAILED(401007, "user.registration.failed", "User registration failed"),
    USER_UPDATE_FAILED(401008, "user.update.failed", "User update failed"),
    
    // ========== 用户名相关错误 (011xxx) ==========
    USERNAME_ALREADY_EXISTS(401101, "user.username.already.exists", "Username already exists"),
    USERNAME_INVALID(401102, "user.username.invalid", "Invalid username format"),
    USERNAME_TOO_SHORT(401103, "user.username.too.short", "Username is too short"),
    USERNAME_TOO_LONG(401104, "user.username.too.long", "Username is too long"),
    USERNAME_CONTAINS_INVALID_CHARS(401105, "user.username.invalid.chars", "Username contains invalid characters"),
    
    // ========== 邮箱相关错误 (012xxx) ==========
    EMAIL_ALREADY_EXISTS(401201, "user.email.already.exists", "Email already exists"),
    EMAIL_INVALID(401202, "user.email.invalid", "Invalid email format"),
    EMAIL_NOT_VERIFIED(401203, "user.email.not.verified", "Email is not verified"),
    EMAIL_VERIFICATION_FAILED(401204, "user.email.verification.failed", "Email verification failed"),
    EMAIL_VERIFICATION_EXPIRED(401205, "user.email.verification.expired", "Email verification code expired"),
    
    // ========== 手机号相关错误 (013xxx) ==========
    PHONE_ALREADY_EXISTS(401301, "user.phone.already.exists", "Phone number already exists"),
    PHONE_INVALID(401302, "user.phone.invalid", "Invalid phone number format"),
    PHONE_NOT_VERIFIED(401303, "user.phone.not.verified", "Phone number is not verified"),
    PHONE_VERIFICATION_FAILED(401304, "user.phone.verification.failed", "Phone verification failed"),
    PHONE_VERIFICATION_EXPIRED(401305, "user.phone.verification.expired", "Phone verification code expired"),
    
    // ========== 密码相关错误 (014xxx) ==========
    PASSWORD_INCORRECT(401401, "user.password.incorrect", "Incorrect password"),
    PASSWORD_TOO_WEAK(401402, "user.password.too.weak", "Password is too weak"),
    PASSWORD_TOO_SHORT(401403, "user.password.too.short", "Password is too short"),
    PASSWORD_TOO_LONG(401404, "user.password.too.long", "Password is too long"),
    PASSWORD_MISSING_UPPERCASE(401405, "user.password.missing.uppercase", "Password must contain uppercase letters"),
    PASSWORD_MISSING_LOWERCASE(401406, "user.password.missing.lowercase", "Password must contain lowercase letters"),
    PASSWORD_MISSING_DIGIT(401407, "user.password.missing.digit", "Password must contain digits"),
    PASSWORD_MISSING_SPECIAL_CHAR(401408, "user.password.missing.special", "Password must contain special characters"),
    OLD_PASSWORD_INCORRECT(401409, "user.old.password.incorrect", "Old password is incorrect"),
    PASSWORD_SAME_AS_OLD(401410, "user.password.same.as.old", "New password cannot be the same as old password"),
    PASSWORD_CHANGE_FAILED(401411, "user.password.change.failed", "Password change failed"),
    
    // ========== 登录相关错误 (015xxx) ==========
    LOGIN_FAILED(401501, "user.login.failed", "Login failed"),
    LOGIN_IDENTIFIER_INVALID(401502, "user.login.identifier.invalid", "Invalid login identifier"),
    LOGIN_TOO_MANY_ATTEMPTS(401503, "user.login.too.many.attempts", "Too many login attempts"),
    LOGIN_ACCOUNT_LOCKED(401504, "user.login.account.locked", "Account locked due to too many failed attempts"),
    LOGIN_DEVICE_NOT_ALLOWED(401505, "user.login.device.not.allowed", "Login from this device is not allowed"),
    LOGIN_LOCATION_NOT_ALLOWED(401506, "user.login.location.not.allowed", "Login from this location is not allowed"),
    
    // ========== 设备相关错误 (016xxx) ==========
    DEVICE_NOT_FOUND(401601, "user.device.not.found", "Device not found"),
    DEVICE_ALREADY_EXISTS(401602, "user.device.already.exists", "Device already exists"),
    DEVICE_LIMIT_EXCEEDED(401603, "user.device.limit.exceeded", "Device limit exceeded"),
    DEVICE_NOT_TRUSTED(401604, "user.device.not.trusted", "Device is not trusted"),
    DEVICE_REGISTRATION_FAILED(401605, "user.device.registration.failed", "Device registration failed"),
    
    // ========== 会话相关错误 (017xxx) ==========
    SESSION_NOT_FOUND(401701, "user.session.not.found", "Session not found"),
    SESSION_EXPIRED(401702, "user.session.expired", "Session expired"),
    SESSION_INVALID(401703, "user.session.invalid", "Invalid session"),
    SESSION_CREATE_FAILED(401704, "user.session.create.failed", "Session creation failed"),
    SESSION_LIMIT_EXCEEDED(401705, "user.session.limit.exceeded", "Session limit exceeded"),
    
    // ========== 个人资料相关错误 (018xxx) ==========
    PROFILE_UPDATE_FAILED(401801, "user.profile.update.failed", "Profile update failed"),
    AVATAR_UPLOAD_FAILED(401802, "user.avatar.upload.failed", "Avatar upload failed"),
    AVATAR_SIZE_EXCEEDED(401803, "user.avatar.size.exceeded", "Avatar size exceeded"),
    AVATAR_FORMAT_NOT_SUPPORTED(401804, "user.avatar.format.not.supported", "Avatar format not supported"),
    NICKNAME_ALREADY_EXISTS(401805, "user.nickname.already.exists", "Nickname already exists"),
    NICKNAME_INVALID(401806, "user.nickname.invalid", "Invalid nickname format"),
    BIO_TOO_LONG(401807, "user.bio.too.long", "Bio is too long"),
    
    // ========== 验证码相关错误 (019xxx) ==========
    VERIFICATION_CODE_INVALID(401901, "user.verification.code.invalid", "Invalid verification code"),
    VERIFICATION_CODE_EXPIRED(401902, "user.verification.code.expired", "Verification code expired"),
    VERIFICATION_CODE_SEND_FAILED(401903, "user.verification.code.send.failed", "Failed to send verification code"),
    VERIFICATION_CODE_TOO_FREQUENT(401904, "user.verification.code.too.frequent", "Verification code sent too frequently"),
    VERIFICATION_CODE_LIMIT_EXCEEDED(401905, "user.verification.code.limit.exceeded", "Verification code limit exceeded");
}
