package com.zlim.user.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zlim.user.entity.User
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * 用户数据访问层
 */
@Mapper
interface UserMapper : BaseMapper<User> {
    
    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = false")
    fun findByUsername(@Param("username") username: String): User?
    
    /**
     * 根据邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = false")
    fun findByEmail(@Param("email") email: String): User?
    
    /**
     * 根据手机号查找用户
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted = false")
    fun findByPhone(@Param("phone") phone: String): User?
    
    /**
     * 根据标识符查找用户（用户名、邮箱或手机号）
     */
    @Select("""
        SELECT * FROM users 
        WHERE (username = #{identifier} OR email = #{identifier} OR phone = #{identifier}) 
        AND deleted = false
    """)
    fun findByIdentifier(@Param("identifier") identifier: String): User?
    
    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username} AND deleted = false")
    fun existsByUsername(@Param("username") username: String): Boolean
    
    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email} AND deleted = false")
    fun existsByEmail(@Param("email") email: String): Boolean
    
    /**
     * 检查手机号是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE phone = #{phone} AND deleted = false")
    fun existsByPhone(@Param("phone") phone: String): Boolean
}
