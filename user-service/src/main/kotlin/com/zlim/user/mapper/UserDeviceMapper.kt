package com.zlim.user.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.zlim.user.entity.UserDevice
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update

/**
 * 用户设备数据访问层
 */
@Mapper
interface UserDeviceMapper : BaseMapper<UserDevice> {
    
    /**
     * 根据用户ID和设备ID查找设备
     */
    @Select("SELECT * FROM user_devices WHERE user_id = #{userId} AND device_id = #{deviceId}")
    fun findByUserIdAndDeviceId(@Param("userId") userId: Long, @Param("deviceId") deviceId: String): UserDevice?
    
    /**
     * 根据用户ID查找所有设备
     */
    @Select("SELECT * FROM user_devices WHERE user_id = #{userId} ORDER BY last_login_at DESC")
    fun findByUserId(@Param("userId") userId: Long): List<UserDevice>
    
    /**
     * 更新设备推送令牌
     */
    @Update("""
        UPDATE user_devices 
        SET push_token = #{pushToken}, updated_at = CURRENT_TIMESTAMP 
        WHERE user_id = #{userId} AND device_id = #{deviceId}
    """)
    fun updatePushToken(
        @Param("userId") userId: Long, 
        @Param("deviceId") deviceId: String, 
        @Param("pushToken") pushToken: String
    ): Int
    
    /**
     * 更新最后登录时间
     */
    @Update("""
        UPDATE user_devices 
        SET last_login_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
        WHERE user_id = #{userId} AND device_id = #{deviceId}
    """)
    fun updateLastLoginTime(@Param("userId") userId: Long, @Param("deviceId") deviceId: String): Int
}
