package com.zlim.user.controller

import com.zlim.common.proto.UserInfo
import com.zlim.user.proto.*
import com.zlim.user.service.UserService
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

/**
 * 用户控制器 - 使用Protobuf格式
 */
@RestController
@RequestMapping("/users")
class UserController(
    private val userService: UserService
) {

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me", produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun getCurrentUser(@RequestHeader("X-User-Id") userId: Long): GetUserInfoResponse {
        val userInfo = userService.getUserInfo(userId)
        return GetUserInfoResponse.newBuilder()
            .setUserInfo(com.zlim.common.proto.UserInfo.newBuilder()
                .setUserId(userInfo.userId)
                .setUsername(userInfo.username)
                .setNickname(userInfo.nickname ?: "")
                .setAvatar(userInfo.avatar ?: "")
                .setEmail(userInfo.email ?: "")
                .setPhone(userInfo.phone ?: "")
                .setStatus(com.zlim.common.proto.UserStatus.forNumber(userInfo.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
                .setCreatedAt(System.currentTimeMillis())
                .build())
            .build()
    }

    /**
     * 获取指定用户信息
     */
    @GetMapping("/{userId}", produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun getUserInfo(@PathVariable userId: Long): GetUserInfoResponse {
        val userInfo = userService.getUserInfo(userId)
        return GetUserInfoResponse.newBuilder()
            .setUserInfo(com.zlim.common.proto.UserInfo.newBuilder()
                .setUserId(userInfo.userId)
                .setUsername(userInfo.username)
                .setNickname(userInfo.nickname ?: "")
                .setAvatar(userInfo.avatar ?: "")
                .setEmail(userInfo.email ?: "")
                .setPhone(userInfo.phone ?: "")
                .setStatus(com.zlim.common.proto.UserStatus.forNumber(userInfo.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
                .setCreatedAt(System.currentTimeMillis())
                .build())
            .build()
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/me",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun updateUserInfo(
        @RequestHeader("X-User-Id") userId: Long,
        @RequestBody request: UpdateUserInfoRequest
    ): UpdateUserInfoResponse {
        val updateRequest = com.zlim.user.dto.UpdateUserInfoRequest(
            nickname = if (request.nickname.isBlank()) null else request.nickname,
            avatar = if (request.avatar.isBlank()) null else request.avatar,
            bio = if (request.bio.isBlank()) null else request.bio
        )
        val result = userService.updateUserInfo(userId, updateRequest)
        return UpdateUserInfoResponse.newBuilder()
            .setSuccess(result)
            .setMessage(if (result) "更新成功" else "更新失败")
            .build()
    }

    /**
     * 批量获取用户信息
     */
    @PostMapping("/batch",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun batchGetUserInfo(@RequestBody request: BatchGetUserInfoRequest): BatchGetUserInfoResponse {
        val userInfos = userService.batchGetUserInfo(request.userIdsList)
        val protoUserInfos = userInfos.map { userInfo ->
            com.zlim.common.proto.UserInfo.newBuilder()
                .setUserId(userInfo.userId)
                .setUsername(userInfo.username)
                .setNickname(userInfo.nickname ?: "")
                .setAvatar(userInfo.avatar ?: "")
                .setEmail(userInfo.email ?: "")
                .setPhone(userInfo.phone ?: "")
                .setStatus(com.zlim.common.proto.UserStatus.forNumber(userInfo.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
                .setCreatedAt(System.currentTimeMillis())
                .build()
        }
        return BatchGetUserInfoResponse.newBuilder()
            .addAllUserInfos(protoUserInfos)
            .build()
    }
}
