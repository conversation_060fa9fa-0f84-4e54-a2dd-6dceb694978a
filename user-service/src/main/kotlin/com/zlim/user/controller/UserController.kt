package com.zlim.user.controller

import com.zlim.common.proto.UserInfo
import com.zlim.user.proto.*
import com.zlim.user.service.UserService
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

/**
 * 用户控制器 - 使用Protobuf格式
 */
@RestController
@RequestMapping("/users")
class UserController(
    private val userService: UserService
) {

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me", produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun getCurrentUser(@RequestHeader("X-User-Id") userId: Long): GetUserInfoResponse {
        return userService.getUserInfo(GetUserInfoRequest.newBuilder().setUserId(userId).build())
    }

    /**
     * 获取指定用户信息
     */
    @GetMapping("/{userId}", produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun getUserInfo(@PathVariable userId: Long): GetUserInfoResponse {
        return userService.getUserInfo(GetUserInfoRequest.newBuilder().setUserId(userId).build())
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/me",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun updateUserInfo(
        @RequestHeader("X-User-Id") userId: Long,
        @RequestBody request: UpdateUserInfoRequest
    ): UpdateUserInfoResponse {
        val updateRequest = request.toBuilder().setUserId(userId).build()
        return userService.updateUserInfo(updateRequest)
    }

    /**
     * 批量获取用户信息
     */
    @PostMapping("/batch",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun batchGetUserInfo(@RequestBody request: BatchGetUserInfoRequest): BatchGetUserInfoResponse {
        return userService.batchGetUserInfo(request)
    }
}
