package com.zlim.user.controller

import com.zlim.common.core.result.Result
import com.zlim.user.proto.*
import com.zlim.user.service.UserService
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

/**
 * 认证控制器 - 使用Protobuf格式
 */
@RestController
@RequestMapping("/auth")
class AuthController(
    private val userService: UserService
) {

    /**
     * 用户注册
     */
    @PostMapping("/register",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun register(@RequestBody request: RegisterRequest): RegisterResponse {
        return userService.register(request)
    }

    /**
     * 用户登录
     */
    @PostMapping("/login",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun login(@RequestBody request: LoginRequest): LoginResponse {
        return userService.login(request)
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun refreshToken(@RequestBody request: RefreshTokenRequest): RefreshTokenResponse {
        return userService.refreshToken(request)
    }

    /**
     * 验证令牌
     */
    @PostMapping("/verify",
        consumes = [MediaType.APPLICATION_PROTOBUF_VALUE],
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun verifyToken(@RequestBody request: VerifyTokenRequest): VerifyTokenResponse {
        return userService.verifyToken(request)
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout",
        produces = [MediaType.APPLICATION_PROTOBUF_VALUE])
    fun logout(
        @RequestHeader("Authorization") authorization: String,
        @RequestHeader("X-User-Id") userId: Long
    ): LogoutResponse {
        val accessToken = authorization.removePrefix("Bearer ")
        val request = LogoutRequest.newBuilder()
            .setUserId(userId)
            .setAccessToken(accessToken)
            .build()
        return userService.logout(request)
    }
}
