package com.zlim.user.config

import com.zlim.common.core.enums.ErrorCodeFactory
import com.zlim.user.enums.AuthErrorCode
import com.zlim.user.enums.UserErrorCode
import org.springframework.context.annotation.Configuration
import jakarta.annotation.PostConstruct

/**
 * 用户服务错误码注册配置
 */
@Configuration
class ErrorCodeConfig {
    
    @PostConstruct
    fun registerErrorCodes() {
        // 注册用户模块错误码
        ErrorCodeFactory.register(UserErrorCode.values())
        
        // 注册认证模块错误码
        ErrorCodeFactory.register(AuthErrorCode.values())
    }
}
