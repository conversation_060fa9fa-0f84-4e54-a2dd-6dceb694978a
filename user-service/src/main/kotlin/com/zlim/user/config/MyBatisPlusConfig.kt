package com.zlim.user.config

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor
import org.apache.ibatis.reflection.MetaObject
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.LocalDateTime

/**
 * MyBatis-Plus配置
 */
@Configuration
class MyBatisPlusConfig {
    
    /**
     * 分页插件
     */
    @Bean
    fun mybatisPlusInterceptor(): MybatisPlusInterceptor {
        val interceptor = MybatisPlusInterceptor()
        interceptor.addInnerInterceptor(PaginationInnerInterceptor())
        return interceptor
    }
    
    /**
     * 自动填充处理器
     */
    @Bean
    fun metaObjectHandler(): MetaObjectHandler {
        return object : MetaObjectHandler {
            override fun insertFill(metaObject: MetaObject) {
                val now = LocalDateTime.now()
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime::class.java, now)
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime::class.java, now)
            }
            
            override fun updateFill(metaObject: MetaObject) {
                val now = LocalDateTime.now()
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime::class.java, now)
            }
        }
    }
}
