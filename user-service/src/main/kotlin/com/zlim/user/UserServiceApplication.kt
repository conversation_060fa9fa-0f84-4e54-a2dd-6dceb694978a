package com.zlim.user

import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cloud.client.discovery.EnableDiscoveryClient

/**
 * 用户服务启动类
 */
@SpringBootApplication(scanBasePackages = ["com.zlim"])
@EnableDiscoveryClient
@MapperScan("com.zlim.user.mapper")
class UserServiceApplication

fun main(args: Array<String>) {
    runApplication<UserServiceApplication>(*args)
}
