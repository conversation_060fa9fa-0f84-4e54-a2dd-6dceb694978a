package com.zlim.user.service

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.zlim.common.core.exception.BusinessException
import com.zlim.common.core.exception.NotFoundException
import com.zlim.common.security.jwt.JwtTokenProvider
import com.zlim.common.core.util.ProtoResponseBuilder
import com.zlim.common.core.util.withError
import com.zlim.common.core.util.withSuccess
import com.zlim.common.proto.DeviceInfo
import com.zlim.common.proto.UserInfo
import com.zlim.common.proto.UserStatus
import com.zlim.user.proto.*
import com.zlim.user.entity.User
import com.zlim.user.entity.UserDevice
import com.zlim.user.dto.*
import com.zlim.user.mapper.UserDeviceMapper
import com.zlim.user.mapper.UserMapper
import com.zlim.user.enums.UserErrorCode
import com.zlim.user.enums.AuthErrorCode
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 用户服务
 */
@Service
class UserService(
    private val userMapper: UserMapper,
    private val userDeviceMapper: UserDeviceMapper,
    private val passwordEncoder: PasswordEncoder,
    private val jwtTokenProvider: JwtTokenProvider
) {
    
    /**
     * 用户注册
     */
    @Transactional
    fun register(request: com.zlim.user.dto.RegisterRequest): com.zlim.user.dto.RegisterResponse {
        // 验证用户名是否已存在
        if (userMapper.existsByUsername(request.username)) {
            return com.zlim.user.dto.RegisterResponse(
                success = false,
                message = "用户名已存在"
            )
        }

        // 验证邮箱是否已存在
        if (!request.email.isNullOrBlank() && userMapper.existsByEmail(request.email)) {
            return com.zlim.user.dto.RegisterResponse(
                success = false,
                message = "邮箱已存在"
            )
        }

        // 验证手机号是否已存在
        if (!request.phone.isNullOrBlank() && userMapper.existsByPhone(request.phone)) {
            return com.zlim.user.dto.RegisterResponse(
                success = false,
                message = "手机号已存在"
            )
        }

        // 创建用户
        val user = User(
            username = request.username,
            password = passwordEncoder.encode(request.password),
            email = if (request.email.isBlank()) null else request.email,
            phone = if (request.phone.isBlank()) null else request.phone,
            nickname = if (request.nickname.isBlank()) request.username else request.nickname,
            status = User.STATUS_ACTIVE
        )

        val result = userMapper.insert(user)
        if (result <= 0) {
            return com.zlim.user.dto.RegisterResponse(
                success = false,
                message = "用户注册失败"
            )
        }

        return com.zlim.user.dto.RegisterResponse(
            success = true,
            message = "注册成功",
            userId = user.id!!
        )
    }
    
    /**
     * 用户登录
     */
    @Transactional
    fun login(request: com.zlim.user.dto.LoginRequest): com.zlim.user.dto.LoginResponse {
        // 查找用户
        val user = userMapper.findByIdentifier(request.identifier)
            ?: return com.zlim.user.dto.LoginResponse(
                success = false,
                message = "用户不存在"
            )

        // 检查用户状态
        if (!user.isActive()) {
            return LoginResponse.newBuilder()
                .withError(UserErrorCode.USER_DISABLED)
                .build()
        }

        // 验证密码
        if (!passwordEncoder.matches(request.password, user.password)) {
            return LoginResponse.newBuilder()
                .withError(UserErrorCode.PASSWORD_INCORRECT)
                .build()
        }

        // 处理设备信息
        if (request.hasDevice()) {
            saveOrUpdateDevice(user.id!!, request.device)
        }

        // 生成令牌
        val accessToken = jwtTokenProvider.generateAccessToken(user.id!!, user.username)
        val refreshToken = jwtTokenProvider.generateRefreshToken(user.id)
        val expiresIn = jwtTokenProvider.getExpirationFromToken(accessToken)?.time ?: 0L

        return LoginResponse.newBuilder()
            .withSuccess("user.login.success")
            .setAccessToken(accessToken)
            .setRefreshToken(refreshToken)
            .setExpiresIn(expiresIn)
            .setUserInfo(convertToUserInfo(user))
            .build()
    }
    
    /**
     * 刷新令牌
     */
    fun refreshToken(request: RefreshTokenRequest): RefreshTokenResponse {
        // 验证刷新令牌
        if (!jwtTokenProvider.validateToken(request.refreshToken)) {
            throw BusinessException("刷新令牌无效")
        }
        
        val userId = jwtTokenProvider.getUserIdFromToken(request.refreshToken)
            ?: throw BusinessException("无法获取用户信息")
        
        // 查找用户
        val user = userMapper.selectById(userId)
            ?: throw NotFoundException("用户不存在")
        
        // 检查用户状态
        if (!user.isActive()) {
            throw BusinessException("用户已被禁用")
        }
        
        // 撤销旧的刷新令牌
        jwtTokenProvider.revokeRefreshToken(request.refreshToken)
        
        // 生成新的令牌
        val newAccessToken = jwtTokenProvider.generateAccessToken(user.id!!, user.username)
        val newRefreshToken = jwtTokenProvider.generateRefreshToken(user.id)
        val expiresIn = jwtTokenProvider.getExpirationFromToken(newAccessToken)?.time
        
        return com.zlim.user.proto.RefreshTokenResponse.newBuilder()
            .setSuccess(true)
            .setAccessToken(newAccessToken)
            .setRefreshToken(newRefreshToken)
            .apply { expiresIn?.let { setExpiresIn(it) } }
            .build()
    }
    
    /**
     * 验证令牌
     */
    fun verifyToken(request: VerifyTokenRequest): VerifyTokenResponse {
        // 检查令牌是否在黑名单中
        if (jwtTokenProvider.isTokenBlacklisted(request.accessToken)) {
            return com.zlim.user.proto.VerifyTokenResponse.newBuilder()
                .setValid(false)
                .build()
        }
        
        // 验证令牌
        if (!jwtTokenProvider.validateToken(request.accessToken)) {
            return com.zlim.user.proto.VerifyTokenResponse.newBuilder()
                .setValid(false)
                .build()
        }
        
        val userId = jwtTokenProvider.getUserIdFromToken(request.accessToken)
        val username = jwtTokenProvider.getUsernameFromToken(request.accessToken)
        val expiresAt = jwtTokenProvider.getExpirationFromToken(request.accessToken)?.time
        
        return com.zlim.user.proto.VerifyTokenResponse.newBuilder()
            .setValid(true)
            .apply {
                userId?.let { setUserId(it) }
                username?.let { setUsername(it) }
                expiresAt?.let { setExpiresAt(it) }
            }
            .build()
    }
    
    /**
     * 获取用户信息
     */
    fun getUserInfo(userId: Long): UserInfoDto {
        val user = userMapper.selectById(userId)
            ?: throw NotFoundException("用户不存在")
        
        return convertToUserInfoDto(user)
    }
    
    /**
     * 更新用户信息
     */
    @Transactional
    fun updateUserInfo(userId: Long, request: UpdateUserInfoRequest): Boolean {
        val user = userMapper.selectById(userId)
            ?: throw NotFoundException("用户不存在")
        
        val updatedUser = user.copy(
            nickname = request.nickname ?: user.nickname,
            avatar = request.avatar ?: user.avatar,
            bio = request.bio ?: user.bio
        )
        
        return userMapper.updateById(updatedUser) > 0
    }
    
    /**
     * 修改密码
     */
    @Transactional
    fun changePassword(userId: Long, request: ChangePasswordRequest): Boolean {
        val user = userMapper.selectById(userId)
            ?: throw NotFoundException("用户不存在")
        
        // 验证原密码
        if (!passwordEncoder.matches(request.oldPassword, user.password)) {
            throw BusinessException("原密码错误")
        }
        
        // 更新密码
        val updatedUser = user.copy(
            password = passwordEncoder.encode(request.newPassword)
        )
        
        val result = userMapper.updateById(updatedUser) > 0
        
        if (result) {
            // 撤销所有刷新令牌，强制重新登录
            jwtTokenProvider.revokeAllRefreshTokens(userId)
        }
        
        return result
    }
    
    /**
     * 用户登出
     */
    fun logout(request: LogoutRequest): LogoutResponse {
        return try {
            // 将访问令牌加入黑名单
            jwtTokenProvider.blacklistToken(request.accessToken)

            // 撤销所有刷新令牌
            jwtTokenProvider.revokeAllRefreshTokens(request.userId)

            LogoutResponse.newBuilder()
                .withSuccess("user.logout.success")
                .build()
        } catch (e: Exception) {
            LogoutResponse.newBuilder()
                .withError(AuthErrorCode.LOGOUT_FAILED)
                .build()
        }
    }
    
    /**
     * 批量获取用户信息
     */
    fun batchGetUserInfo(userIds: List<Long>): List<UserInfoDto> {
        val users = userMapper.selectBatchIds(userIds)
        return users.map { convertToUserInfoDto(it) }
    }
    
    /**
     * 保存或更新设备信息
     */
    private fun saveOrUpdateDevice(userId: Long, deviceInfo: DeviceInfoDto) {
        val existingDevice = userDeviceMapper.findByUserIdAndDeviceId(userId, deviceInfo.deviceId)
        
        if (existingDevice != null) {
            // 更新设备信息
            val updatedDevice = existingDevice.copy(
                deviceType = deviceInfo.deviceType,
                appVersion = deviceInfo.appVersion,
                osVersion = deviceInfo.osVersion,
                pushToken = deviceInfo.pushToken,
                lastLoginAt = LocalDateTime.now()
            )
            userDeviceMapper.updateById(updatedDevice)
        } else {
            // 创建新设备记录
            val newDevice = UserDevice(
                userId = userId,
                deviceId = deviceInfo.deviceId,
                deviceType = deviceInfo.deviceType,
                appVersion = deviceInfo.appVersion,
                osVersion = deviceInfo.osVersion,
                pushToken = deviceInfo.pushToken,
                lastLoginAt = LocalDateTime.now()
            )
            userDeviceMapper.insert(newDevice)
        }
    }
    
    /**
     * 转换为用户信息DTO
     */
    private fun convertToUserInfoDto(user: User): UserInfoDto {
        return UserInfoDto(
            userId = user.id!!,
            username = user.username,
            nickname = user.nickname,
            avatar = user.avatar,
            email = user.email,
            phone = user.phone,
            status = user.status,
            createdAt = user.createdAt?.format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        )
    }

    /**
     * 转换为proto用户信息
     */
    private fun convertToUserInfo(user: User): com.zlim.common.proto.UserInfo {
        return com.zlim.common.proto.UserInfo.newBuilder()
            .setUserId(user.id!!)
            .setUsername(user.username)
            .setNickname(user.nickname ?: "")
            .setAvatar(user.avatar ?: "")
            .setEmail(user.email ?: "")
            .setPhone(user.phone ?: "")
            .setStatus(com.zlim.common.proto.UserStatus.forNumber(user.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
            .setCreatedAt(System.currentTimeMillis())
            .build()
    }
}
