package com.zlim.user.dto

import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size

/**
 * 用户注册请求
 */
data class RegisterRequest(
    @field:NotBlank(message = "用户名不能为空")
    @field:Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @field:Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    val username: String,
    
    @field:NotBlank(message = "密码不能为空")
    @field:Size(min = 6, max = 50, message = "密码长度必须在6-50个字符之间")
    val password: String,
    
    @field:Email(message = "邮箱格式不正确")
    val email: String? = null,
    
    @field:Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    val phone: String? = null,
    
    @field:Size(max = 100, message = "昵称长度不能超过100个字符")
    val nickname: String? = null,
    
    val verificationCode: String? = null
)

/**
 * 用户注册响应
 */
data class RegisterResponse(
    val success: Boolean,
    val message: String,
    val userId: Long? = null
)

/**
 * 用户登录请求
 */
data class LoginRequest(
    @field:NotBlank(message = "登录标识不能为空")
    val identifier: String, // 用户名、邮箱或手机号
    
    @field:NotBlank(message = "密码不能为空")
    val password: String,
    
    val loginType: String = "password", // password, sms, oauth
    
    val deviceInfo: DeviceInfoDto? = null
)

/**
 * 用户登录响应
 */
data class LoginResponse(
    val success: Boolean,
    val message: String,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val expiresIn: Long? = null,
    val userInfo: UserInfoDto? = null
)

/**
 * 刷新令牌请求
 */
data class RefreshTokenRequest(
    @field:NotBlank(message = "刷新令牌不能为空")
    val refreshToken: String
)

/**
 * 刷新令牌响应
 */
data class RefreshTokenResponse(
    val success: Boolean,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val expiresIn: Long? = null
)

/**
 * 设备信息DTO
 */
data class DeviceInfoDto(
    val deviceId: String,
    val deviceType: String, // ios, android, web, desktop
    val appVersion: String? = null,
    val osVersion: String? = null,
    val pushToken: String? = null
)

/**
 * 用户信息DTO
 */
data class UserInfoDto(
    val userId: Long,
    val username: String,
    val nickname: String? = null,
    val avatar: String? = null,
    val email: String? = null,
    val phone: String? = null,
    val status: Int,
    val createdAt: String? = null
)

/**
 * 更新用户信息请求
 */
data class UpdateUserInfoRequest(
    val nickname: String? = null,
    val avatar: String? = null,
    val bio: String? = null
)

/**
 * 修改密码请求
 */
data class ChangePasswordRequest(
    @field:NotBlank(message = "原密码不能为空")
    val oldPassword: String,
    
    @field:NotBlank(message = "新密码不能为空")
    @field:Size(min = 6, max = 50, message = "新密码长度必须在6-50个字符之间")
    val newPassword: String
)

/**
 * 验证令牌请求
 */
data class VerifyTokenRequest(
    @field:NotBlank(message = "访问令牌不能为空")
    val accessToken: String
)

/**
 * 验证令牌响应
 */
data class VerifyTokenResponse(
    val valid: Boolean,
    val userId: Long? = null,
    val username: String? = null,
    val expiresAt: Long? = null
)
