package com.zlim.user.grpc

import com.zlim.common.proto.DeviceInfo
import com.zlim.common.proto.UserInfo
import com.zlim.user.proto.*
import com.zlim.user.service.UserService
import com.zlim.user.dto.*
import io.grpc.stub.StreamObserver
import net.devh.boot.grpc.server.service.GrpcService
import org.slf4j.LoggerFactory

/**
 * 用户gRPC服务实现
 */
@GrpcService
class UserGrpcService(
    private val userService: UserService
) : UserServiceGrpc.UserServiceImplBase() {
    
    private val logger = LoggerFactory.getLogger(UserGrpcService::class.java)
    
    override fun register(request: com.zlim.user.proto.RegisterRequest, responseObserver: StreamObserver<com.zlim.user.proto.RegisterResponse>) {
        try {
            val registerRequest = com.zlim.user.dto.RegisterRequest(
                username = request.username,
                password = request.password,
                email = if (request.email.isBlank()) null else request.email,
                phone = if (request.phone.isBlank()) null else request.phone,
                nickname = if (request.nickname.isBlank()) null else request.nickname,
                verificationCode = if (request.verificationCode.isBlank()) null else request.verificationCode
            )
            
            val result = userService.register(registerRequest)
            
            val responseBuilder = com.zlim.user.proto.RegisterResponse.newBuilder()
                .setSuccess(result.success)
                .setMessage(result.message)

            result.userId?.let { responseBuilder.setUserId(it) }
            val response = responseBuilder.build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
            
        } catch (e: Exception) {
            logger.error("用户注册失败", e)
            val response = com.zlim.user.proto.RegisterResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.message ?: "注册失败")
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
        }
    }
    
    override fun login(request: com.zlim.user.proto.LoginRequest, responseObserver: StreamObserver<com.zlim.user.proto.LoginResponse>) {
        try {
            val deviceInfo = if (request.hasDevice()) {
                DeviceInfoDto(
                    deviceId = request.device.deviceId,
                    deviceType = request.device.deviceType,
                    appVersion = if (request.device.appVersion.isBlank()) null else request.device.appVersion,
                    osVersion = if (request.device.osVersion.isBlank()) null else request.device.osVersion,
                    pushToken = if (request.device.pushToken.isBlank()) null else request.device.pushToken
                )
            } else null
            
            val loginRequest = com.zlim.user.dto.LoginRequest(
                identifier = request.identifier,
                password = request.password,
                loginType = request.loginType,
                deviceInfo = deviceInfo
            )
            
            val result = userService.login(loginRequest)
            
            val responseBuilder = com.zlim.user.proto.LoginResponse.newBuilder()
                .setSuccess(result.success)
                .setMessage(result.message)
            
            result.accessToken?.let { responseBuilder.setAccessToken(it) }
            result.refreshToken?.let { responseBuilder.setRefreshToken(it) }
            result.expiresIn?.let { responseBuilder.setExpiresIn(it) }
            result.userInfo?.let { userInfo ->
                val protoUserInfo = com.zlim.common.proto.UserInfo.newBuilder()
                    .setUserId(userInfo.userId)
                    .setUsername(userInfo.username)
                    .setNickname(userInfo.nickname ?: "")
                    .setAvatar(userInfo.avatar ?: "")
                    .setEmail(userInfo.email ?: "")
                    .setPhone(userInfo.phone ?: "")
                    .setStatus(com.zlim.common.proto.UserStatus.forNumber(userInfo.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
                    .setCreatedAt(System.currentTimeMillis())
                    .setUpdatedAt(System.currentTimeMillis())
                    .build()
                responseBuilder.setUserInfo(protoUserInfo)
            }
            
            responseObserver.onNext(responseBuilder.build())
            responseObserver.onCompleted()
            
        } catch (e: Exception) {
            logger.error("用户登录失败", e)
            val response = com.zlim.user.proto.LoginResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.message ?: "登录失败")
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
        }
    }
    
    override fun refreshToken(request: com.zlim.user.proto.RefreshTokenRequest, responseObserver: StreamObserver<com.zlim.user.proto.RefreshTokenResponse>) {
        try {
            val refreshRequest = com.zlim.user.dto.RefreshTokenRequest(
                refreshToken = request.refreshToken
            )
            
            val result = userService.refreshToken(refreshRequest)
            
            val responseBuilder = com.zlim.user.proto.RefreshTokenResponse.newBuilder()
                .setSuccess(result.success)
            
            result.accessToken?.let { responseBuilder.setAccessToken(it) }
            result.refreshToken?.let { responseBuilder.setRefreshToken(it) }
            result.expiresIn?.let { responseBuilder.setExpiresIn(it) }
            
            responseObserver.onNext(responseBuilder.build())
            responseObserver.onCompleted()
            
        } catch (e: Exception) {
            logger.error("刷新令牌失败", e)
            val response = com.zlim.user.proto.RefreshTokenResponse.newBuilder()
                .setSuccess(false)
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
        }
    }
    
    override fun verifyToken(request: com.zlim.user.proto.VerifyTokenRequest, responseObserver: StreamObserver<com.zlim.user.proto.VerifyTokenResponse>) {
        try {
            val result = userService.verifyToken(request)
            responseObserver.onNext(result)
            responseObserver.onCompleted()

        } catch (e: Exception) {
            logger.error("验证令牌失败", e)
            val response = com.zlim.user.proto.VerifyTokenResponse.newBuilder()
                .setValid(false)
                .build()

            responseObserver.onNext(response)
            responseObserver.onCompleted()
        }
    }

    override fun logout(request: LogoutRequest, responseObserver: StreamObserver<LogoutResponse>) {
        try {
            val response = userService.logout(request)
            responseObserver.onNext(response)
            responseObserver.onCompleted()

        } catch (e: Exception) {
            logger.error("用户登出失败", e)
            val response = LogoutResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.message ?: "登出失败")
                .build()

            responseObserver.onNext(response)
            responseObserver.onCompleted()
        }
    }
    
    override fun getUserInfo(request: GetUserInfoRequest, responseObserver: StreamObserver<GetUserInfoResponse>) {
        try {
            val userInfo = userService.getUserInfo(request.userId)
            
            val protoUserInfo = UserInfo.newBuilder()
                .setUserId(userInfo.userId)
                .setUsername(userInfo.username)
                .setNickname(userInfo.nickname ?: "")
                .setAvatar(userInfo.avatar ?: "")
                .setEmail(userInfo.email ?: "")
                .setPhone(userInfo.phone ?: "")
                .setStatus(com.zlim.common.proto.UserStatus.forNumber(userInfo.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
                .setCreatedAt(System.currentTimeMillis())
                .setUpdatedAt(System.currentTimeMillis())
                .build()
            
            val response = GetUserInfoResponse.newBuilder()
                .setUserInfo(protoUserInfo)
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
            
        } catch (e: Exception) {
            logger.error("获取用户信息失败", e)
            responseObserver.onError(e)
        }
    }
    
    override fun updateUserInfo(request: com.zlim.user.proto.UpdateUserInfoRequest, responseObserver: StreamObserver<com.zlim.user.proto.UpdateUserInfoResponse>) {
        try {
            val updateRequest = com.zlim.user.dto.UpdateUserInfoRequest(
                nickname = if (request.nickname.isBlank()) null else request.nickname,
                avatar = if (request.avatar.isBlank()) null else request.avatar,
                bio = if (request.bio.isBlank()) null else request.bio
            )
            
            val result = userService.updateUserInfo(request.userId, updateRequest)
            
            val response = com.zlim.user.proto.UpdateUserInfoResponse.newBuilder()
                .setSuccess(result)
                .setMessage(if (result) "更新成功" else "更新失败")
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
            
        } catch (e: Exception) {
            logger.error("更新用户信息失败", e)
            val response = com.zlim.user.proto.UpdateUserInfoResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.message ?: "更新失败")
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
        }
    }
    
    override fun batchGetUserInfo(request: BatchGetUserInfoRequest, responseObserver: StreamObserver<BatchGetUserInfoResponse>) {
        try {
            val userInfos = userService.batchGetUserInfo(request.userIdsList)
            
            val protoUserInfos = userInfos.map { userInfo ->
                com.zlim.common.proto.UserInfo.newBuilder()
                    .setUserId(userInfo.userId)
                    .setUsername(userInfo.username)
                    .setNickname(userInfo.nickname ?: "")
                    .setAvatar(userInfo.avatar ?: "")
                    .setEmail(userInfo.email ?: "")
                    .setPhone(userInfo.phone ?: "")
                    .setStatus(com.zlim.common.proto.UserStatus.forNumber(userInfo.status) ?: com.zlim.common.proto.UserStatus.USER_STATUS_UNKNOWN)
                    .setCreatedAt(System.currentTimeMillis())
                    .setUpdatedAt(System.currentTimeMillis())
                    .build()
            }
            
            val response = BatchGetUserInfoResponse.newBuilder()
                .addAllUserInfos(protoUserInfos)
                .build()
            
            responseObserver.onNext(response)
            responseObserver.onCompleted()
            
        } catch (e: Exception) {
            logger.error("批量获取用户信息失败", e)
            responseObserver.onError(e)
        }
    }
}
