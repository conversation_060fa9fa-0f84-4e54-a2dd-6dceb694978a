version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:16-alpine
    container_name: zlim-postgres
    environment:
      POSTGRES_DB: zlim
      POSTGRES_USER: zlim
      POSTGRES_PASSWORD: zlim123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - zlim-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7.2-alpine
    container_name: zlim-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./scripts/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - zlim-network
    restart: unless-stopped

  # Nacos服务注册发现
  nacos:
    image: nacos/nacos-server:v2.3.2
    container_name: zlim-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: nacos-mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos123
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_logs:/home/<USER>/logs
    depends_on:
      - nacos-mysql
    networks:
      - zlim-network
    restart: unless-stopped

  # Nacos MySQL数据库
  nacos-mysql:
    image: mysql:8.0
    container_name: zlim-nacos-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: nacos
      MYSQL_USER: nacos
      MYSQL_PASSWORD: nacos123
    ports:
      - "3307:3306"
    volumes:
      - nacos_mysql_data:/var/lib/mysql
      - ./scripts/nacos-mysql-schema.sql:/docker-entrypoint-initdb.d/nacos-mysql-schema.sql
    networks:
      - zlim-network
    restart: unless-stopped

  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:5.3.0
    container_name: zlim-rocketmq-nameserver
    ports:
      - "9876:9876"
    volumes:
      - rocketmq_nameserver_logs:/home/<USER>/logs
    command: sh mqnamesrv
    networks:
      - zlim-network
    restart: unless-stopped

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:5.3.0
    container_name: zlim-rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    volumes:
      - rocketmq_broker_logs:/home/<USER>/logs
      - rocketmq_broker_store:/home/<USER>/store
      - ./scripts/broker.conf:/home/<USER>/rocketmq-5.3.0/conf/broker.conf
    command: sh mqbroker -n rocketmq-nameserver:9876 -c /home/<USER>/rocketmq-5.3.0/conf/broker.conf
    depends_on:
      - rocketmq-nameserver
    networks:
      - zlim-network
    restart: unless-stopped

  # RocketMQ Console
  rocketmq-console:
    image: styletang/rocketmq-console-ng:latest
    container_name: zlim-rocketmq-console
    ports:
      - "8180:8080"
    environment:
      JAVA_OPTS: -Drocketmq.namesrv.addr=rocketmq-nameserver:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    depends_on:
      - rocketmq-nameserver
    networks:
      - zlim-network
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: zlim-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - zlim-network
    restart: unless-stopped

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: zlim-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - zlim-network
    restart: unless-stopped

  # Kibana
  kibana:
    image: kibana:8.11.0
    container_name: zlim-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - zlim-network
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: zlim-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./scripts/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - zlim-network
    restart: unless-stopped

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: zlim-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - zlim-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  nacos_logs:
  nacos_mysql_data:
  rocketmq_nameserver_logs:
  rocketmq_broker_logs:
  rocketmq_broker_store:
  minio_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  zlim-network:
    driver: bridge
