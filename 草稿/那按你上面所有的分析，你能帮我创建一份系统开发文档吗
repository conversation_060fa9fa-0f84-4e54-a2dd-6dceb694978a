系统开发文档  
===============  
（面向聊天 / 社交类 App 的微服务后端）

0. 版本信息  
------------------------------------  
• 文档版本：v1.0.0  • 最后更新：2025-07-06  
• 适用代码分支：main  

1. 引言  
------------------------------------  
1.1 背景  
 满足 IM-即时通讯、好友关系、群聊、媒体上传、推送通知等功能需求；采用微服务 + 云原生架构，长连接走 Netty 网关，HTTP 走 API-Gateway。  

1.2 目标  
 给研发、测试、运维及安全团队提供「能落地」的完整蓝图：  
 • 描述服务拆分与职责边界  
 • 定义协议、数据库、CI/CD、监控、伸缩等关键细节  
 • 为后期扩展（直播、RTC、AI 推荐）留接口  

1.3 名词缩写  
 NIO：Non-blocking I/O  
 CID：Connection-ID，Netty 侧连接唯一标识  
 STS：Short-Time-Credential（临时上传凭证）  

2. 业务需求概要  
------------------------------------  
• 单聊 / 群聊（文本、表情、语音）  
• 好友&群管理、黑名单  
• 第三方登录 (OAuth)  
• 多端在线、漫游同步、离线推送  
• 图片 / 语音 / 视频上传下载（≤100 MB）  
• 基础运营后台：封号、关键词审核  
• QPS 峰值：10 k/s 长连接包；3 k/s HTTP；日活 200 w  

3. 总体架构  
------------------------------------  
3.1 分层视图  
 Client ⇆ 接入层 ⇆ 服务层 ⇆ 基础设施  

 ┌─────────────┐          ┌────────────┐  
 │  iOS / Android │<HTTP>──│HTTP API-GW │──┐  
 └─────────────┘          └────────────┘  │gRPC  
 ┌─────────────┐<WebSock>─┐                ▼  
 │Web / H5 / 小程序│        │      ┌────────────┐  
 └─────────────┘          │<TCP> │gateway-svc │  
                           │      └────────────┘  
                           ▼            │  
                     ┌────────────┐     │gRPC  
                     │message-svc │◀────┘  
                     └────────────┘ …

3.2 逻辑服务列表  
 1) gateway-service      (Netty, 长连接)  
 2) http-api-gateway    (Nginx/Kong/Spring Cloud Gateway)  
 3) auth-service        (授权、JWT)  
 4) user-service        (资料、关系)  
 5) social-service      (好友、群逻辑)  
 6) message-service    (消息收发、存储、同步)  
 7) media-service      (上传 STS、CDN 刷新)  
 8) push-service       (FCM/APNs/厂商)  
 9) admin-service      (运营后台、风控)  

3.3 数据流 & 时序（发送文本示例）  
 Client→gateway-svc (WebSocket Frame)  
 gateway-svc→message-svc (gRPC SendMsg)  
 message-svc →Redis Stream 写入  
 message-svc →gateway-svc (fan-out)  
 gateway-svc →目标在线端  
 message-svc →push-svc (离线终端)  

4. 组件详述  
------------------------------------  
4.1 gateway-service  
 • Tech：Java17 + Netty 4.1 + Protobuf  
 • 单节点 200k 长连接；EPOLL / KQUEUE  
 • 核心 Pipeline：  
  – IdleStateHandler（60s 心跳）  
  – LengthFieldBasedFrameDecoder  
  – ProtobufDecoder / Encoder  
  – AuthHandler（JWT 鉴权，绑定 CID）  
  – RouterHandler（查 Redis HASH: uid→node）  
  – AckHandler（重试、离线缓存）  
 • 水平扩容：基于一致性 Hash 选主；跨节点转发走 gRPC Stream  

4.2 http-api-gateway  
 • 统一域名 api.xxx.com  
 • WAF, Rate-Limit, OAuth2 Filter  
 • 每条路由输出 Zipkin Trace-ID → sidecar envoy  

4.3 message-service  
 • 存储：MySQL 分库分表 (msg_%Y%m)、Redis Stream 缓存 3d  
 • 幂等：msgId = senderUid + ts(ms) + random16  
 • 历史漫游：S3 + Parquet 冷归档  

4.4 media-service  
 • 生成 STS(10 min) 再由客户端直传 S3/OSS  
 • Callback → media-svc（校验 MD5、暴恐黄扫描）  
 • TinyPNG / FFMpeg 任务进入 Kafka topic media-compress  

…（其余服务同理，略）  

5. 数据设计  
------------------------------------  
5.1 核心表 (DDL 片段)  
CREATE TABLE `im_message_202507` (  
  `msg_id`   varchar(64) primary key,  
  `conv_id`  varchar(64) not null,  
  `sender`   bigint       not null,  
  `type`     tinyint      not null, -- 0=text 1=image 2=voice …  
  `body`     blob         not null, -- Protobuf 序列化  
  `ts`       bigint       not null,  
  key idx_conv_ts(`conv_id`,`ts`)  
) PARTITION BY HASH(conv_id) PARTITIONS 32;  

5.2 Redis Key 规范  
 online:{uid}             → CID@node  
 conv:unread:{uid}:{cid}  → int  
 token:blacklist          → BloomFilter  

6. 接口规范  
------------------------------------  
6.1 长连接协议 (Protobuf)  
message Envelope {  
  uint32 cmd = 1;   // 1=Ping 2=Pong 3=MsgSend 4=MsgRecvAck …  
  bytes  payload = 2;  
  uint32 seq = 3;  
}  

6.2 典型 HTTP API (OpenAPI 摘要)  
POST /v1/auth/login  
  Body: {code,provider} -> 200 {jwt,uid,expiresIn}  

GET /v1/messages/history?convId=xxx&cursor=xxx&limit=50  

6.3 gRPC – message.proto  
service MsgService {  
  rpc Send(SendReq) returns (SendAck);  
  rpc Sync(stream SyncReq) returns (stream SyncResp);  
}  

7. 安全设计  
------------------------------------  
• TLS 1.3 全链路；Netty 使用 OpenSSL-based tcnative  
• JWT (RS256)，过期 2h，Refresh-Token 7d  
• CSRF / XSS 由前端双 Cookie 防护  
• 数据库加密字段：手机、邮箱(AES-256-GCM)  
• 网关限速：/auth/login 20 req/min/IP  
• 敏感操作（二次密码/指纹）走 /v1/user/secure/**  

8. 运维与部署  
------------------------------------  
8.1 环境  
 dev → staging → prod（三套独立 K8s 集群）；镜像 alpine-jdk17-distroless  

8.2 节点规格 (初始)  
 gateway-svc     6 × 8C16G HPA: 60% CPU or 150k QPS  
 message-svc     4 × 8C16G  
 MySQL 主从      db.m6g.large (AWS)  

8.3 Observability  
 • Prometheus + Grafana（TCP backlog、eventLoop 延迟）  
 • Loki 日志；Trace → Tempo；报警 → PagerDuty  
 • 异常丢包率 >0.1% 或 P99 延迟 >400 ms 自动扩容  

8.4 灰度 / 熔断  
 • Istio VirtualService 按 uid%100 <10 灰度  
 • Hystrix/Governor 对下游超时 1 s 开断路  

9. 测试策略  
------------------------------------  
• 单元测试覆盖率 ≥80% (Jacoco)  
• 集成测试：docker-compose-e2e，包含模拟 Netty Client 50 k 并发  
• 性能压测：Locust + k6；目标 1 M 在线 300 k pps  
• Chaos Mesh 随机杀 Pod，验证重连 5 s 内恢复  

10. CI/CD 流程  
------------------------------------  
GitHub → Actions → Sonar → Buildx → Harbor → ArgoCD  
 • feature/* 触发单元+静态扫描  
 • release/* 触发 e2e + Canary  
 • tag v*.*.* 进入生产灰度 2% → 100%  

11. 迭代计划 (里程碑)  
------------------------------------  
M1：长连接、登录、单聊（2025-08）  
M2：群聊、历史漫游、媒体上传（2025-10）  
M3：运营后台、灰度发布、监控闭环（2025-12）  

12. 风险与缓解  
------------------------------------  
• 网关层雪崩 → 限速+熔断+快速失败  
• 数据倾斜 → 会话 hash+分区键调整  
• CDN 失效 → 多云冗余、客户端 fallback  

附录 A. 端口分配  
附录 B. Protobuf ID 映射表  
附录 C. SLA & SLO 定义  

—— END ——