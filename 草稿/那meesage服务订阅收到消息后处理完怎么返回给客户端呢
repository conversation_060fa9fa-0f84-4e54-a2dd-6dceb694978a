从 message-service 把“已经存盘的消息”送回在线客户端，可以分成 4 个连续环节：

1）message-service → Kafka（msg.down）  
2）push-service ← Kafka（msg.down）  
3）push-service → Netty-Gateway（gRPC Stream 或 HTTP/2 Stream）  
4）Netty-Gateway → WebSocket/TCP → 目标客户端  

下面把每一跳要做的事、为什么这么设计，以及离线 / 已读回执怎么走，一次说明白。

─────────────────────────  
①  message-service → Kafka（msg.down）  
─────────────────────────  
• message-service 在消费到 “msg.up” 时就完成了落库、生成 msgId。  
• 落库成功后立即 `kafkaTemplate.send("msg.down", toUid, dto)`。  
  – partitionKey 选用 toUid，可让同一用户下行顺序有保证。  
• dto 示例  
  {  
    "msgId": 99100123,  
    "fromUid": 10086,  
    "toUid"  : 10010,  
    "body"   : "hi",  
    "ts"     : 1720912345  
  }

目的：把“需要推送给某个用户的消息”汇总到统一 Topic，解耦 message-service 和真正推送逻辑，便于横向扩容。

─────────────────────────  
②  push-service ← Kafka（msg.down）  
─────────────────────────  
• push-service 订阅 msg.down；每条消息先查 Redis 判断收件人是否在线：  

  Route r = redis.get("uid:10010");  
  if (r == null) { offlineQueue.add(dto); /* 后面走 APNs/FCM */ }  
  else   buckets[r.gw].add(dto);

• 为什么要单独做 push-service？  
  – 可以同时处理在线推送与离线推送（APNs、短信等）；  
  – 做批量下发、失败重试、速率控制；  
  – 网关保持无状态。

─────────────────────────  
③  push-service → Netty-Gateway  
─────────────────────────  
连接形态有两种主流方案：

A. gRPC 双向流（生产级）  
   – Netty-Gateway 在启动时，作为 Client 发起 `PushServiceGrpc.newStub(channel)`  
     建立到各 push-service 实例的长流 `stream = stub.push(streamObserver)`；  
   – push-service 需要把消息“按 gw”分桶：  
        Map<String, List<MsgDTO>> map = bucket.flush();  
        for (var entry : map.entrySet()) {  
            stream = gatewayStream(entry.getKey());  
            stream.onNext(BatchMsg.newBuilder().addAllMsg(entry.getValue()).build());  
        }

B. HTTP/2 + Server-Push（小规模或边缘网关）  
   push-service 直接 `POST /internal/push` 带一个批次，网关同步返回 202。  

推荐 A：长流能减少握手开销、天然支持 back-pressure。

流控：每个流 Netty-Gateway 维持一个 `Semaphore(4096)`，写入前 `acquire()`, 客户端 ACK 或写完释放保证网关堆积可控。

─────────────────────────  
④  Netty-Gateway → WebSocket/TCP → Client  
─────────────────────────  
• Gateway 拿到 BatchMsg 后，对每条消息：  
  Channel ch = channelMap.get(msg.toUid);  
  if (ch != null && ch.isActive()) {  
      ch.writeAndFlush(new TextWebSocketFrame(json(msg)));  
  } else {  
      // 链路刚好掉了，回补到 Redis 离线集  
      offlineRedis.add(msg);  
  }

• 成功写出后在同一线程生成 delivery ack：  
  producer.send("ack.up", msg.msgId, new AckDTO(...DELIVERED));

─────────────────────────  
⑤  已读 / 送达回执链路（可选）  
─────────────────────────  
client → gateway → Kafka(ack.up) → message-service → Kafka(receipt.down)  
→ push-service → gateway → 原发送方  
逻辑与正式消息几乎一样，只是 DTO 字段不同。

─────────────────────────  
⑥  离线用户怎么办  
─────────────────────────  
push-service 判断 r == null 就说明用户不在线：  
1. 暂存到 Mongo / Redis list offline:{uid}。  
2. 触发 APNs/FCM：title=“10086：hi”。  
3. 用户下次登陆时，gateway 在握手成功后 RPC `pullOffline(uid)`，一次捞回 N 条补推。  

─────────────────────────  
示例代码速览  
─────────────────────────  
// message-service  
@KafkaListener("msg.up")  
public void onUp(MsgDTO dto) {  
    repo.save(dto);  
    kafka.send("msg.down", dto.getToUid().toString(), dto);  
}

// push-service  
@KafkaListener("msg.down")  
public void onDown(MsgDTO dto) {  
    Route r = redis.get("uid:" + dto.getToUid());  
    if (r != null) bucket.put(r.getGw(), dto);  
    else offline.store(dto);  
}

// gateway – PushStreamObserver  
public void onNext(BatchMsg batch) {  
    for (MsgDTO m : batch.getMsgList()) {  
        Channel ch = routes.get(m.getToUid());  
        if (ch != null) ch.writeAndFlush(new TextWebSocketFrame(Json.to(m)));  
    }  
}

─────────────────────────  
一句话总结  
message-service 只负责“落库 + 抛到 msg.down”，真正把消息送到客户端的动作由 push-service 和 Netty-Gateway 完成：push-service 读取 Kafka、按网关分桶，用 gRPC/HTTP2 长流把批次推给各网关；网关再根据内存中的 uid→Channel 路由写 WebSocket 帧给最终用户。这样分层既保持了微服务解耦，也保证了在海量并发场景下的可伸缩与可靠投递。