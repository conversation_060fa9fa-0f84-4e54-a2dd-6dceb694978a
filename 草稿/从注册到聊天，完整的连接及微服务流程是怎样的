下面把「用户第一次注册」到「双方完成一次聊天」整条链路拆成 5 个阶段、31 个具体步骤，并标注每一步命中了哪台微服务、用了什么存储 / 中间件。看完你就可以在脑子里跑出一张时序图，落地时只要把对应的 REST-API、Kafka Topic、Redis Key 做到即可。

─────────────────────────  
参与角色（按调用顺序出现）  
─────────────────────────  
C : 客户端（iOS/Android/小程序/网页）  
LB_H : HTTP 负载均衡（L7, 80/443）  
GW_H : Spring Cloud Gateway（gateway-http）  
S_USER : user-service（账户档案）  
S_AUTH : auth-service（JWT 签发、刷新）  
DB_U  : MySQL user, auth 表  
C_CFG : Nacos Config & Nacos Discovery  
LB_C : 长连负载均衡（L4, 443/5222）  
GW_C : Netty Conn-Gateway（gateway-conn）  
REDIS : Session & 路由表  
K_UP  : Kafka Topic msg.up  
S_MSG : message-service（持久化、分片）  
DB_M  : MySQL / Cassandra message 表  
K_DN  : Kafka Topic msg.down  
S_PUSH: push-service（下行汇聚）  
GRPC  : gRPC 流，推到 GW_C  
OBS   : Prometheus / OTEL / Loki

─────────────────────────  
阶段 0  ：服务注册 & 配置下发  
─────────────────────────  
0-1  全部 Spring Boot 服务启动时   ──►  C_CFG：pull application-*.yaml  
0-2  GW_H / S_* / GW_C            ──►  C_CFG：registerInstance(name, ip, port)  
0-3  Prometheus Scrape            ◄──  所有实例：/actuator/prometheus  

（结果：网关可以发现业务服务；业务服务可以被网关发现）  

─────────────────────────  
阶段 1  ：用户注册  
─────────────────────────  
1-1  C  POST /v1/user/register                       → LB_H  
1-2  LB_H                                           → GW_H  
1-3  GW_H 负载到 S_USER (/users)                    → S_USER  
1-4  S_USER 事务写入用户表                         → DB_U  
1-5  S_USER 200 OK（userId=10086）                  → GW_H → LB_H → C  

─────────────────────────  
阶段 2  ：登录 & 获取访问凭证  
─────────────────────────  
2-1  C  POST /v1/auth/login { phone, pwd }          → LB_H  
2-2  GW_H 负载到 S_AUTH (/login)                   → S_AUTH  
2-3  S_AUTH 校验密码、生成 accessToken,refreshToken  
     – accessToken = JWT(uid, exp=30min, sign)  
2-4  S_AUTH 201 Created { tokens }                  → C  
2-5  C 把 token 持久化本地 Keychain / Keystore  

─────────────────────────  
阶段 3  ：建立长连接 & 认证握手  
─────────────────────────  
3-1  C  DNS 解析 conn.example.com = LB_C (VIP)  
3-2  C  WebSocket TLS wss://conn.example.com/ws     → LB_C  
      或 TCP tls://conn.example.com:5222           → LB_C  
3-3  LB_C 四层转发到某台 GW_C 实例 (conn-1)  
3-4  （只有 WS）Http/WebSocket 握手完成  
3-5  C  发送 CONNECT 帧 { accessToken }             → GW_C  
3-6  GW_C.AuthHandler 远程调用 S_AUTH /verify       → S_AUTH  
3-7  S_AUTH 校验 JWT、返回 uid=10086               → GW_C  
3-8  GW_C 把 Session 写入 REDIS：  
     key uid:10086  →  value { gw="conn-1", chId=xyz, ts } TTL 30s  
3-9  GW_C 回复 CONNECT_ACK                         → C  
（至此长连接上线，Prometheus metric ws_online++）  

─────────────────────────  
阶段 4  ：A 给 B 发一条文本消息  
─────────────────────────  
假设用户 A(uid=10086) 已连到 conn-1，B(uid=10010) 也在线，连到 conn-3。  

发端链路  
4-1  C_A 发送 MSG 帧                              → conn-1  
     { seq=17, to=10010, body="hi" }  
4-2  conn-1.RouterHandler 组装 MsgDTO             → K_UP  (msg.up)  
     {msgId, fromUid, toUid, body, ts, seq}  

中枢 & 持久化  
4-3  S_MSG consume K_UP                           ◄── K_UP  
4-4  S_MSG 落库                                   → DB_M (shard by uid)  
4-5  S_MSG produce K_DN (msg.down)                → K_DN  
     {msgId, fromUid, toUid, body, ts, status=PENDING}  

下行推送  
4-6  S_PUSH consume K_DN                          ◄── K_DN  
4-7  S_PUSH 查 Redis: uid=10010                  → REDIS  
     得到 route {gw="conn-3", chId=abc}  
4-8  S_PUSH 将多条消息按 gw 分桶                  {gw:"conn-3", list[…]}  
4-9  S_PUSH 通过 gRPC Stream push(list)           → conn-3  

收端链路  
4-10 conn-3 收到 gRPC Push, 写 channel.write     → C_B  
4-11 C_B 收到消息，立刻回 ACK 帧                 → conn-3  
4-12 conn-3.AckHandler produce ack.up            → K_UP  
     {msgId, fromUid=10010, status=READ}  

回写已读  
4-13 S_MSG consume ack.up                        ◄── K_UP  
4-14 S_MSG update db set status=READ             → DB_M  
4-15 （可选）S_MSG produce receipt.down          → K_DN (回执给 A)  
4-16 S_PUSH → conn-1 → C_A，让 A 的气泡变灰。  

─────────────────────────  
阶段 5  ：会话维护 & 离线  
─────────────────────────  
5-1  C 每 25s 发送 PING                             → GW_C  
5-2  GW_C.IdleStateHandler 刷新 Redis TTL           → REDIS  
5-3  App 进后台 or 网络断                           (socket close)  
5-4  GW_C.channelInactive 删除 Redis Key            → REDIS  
5-5  Prometheus 抓到 online 下跌，Alert 带 tag gw=conn-1  

─────────────────────────  
附录：关键数据结构  
─────────────────────────  
Redis Session Key  
   uid:{uid} -> { gw, chId, device, lastHeartbeat } , EX 30s  

Kafka Topic  
   msg.up   partitionKey = fromUid (保证同一个人顺序)  
   msg.down partitionKey = gw-instance (便于批量推送)  
   ack.up   partitionKey = msgId  

MySQL message 表  
   CREATE TABLE msg_{shard} (  
     msg_id  BIGINT PK,  
     chat_id BIGINT,      -- 会话 (A_B)  
     from_uid BIGINT,  
     to_uid   BIGINT,  
     body     TEXT,  
     status   TINYINT,    -- 0 SENT 1 DELIVERED 2 READ  
     ts       BIGINT  
   ) PARTITION BY HASH(chat_id) PARTITIONS 64;  

─────────────────────────  
监控指标（Prometheus）  
─────────────────────────  
ws_online_connections{gw}  
msg_up_qps, msg_down_qps  
msg_delivery_latency_ms_p99  
redis_lookup_latency_ms  
error_frame_total{type}  
login_success_total, login_fail_total  

─────────────────────────  
一页总结  
─────────────────────────  
1) 注册 / 登录 100% 经 HTTP-Gateway 调用授权中心获取 JWT。  
2) 客户端带着 JWT 到 Netty-Gateway 建立长连接，网关通过 auth-service 校验，把 Session 写 Redis，让所有后端服务都能按 uid 定位到在线节点。  
3) 发消息时，连接网关只做「入 Kafka」，真正的存储、分发完全由 message-service & push-service 完成，保证网关无状态、易水平扩展。  
4) 收消息流程是「Kafka → push-service → gRPC → 目标网关 → 客户端」，ACK 再回到 Kafka 更新数据库，实现端到端可靠。  
5) 整条链路所有微服务都注册到 Nacos，Prometheus 统一收指标，Redis TTL + IdleStateHandler 保证在线状态秒级一致。  

照着这 5 个阶段 + 31 个步骤落代码和脚本，你就拥有了一条从“按注册键”到“双方看到已读”的完整微服务路径。