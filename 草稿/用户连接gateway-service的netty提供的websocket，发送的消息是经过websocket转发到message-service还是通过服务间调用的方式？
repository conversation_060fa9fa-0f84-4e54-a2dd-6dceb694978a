结论先行  
1. WebSocket 只存在于「客户端 ↔ Netty-Gateway」这一跳；  
2. 进入网关之后，消息会被转成内部 DTO，然后  
   – 大并发场景：写入 Kafka／RocketMQ 等消息总线 → message-service 订阅；  
   – 轻量或 PoC：直接 gRPC / HTTP POST 给 message-service；  
3. 绝不会再用 WebSocket 在服务之间穿透转发——那会把长连接状态蔓延到整个集群，完全失去微服务解耦的意义。

为什么不用“WebSocket → WebSocket”转发  
• 连接数量：10 万在线意味着 10 万条 socket。如果网关再把每条 socket 往下游拉一条，就要求 message-service 也维护 10 万连接，资源和复杂度瞬间爆炸。  
• 生命周期：长连接的心跳、断链、重连都要层层感知；而微服务内部更希望是“请求-响应”或“消息”语义。  
• 可观测性：Kafka / gRPC 很容易统计 QPS、延迟；链路里塞一堆 WebSocket Frame 难以做指标聚合。  
• 弹性：消息总线天然具备缓冲、重试、顺序等能力，可以“削峰”；WebSocket 直接转发做不到。

推荐的两条落地方案  

A. 生产级（高并发 IM / 推送）  
┌────────┐          ws      ┌──────────┐   Kafka   ┌──────────┐  
│  Client │ ───────► │ Netty-GW │ ───────► │msg.up  │ ──────► │MessageSvc│  
└────────┘           ▲         └──────────┘          └──────────┘  
                     │gRPC Push (下行) ▲  Kafka msg.down / ack  
                     └──────────────────┘  

流程  
1) Netty-GW 收到 WebSocket Frame → 校验 token → 封装 MsgDTO。  
2) MsgDTO `producer.send("msg.up", dto)` 写入 Kafka。  
3) message-service `@KafkaListener("msg.up")` 消费，落库、投递下行。  
4) push-service 把下行消息按网关分桶后，走 gRPC stream 一次性推回 Netty-GW。  
5) Netty-GW `channel.writeAndFlush()` 发给目标客户端。

B. 小规模 / PoC（几千在线可接受）  
1) Netty-GW 收帧后 `webClient.post("/internal/messages")` 或用 gRPC unary 调 message-service。  
2) message-service 同步返回 msgId 后，网关立即转发 ACK 给客户端。  
3) 下行可以改成网关主动 Long-Polling message-service，或 message-service 回调网关 HTTP。  
——这种模式代码简单，但网关延迟、消息可靠性、水平扩展都会受限，不建议上线大业务。

核心代码示例（高并发方案）  
// Netty RouterHandler  
public class RouterHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {  
    private final KafkaTemplate<String, MsgDTO> kafka;  
    @Override  
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame frame) {  
        MsgDTO dto = convert(frame.text());  
        kafka.send("msg.up", dto.getFromUid().toString(), dto);  
    }  
}  

// message-service Listener  
@KafkaListener(topics = "msg.up")  
public void onMessage(MsgDTO dto) {  
    messageDao.save(dto);  
    kafkaTemplate.send("msg.down", dto.getToUid().toString(), dto);  
}

可切换的开关  
spring.profiles.active = dev | prod  
dev  ->  netty.gateway.mode = direct (HTTP/gRPC)  
prod ->  netty.gateway.mode = kafka

总结一句话  
只有“客户端 ↔ 网关”走 WebSocket；网关往后全部变成「消息总线 + gRPC/HTTP」这样的服务间调用，这才符合微服务的解耦原则、也便于高并发水平扩展和可观测。