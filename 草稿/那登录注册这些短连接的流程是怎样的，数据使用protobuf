下面把「登录 / 注册」这一类“短连接”请求在整条链路里的走法，以及如何用 Protobuf 编／解码，一步一步说明。账号体系通常独立成 auth-service（或 user-service），网关只做转发和轻量校验。

────────────────────────────────────────
1. 端到端时序
────────────────────────────────────────
Client ───HTTPS/HTTP2───► Netty-Gateway ───gRPC───► auth-service
        (Proto Bytes)                    (Proto Bytes)

<登录>
1) Client POST /api/v1/login   Body=LoginReq(proto)   —— 短连接
2) Gateway 做 TLS、限流、签名校验→ 直接 gRPC 调 auth-service.login()
3) auth-service 校验密码 → 生成 userId, JWT/Session → 写 Redis “token→uid”
4) gRPC 回 LoginResp(token, uid, expire) 给网关
5) 网关把同一段 protobuf 原样写回 HTTP Response
6) Client 收到 token，随即建立 WebSocket：
      ws://gw.example.com/chat?token=eyJhbGciOi…

<注册>
路径类似，只是 gRPC 方法是 auth-service.register()，成功后可以直接下发 token 让客户端无缝登录。

────────────────────────────────────────
2. 为什么不用 WebSocket 把登陆报文发进来？
────────────────────────────────────────
• 先 HTTP 再 WS —— 能在网关层完成鉴权、限流、验证码、风控；  
• 登录失败直接 4xx，省掉建立长连接的开销；  
• 绝大部分移动／Web SDK 天生就有 “REST + WebSocket” 的双栈，接入简单。

────────────────────────────────────────
3. proto 定义（*.proto）
────────────────────────────────────────
syntax = "proto3";
package auth.v1;

option java_package = "com.demo.auth.proto";
option go_package   = "github.com/demo/im/api/auth/v1;authv1";

// ---------- Login ----------
message LoginRequest {
  string phone    = 1;   // 或 email/username
  string password = 2;   // 客户端 SHA-256 后再传
  string deviceId = 3;
}

message LoginResponse {
  int64  uid      = 1;
  string token    = 2;   // JWT
  int64  expireAt = 3;   // epoch 秒
}

// ---------- Register ----------
message RegisterRequest {
  string phone       = 1;
  string smsCode     = 2;
  string password    = 3;
}

message RegisterResponse {
  int64  uid      = 1;
  string token    = 2;
  int64  expireAt = 3;
}

// ---------- gRPC Service ----------
service AuthService {
  rpc Login    (LoginRequest)    returns (LoginResponse);
  rpc Register (RegisterRequest) returns (RegisterResponse);
}

网关暴露 HTTP/1.1 + Protobuf：  
POST /api/v1/login  
Content-Type: application/x-protobuf

若想直接用 gRPC-Web，可把网关开启 gRPC-Web 反向代理；移动端用 grpc-okhttp，Web 端用 grpc-web.js。

────────────────────────────────────────
4. Netty-Gateway 实现要点
────────────────────────────────────────
• 路由：/api/** 都走短桶线程池 httpPool，/ws/** 走 longConnPool。  
• 编码：HTTP Side – 十六进制或直接 `ByteBuf.writeBytes(protoBytes)`，不转 JSON。  
• gRPC 客户端：启动时 `managedChannel = ManagedChannelBuilder.forTarget("auth:8080").usePlaintext().build();`  
• 熔断/限流：login 和 register 通常加滑窗限速：同 IP 1min / 30 次；同手机号短信 10 次。

────────────────────────────────────────
5. auth-service 内部流程
────────────────────────────────────────
1) MySQL `SELECT id, pwd_hash, salt FROM t_user WHERE phone=?`  
2) `hash(pwd + salt) == pwd_hash`? → 否则返回 code=401  
3) `jwt = jwt.sign(uid, deviceId, exp=7d)`  
4) Redis SETEX "jwt:{token}" = uid, 7d  
5) 插入或更新登录日志、设备表

────────────────────────────────────────
6. Client SDK 典型调用
────────────────────────────────────────
byte[] body = LoginRequest.newBuilder()
    .setPhone("13800138000")
    .setPassword(sha256(pwd))
    .setDeviceId(deviceId)
    .build().toByteArray();

HttpRequest req = HttpRequest.newBuilder()
    .uri(URI.create("https://gw.example.com/api/v1/login"))
    .POST(BodyPublishers.ofByteArray(body))
    .header("Content-Type", "application/x-protobuf")
    .build();

HttpResponse<byte[]> resp = httpClient.send(req, BodyHandlers.ofByteArray());
LoginResponse lr = LoginResponse.parseFrom(resp.body());
openWebSocket("wss://gw.example.com/ws/chat?token=" + lr.getToken());

────────────────────────────────────────
7. 登录后在 WebSocket 首帧补 ACK（可选）
────────────────────────────────────────
握手参数 token 校验通过 → 网关在 ChannelAttr 写入 uid → 回一帧  
LoginAck { code=200, server_ts=... }  
这帧同样使用 protobuf（例如 ChatEnvelope type=LOGIN_ACK）。

────────────────────────────────────────
8. 退出 / token 失效
────────────────────────────────────────
client POST /api/v1/logout(token) → 网关 RPC 调 auth-service.logout()  
auth-service 删除 Redis jwt → 返回 200  
随后网关关闭该用户所有 WebSocket channel。

────────────────────────────────────────
一句话总结
────────────────────────────────────────
登录 / 注册走独立的短连接 HTTP/gRPC，整个链路同样用 protobuf 编码：  
Client ↔（HTTP/HTTPS, x-protobuf）↔ Netty-Gateway ↔（gRPC, protobuf）↔ auth-service  
成功后客户端拿到 JWT，再去建立长连接 WebSocket 用于实时消息。这样可以把“一次性业务”与“长连接 IM”彻底分离，接口清晰、性能好、也便于权限与风控的统一管理。