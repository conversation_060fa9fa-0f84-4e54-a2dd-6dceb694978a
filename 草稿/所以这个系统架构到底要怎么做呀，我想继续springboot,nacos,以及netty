下面给你一份「能落地、能演进」的整体蓝图，全部围绕 Spring Boot + Nacos + Netty 来设计。读完你应该能回答三件事：  
1) 每一层各放什么组件；  
2) 服务之间怎么调用、怎么注册到 Nacos；  
3) 从代码结构到部署、监控分别怎么做。

─────────────────────────  
一、总览拓扑  
─────────────────────────  
                    ┌──────────┐  80/443           ┌──────────┐
Internet ──LB(7)──▶ │HTTP GW   │ ───────────► ……REST…… │业务微服务│
                    │(SCG)     │                  │SpringBoot│
                    └──────────┘                  └──────────┘
                       ▲   ▲                         ▲
   443/WSS 5222/TCP    │   │gRPC/Kafka               │Nacos Discover
                    ┌──────────┐  SessionState  ┌────────┐
Internet ──LB(4)──▶ │Conn-GW   │───────────────▶│Redis   │
(Netty)             │(Netty)   │<───Push───────▶│Cluster │
                    └──────────┘                └────────┘
                       │
                       ▼
                   Observability
 (Prometheus / Grafana / Loki / OTEL Collector)

说明  
1. HTTP Gateway：Spring Cloud Gateway (SCG)，负责所有短连接/REST。  
2. Conn-Gateway：自研 Netty 服务，同时监听 443(wss) 和 5222(tcp)。  
3. 微服务：纯 Spring Boot (MVC or WebFlux 自定)，配置、注册全走 Nacos。  
4. Redis：用户 Session & 路由表，做水平扩展。  
5. Kafka/RocketMQ：消息总线，解耦流量读写。  
6. Push-Service：订阅 Kafka，把消息推给连接网关（gRPC / TCP 内部协议）。  

─────────────────────────  
二、每层如何接 Nacos  
─────────────────────────  
1. 配置中心  
   spring-cloud-starter-alibaba-nacos-config  
   – application.yaml 放在 Nacos namespace=prod，Conn-GW 也可直接用 NacosConfigService 拉取限流、灰度比例等动态配置。  

2. 服务注册  
   spring-cloud-starter-alibaba-nacos-discovery  
   • HTTP-GW 在启动时注册 name=http-gateway。  
   • Conn-GW 注册 name=conn-gateway；Push-Service 通过 Nacos 获取 Conn-GW 列表做长连 RPC。  
   • 业务微服务之间使用 @LoadBalanced RestTemplate / Feign + Nacos 服务发现。  

─────────────────────────  
三、Netty 长连接网关实现要点  
─────────────────────────  
ChannelInitializer  
  if (port == 443)    -> WsInitializer()  
  if (port == 5222)   -> TcpInitializer()  

Pipeline（合并业务 Handler）  
  IdleStateHandler  
  AuthHandler (调用 auth-service 验证 token)  
  RouterHandler (写入 Kafka “msg.up”)  
  AckHandler  
  ErrorHandler  

Session 存储  
  key   : uid → ConnInfo(ip,port,channelId,shard)  
  value : 用 Redis HASH / Cluster；TTL = 24h，心跳续期。  

下行推送  
  Push-Service 拉取“msg.down”Topic  
  -> 根据 uid 查询 Session -> gRPC Stream 到对应 Conn-GW  
  -> Conn-GW channel.writeAndFlush(ByteBuf)  

扩容  
  • Conn-GW 无状态，只依赖 Redis；HPA 根据 online_connections / CPU 自动扩。  
  • Nacos 健康检查 5s 一次；失联节点 Redis Key 在 30s 内失效。  

─────────────────────────  
四、Spring Boot 微服务规范  
─────────────────────────  
• 基础依赖：spring-boot-starter-web + nacos-discovery + springdoc-openapi。  
• 统一错误码 & 日志：logback + traceId (OTEL)。  
• 数据层：MyBatis-Plus / JPA；如需真正异步可渐进引入 R2DBC+WebFlux。  
• 消息：spring-kafka / spring-rocketmq；所有写消息走事务 outbox，防止丢失。  

─────────────────────────  
五、代码目录示例  
─────────────────────────  
im-platform/  
  ├─ common/               # DTO, Utils, ErrorCode  
  ├─ gateway-http/         # Spring Cloud Gateway  
  ├─ gateway-conn/         # Netty Starter  
  ├─ service-user/         # 用户中心  
  ├─ service-auth/         # 登录鉴权  
  ├─ service-message/      # 保存、分片、回执  
  ├─ push-service/         # 下行推送  
  └─ deploy/               # Dockerfile, Helm chart  

gateway-conn 依赖 spring-boot-starter + netty-all，没有 Web 容器；main 方法里启动两个 ServerBootstrap。  

─────────────────────────  
六、链路观测与告警  
─────────────────────────  
1. Trace：OpenTelemetry Java Agent + Tempo / Jaeger。  
2. Metric：Micrometer + Prometheus；关键指标  
   http_requests_total,  
   ws_online_connections, tcp_online_connections,  
   msg_latency_p99, dropped_msg_total.  
3. Log：Loki / Elasticsearch；Conn-GW 每个异常帧记录 uid + seq + error。  
4. Dashboard：Grafana 混合展示 HTTP + 长连接健康度。  

─────────────────────────  
七、上线顺序与里程碑  
─────────────────────────  
M1  搭好 Nacos（单机→集群）、MySQL、Redis、Kafka 基础设施  
M2  HTTP-GW + User/Auth 服务跑通手机验证码登录  
M3  Conn-GW 能登录、heartbeat、上下行 echo  
M4  Message-Service + Kafka 保证消息可靠写+读  
M5  Push-Service + Conn-GW 完整 IM 流程  
M6  灰度发布工具、蓝绿、Prometheus 告警规则补齐  
M7  性能压测：10 万在线，1 万 QPS 发消息；根据监控调优  

─────────────────────────  
八、一句话结论  
继续 Spring Boot + Nacos + Netty 完全没问题：REST 流量交给 Spring Cloud Gateway + 普通微服务；长连接单独用 Netty-Gateway，统一注册到 Nacos，再用 Kafka/Redis 做消息解耦，就能既保持团队熟悉的技术栈，又满足高并发 IM 需求。