可以，而且通常会把两种长连接放在同一套 Netty-Gateway 集群里统一维护，区别只是「握手阶段」与「编解码 Pipeline」不同。下面给出落地方案与注意点，供你决定要不要同时开放 WebSocket 和自研 TCP。

一、推荐的网关分工  
────────────────────────  
1. HTTP API-Gateway（Spring Cloud Gateway、Kong 等）  
   • 仅负责 REST / GraphQL / 上传回调等短连接流量  
   • 走 80/443 端口，使用 HTTP/1.1 或 HTTP/2 TLS  

2. Long-Connection Gateway（Netty）  
   • 同时监听两个端口：  
     – 443 (wss) 兼容浏览器 / 小程序  
     – 5222 或 8600 (tcp) 给原生 App  
   • 内部转发统一走 gRPC / Kafka，跟协议无关。  

二、在同一进程支持两种协议的实现要点  
────────────────────────  
1. 端口与 ChannelInitializer  
   ```
   EventLoopGroup boss = new NioEventLoopGroup(1);
   EventLoopGroup worker = new NioEventLoopGroup();
   
   // TCP
   new ServerBootstrap()
       .group(boss, worker)
       .channel(NioServerSocketChannel.class)
       .childHandler(new TcpInitializer())
       .bind(5222);
   
   // WebSocket (TLS 终止用 Netty/OpenSSL or 外部 LB)
   new ServerBootstrap()
       .group(boss, worker)
       .channel(NioServerSocketChannel.class)
       .childHandler(new WsInitializer())
       .bind(443);
   ```

2. 两条 Pipeline  
   • TCP:  
     IdleStateHandler → LengthFieldBasedFrameDecoder → ProtobufDecoder → AuthHandler → RouterHandler → …  

   • WS:  
     HttpServerCodec → HttpObjectAggregator → WebSocketServerProtocolHandler("/ws",…)  
     → WsBinaryFrame2ByteBufHandler → 复用后面的 ProtobufDecoder…  

   业务处理器完全共用，差异止于握手与帧拆包。  

3. 统一 Session 抽象  
   Session {  
      long uid;  
      String cid;        // 连接唯一标识  
      Channel channel;   // Netty channel  
      ConnType type;     // WS / TCP  
   }  
   其余逻辑（心跳、ACK、下行推送）全部基于 Session 做，不关心协议。  

4. 反压与流控  
   • WebSocket 默认基于 HTTP/1.1，浏览器层面有 65 kB send buffer，需要在服务器侧加 Channel.isWritable() 检测 + writeBufferHigh/LowWaterMark。  
   • TCP 客户端自己实现心跳和 ACK，也要在写队列长度过高时丢包或踢下线。  

5. 监控区分  
   label protocol=ws | tcp  
   收集：  
     – online_connections{protocol}  
     – p99_delivery_latency{protocol}  
     – dropped_messages_total{protocol}  

三、运维与弹性  
────────────────────────  
• 在 K8s 中，一个 Deployment，两份 Service (tcp 5222, wss 443) 或用同一个 LoadBalancer 不同端口；HPA 依据总在线数 / CPU。  
• 如果只打开 443(wss) 也可以，移动端同样能用 TLS-WebSocket；但原生 TCP 更省帧头开销（≈10% 带宽）且握手快。  

四、何时拆成两套集群？  
────────────────────────  
1. 协议特征差异导致资源使用大幅不同（例如浏览器频繁切前后台）。  
2. 想做分阶段灰度：先 WebSocket，后大版本 TCP。  
3. 安全策略不同：WSS 需要 SNI、多域证书；TCP 可能走私有协议端口。  

五、小结  
────────────────────────  
• “网关服务能否同时提供 WebSocket 和 TCP？”——完全可以，且主流 IM 都是同一套长连接网关对两类协议做分流。  
• 最省事的做法是在 Netty 内部起两组 ChannelInitializer，共享业务 Handler 与下游调用。  
• 只要保持统一的 Session 抽象和监控维度，就不会因为双协议而增大太多复杂度。