dependencies {
    implementation(project(":common:common-core"))
    implementation(project(":common:common-proto"))
    implementation(project(":common:common-security"))
    implementation(project(":common:common-web"))
    
    // Spring Cloud Gateway
    implementation("org.springframework.cloud:spring-cloud-starter-gateway")
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer")
    
    // WebFlux (Gateway基于WebFlux)
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    
    // Netty for WebSocket
    implementation("io.netty:netty-all:${property("nettyVersion")}")
    implementation("io.netty:netty-transport-native-epoll:${property("nettyVersion")}:linux-x86_64")
    implementation("io.netty:netty-transport-native-kqueue:${property("nettyVersion")}:osx-x86_64")
    
    // gRPC客户端
    implementation("net.devh:grpc-client-spring-boot-starter:3.1.0.RELEASE")
    
    // 限流
    implementation("org.springframework.boot:spring-boot-starter-data-redis-reactive")
    
    // 监控
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    
    // 配置处理
    implementation("org.springframework.boot:spring-boot-configuration-processor")
    
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.projectreactor:reactor-test")
}
