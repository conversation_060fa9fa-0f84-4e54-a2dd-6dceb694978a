package com.zlim.gateway.websocket.handler

import com.fasterxml.jackson.databind.ObjectMapper
import com.zlim.gateway.websocket.session.WebSocketSessionManager
import com.zlim.message.proto.Frame
import com.zlim.message.proto.FrameType
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.SimpleChannelInboundHandler
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame
import io.netty.handler.codec.http.websocketx.WebSocketFrame
import io.netty.handler.timeout.IdleState
import io.netty.handler.timeout.IdleStateEvent
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * WebSocket消息处理器
 */
@Component
class WebSocketHandler(
    private val sessionManager: WebSocketSessionManager,
    private val objectMapper: ObjectMapper
) : SimpleChannelInboundHandler<WebSocketFrame>() {
    
    private val logger = LoggerFactory.getLogger(WebSocketHandler::class.java)
    
    override fun channelActive(ctx: ChannelHandlerContext) {
        logger.info("WebSocket连接建立: {}", ctx.channel().id())
        super.channelActive(ctx)
    }
    
    override fun channelInactive(ctx: ChannelHandlerContext) {
        logger.info("WebSocket连接断开: {}", ctx.channel().id())
        sessionManager.removeSession(ctx.channel())
        super.channelInactive(ctx)
    }
    
    override fun channelRead0(ctx: ChannelHandlerContext, frame: WebSocketFrame) {
        when (frame) {
            is TextWebSocketFrame -> {
                handleTextFrame(ctx, frame)
            }
            else -> {
                logger.warn("不支持的WebSocket帧类型: {}", frame.javaClass.simpleName)
            }
        }
    }
    
    override fun userEventTriggered(ctx: ChannelHandlerContext, evt: Any) {
        if (evt is IdleStateEvent) {
            when (evt.state()) {
                IdleState.READER_IDLE -> {
                    logger.info("读空闲，发送心跳: {}", ctx.channel().id())
                    sendPing(ctx)
                }
                else -> {
                    super.userEventTriggered(ctx, evt)
                }
            }
        } else {
            super.userEventTriggered(ctx, evt)
        }
    }
    
    override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
        logger.error("WebSocket处理异常: {}", ctx.channel().id(), cause)
        ctx.close()
    }
    
    /**
     * 处理文本帧
     */
    private fun handleTextFrame(ctx: ChannelHandlerContext, frame: TextWebSocketFrame) {
        try {
            val text = frame.text()
            logger.debug("收到WebSocket消息: {}", text)
            
            // 解析消息帧
            val frameData = objectMapper.readValue(text, Map::class.java)
            val frameType = FrameType.valueOf(frameData["type"] as String)
            
            when (frameType) {
                FrameType.FRAME_TYPE_CONNECT -> {
                    handleConnect(ctx, frameData)
                }
                FrameType.FRAME_TYPE_PING -> {
                    handlePing(ctx)
                }
                FrameType.FRAME_TYPE_PONG -> {
                    handlePong(ctx)
                }
                FrameType.FRAME_TYPE_MESSAGE -> {
                    handleMessage(ctx, frameData)
                }
                FrameType.FRAME_TYPE_TYPING -> {
                    handleTyping(ctx, frameData)
                }
                else -> {
                    logger.warn("未处理的帧类型: {}", frameType)
                }
            }
            
        } catch (e: Exception) {
            logger.error("处理WebSocket消息失败", e)
            sendError(ctx, "消息格式错误")
        }
    }
    
    /**
     * 处理连接请求
     */
    private fun handleConnect(ctx: ChannelHandlerContext, frameData: Map<*, *>) {
        try {
            val payload = frameData["payload"] as? Map<*, *>
            val accessToken = payload?.get("accessToken") as? String
            
            if (accessToken.isNullOrBlank()) {
                sendConnectResponse(ctx, false, "访问令牌不能为空")
                return
            }
            
            // 验证令牌并建立会话
            sessionManager.createSession(ctx.channel(), accessToken) { success, message, userId ->
                sendConnectResponse(ctx, success, message)
                if (success && userId != null) {
                    logger.info("用户 {} 建立WebSocket连接: {}", userId, ctx.channel().id())
                }
            }
            
        } catch (e: Exception) {
            logger.error("处理连接请求失败", e)
            sendConnectResponse(ctx, false, "连接失败")
        }
    }
    
    /**
     * 处理心跳请求
     */
    private fun handlePing(ctx: ChannelHandlerContext) {
        sendPong(ctx)
    }
    
    /**
     * 处理心跳响应
     */
    private fun handlePong(ctx: ChannelHandlerContext) {
        logger.debug("收到心跳响应: {}", ctx.channel().id())
    }
    
    /**
     * 处理消息
     */
    private fun handleMessage(ctx: ChannelHandlerContext, frameData: Map<*, *>) {
        val session = sessionManager.getSession(ctx.channel())
        if (session == null) {
            sendError(ctx, "未认证的连接")
            return
        }
        
        try {
            // 转发消息到消息服务
            sessionManager.forwardMessage(session, frameData)
        } catch (e: Exception) {
            logger.error("转发消息失败", e)
            sendError(ctx, "消息发送失败")
        }
    }
    
    /**
     * 处理输入状态
     */
    private fun handleTyping(ctx: ChannelHandlerContext, frameData: Map<*, *>) {
        val session = sessionManager.getSession(ctx.channel())
        if (session == null) {
            sendError(ctx, "未认证的连接")
            return
        }
        
        try {
            // 转发输入状态到消息服务
            sessionManager.forwardTypingStatus(session, frameData)
        } catch (e: Exception) {
            logger.error("转发输入状态失败", e)
        }
    }
    
    /**
     * 发送连接响应
     */
    private fun sendConnectResponse(ctx: ChannelHandlerContext, success: Boolean, message: String) {
        val response = mapOf(
            "type" to "CONNECT_ACK",
            "payload" to mapOf(
                "success" to success,
                "message" to message,
                "timestamp" to System.currentTimeMillis()
            )
        )
        sendFrame(ctx, response)
    }
    
    /**
     * 发送心跳
     */
    private fun sendPing(ctx: ChannelHandlerContext) {
        val ping = mapOf(
            "type" to "PING",
            "timestamp" to System.currentTimeMillis()
        )
        sendFrame(ctx, ping)
    }
    
    /**
     * 发送心跳响应
     */
    private fun sendPong(ctx: ChannelHandlerContext) {
        val pong = mapOf(
            "type" to "PONG",
            "timestamp" to System.currentTimeMillis()
        )
        sendFrame(ctx, pong)
    }
    
    /**
     * 发送错误消息
     */
    private fun sendError(ctx: ChannelHandlerContext, message: String) {
        val error = mapOf(
            "type" to "ERROR",
            "payload" to mapOf(
                "message" to message,
                "timestamp" to System.currentTimeMillis()
            )
        )
        sendFrame(ctx, error)
    }
    
    /**
     * 发送帧
     */
    private fun sendFrame(ctx: ChannelHandlerContext, frame: Any) {
        try {
            val json = objectMapper.writeValueAsString(frame)
            ctx.writeAndFlush(TextWebSocketFrame(json))
        } catch (e: Exception) {
            logger.error("发送WebSocket消息失败", e)
        }
    }
}
