package com.zlim.gateway.websocket

import com.zlim.gateway.websocket.handler.WebSocketChannelInitializer
import io.netty.bootstrap.ServerBootstrap
import io.netty.channel.ChannelFuture
import io.netty.channel.ChannelOption
import io.netty.channel.EventLoopGroup
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.nio.NioServerSocketChannel
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy

/**
 * WebSocket服务器
 */
@Component
class WebSocketServer(
    private val channelInitializer: WebSocketChannelInitializer
) {
    
    private val logger = LoggerFactory.getLogger(WebSocketServer::class.java)
    
    @Value("\${websocket.port:9090}")
    private var port: Int = 9090
    
    @Value("\${websocket.boss-threads:1}")
    private var bossThreads: Int = 1
    
    @Value("\${websocket.worker-threads:4}")
    private var workerThreads: Int = 4
    
    private lateinit var bossGroup: EventLoopGroup
    private lateinit var workerGroup: EventLoopGroup
    private lateinit var channelFuture: ChannelFuture
    
    @PostConstruct
    fun start() {
        Thread {
            try {
                bossGroup = NioEventLoopGroup(bossThreads)
                workerGroup = NioEventLoopGroup(workerThreads)
                
                val bootstrap = ServerBootstrap()
                bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel::class.java)
                    .childHandler(channelInitializer)
                    .option(ChannelOption.SO_BACKLOG, 1024)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true)
                
                channelFuture = bootstrap.bind(port).sync()
                logger.info("WebSocket服务器启动成功，端口: {}", port)
                
                channelFuture.channel().closeFuture().sync()
                
            } catch (e: Exception) {
                logger.error("WebSocket服务器启动失败", e)
            } finally {
                shutdown()
            }
        }.start()
    }
    
    @PreDestroy
    fun shutdown() {
        try {
            if (::channelFuture.isInitialized) {
                channelFuture.channel().close().sync()
            }
            if (::workerGroup.isInitialized) {
                workerGroup.shutdownGracefully().sync()
            }
            if (::bossGroup.isInitialized) {
                bossGroup.shutdownGracefully().sync()
            }
            logger.info("WebSocket服务器已关闭")
        } catch (e: Exception) {
            logger.error("WebSocket服务器关闭失败", e)
        }
    }
}
