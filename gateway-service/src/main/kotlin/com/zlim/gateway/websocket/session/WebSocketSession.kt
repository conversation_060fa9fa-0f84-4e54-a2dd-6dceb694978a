package com.zlim.gateway.websocket.session

import io.netty.channel.Channel
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicLong

/**
 * WebSocket会话
 */
data class WebSocketSession(
    val sessionId: String,
    val channel: Channel,
    val userId: Long,
    val username: String,
    val deviceId: String? = null,
    val deviceType: String? = null,
    val connectedAt: LocalDateTime = LocalDateTime.now(),
    private val lastActiveTime: AtomicLong = AtomicLong(System.currentTimeMillis())
) {
    
    /**
     * 更新最后活跃时间
     */
    fun updateLastActiveTime() {
        lastActiveTime.set(System.currentTimeMillis())
    }
    
    /**
     * 获取最后活跃时间
     */
    fun getLastActiveTime(): Long = lastActiveTime.get()
    
    /**
     * 检查连接是否活跃
     */
    fun isActive(): Boolean = channel.isActive
    
    /**
     * 获取连接ID
     */
    fun getChannelId(): String = channel.id().asShortText()
    
    /**
     * 发送消息
     */
    fun sendMessage(message: String) {
        if (isActive()) {
            channel.writeAndFlush(io.netty.handler.codec.http.websocketx.TextWebSocketFrame(message))
        }
    }
    
    /**
     * 关闭连接
     */
    fun close() {
        if (isActive()) {
            channel.close()
        }
    }
}
