package com.zlim.gateway.websocket.handler

import io.netty.channel.ChannelInitializer
import io.netty.channel.socket.SocketChannel
import io.netty.handler.codec.http.HttpObjectAggregator
import io.netty.handler.codec.http.HttpServerCodec
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler
import io.netty.handler.stream.ChunkedWriteHandler
import io.netty.handler.timeout.IdleStateHandler
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

/**
 * WebSocket通道初始化器
 */
@Component
class WebSocketChannelInitializer(
    private val webSocketHandler: WebSocketHandler
) : ChannelInitializer<SocketChannel>() {
    
    override fun initChannel(ch: SocketChannel) {
        val pipeline = ch.pipeline()
        
        // HTTP编解码器
        pipeline.addLast("http-codec", HttpServerCodec())
        
        // HTTP对象聚合器
        pipeline.addLast("http-aggregator", HttpObjectAggregator(65536))
        
        // 分块写处理器
        pipeline.addLast("http-chunked", ChunkedWriteHandler())
        
        // 空闲状态处理器（60秒无数据交互则触发心跳检测）
        pipeline.addLast("idle-state", IdleStateHandler(60, 0, 0, TimeUnit.SECONDS))
        
        // WebSocket协议处理器
        pipeline.addLast("websocket-protocol", WebSocketServerProtocolHandler("/ws", null, true))
        
        // 自定义WebSocket处理器
        pipeline.addLast("websocket-handler", webSocketHandler)
    }
}
