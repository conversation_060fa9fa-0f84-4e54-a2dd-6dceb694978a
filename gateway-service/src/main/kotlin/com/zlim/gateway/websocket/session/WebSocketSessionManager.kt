package com.zlim.gateway.websocket.session

import com.fasterxml.jackson.databind.ObjectMapper
import com.zlim.user.proto.VerifyTokenRequest
import com.zlim.user.proto.UserServiceGrpc
import io.netty.channel.Channel
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * WebSocket会话管理器
 */
@Component
class WebSocketSessionManager(
    private val redisTemplate: RedisTemplate<String, String>,
    private val objectMapper: ObjectMapper
) {
    
    private val logger = LoggerFactory.getLogger(WebSocketSessionManager::class.java)
    
    @GrpcClient("user-service")
    private lateinit var userServiceStub: UserServiceGrpc.UserServiceBlockingStub
    
    // 本地会话缓存 channelId -> session
    private val localSessions = ConcurrentHashMap<String, WebSocketSession>()
    
    // 用户ID到会话的映射 userId -> Set<channelId>
    private val userSessions = ConcurrentHashMap<Long, MutableSet<String>>()
    
    /**
     * 创建会话
     */
    fun createSession(
        channel: Channel, 
        accessToken: String, 
        callback: (Boolean, String, Long?) -> Unit
    ) {
        try {
            // 验证访问令牌
            val verifyRequest = VerifyTokenRequest.newBuilder()
                .setAccessToken(accessToken)
                .build()
            
            val verifyResponse = userServiceStub.verifyToken(verifyRequest)
            
            if (!verifyResponse.valid) {
                callback(false, "访问令牌无效", null)
                return
            }
            
            val userId = verifyResponse.userId
            val username = verifyResponse.username
            val channelId = channel.id().asShortText()
            
            // 创建会话
            val session = WebSocketSession(
                sessionId = generateSessionId(userId, channelId),
                channel = channel,
                userId = userId,
                username = username
            )
            
            // 存储到本地缓存
            localSessions[channelId] = session
            
            // 更新用户会话映射
            userSessions.computeIfAbsent(userId) { mutableSetOf() }.add(channelId)
            
            // 存储到Redis（用于跨网关实例的会话路由）
            val sessionKey = "ws_session:$userId:$channelId"
            val sessionData = mapOf(
                "userId" to userId,
                "username" to username,
                "channelId" to channelId,
                "gatewayId" to getGatewayId(),
                "connectedAt" to System.currentTimeMillis()
            )
            
            redisTemplate.opsForValue().set(
                sessionKey,
                objectMapper.writeValueAsString(sessionData),
                30,
                TimeUnit.MINUTES
            )
            
            // 更新在线状态
            updateOnlineStatus(userId, true)
            
            logger.info("用户 {} 建立WebSocket会话: {}", userId, channelId)
            callback(true, "连接成功", userId)
            
        } catch (e: Exception) {
            logger.error("创建WebSocket会话失败", e)
            callback(false, "连接失败: ${e.message}", null)
        }
    }
    
    /**
     * 移除会话
     */
    fun removeSession(channel: Channel) {
        val channelId = channel.id().asShortText()
        val session = localSessions.remove(channelId)
        
        if (session != null) {
            val userId = session.userId
            
            // 从用户会话映射中移除
            userSessions[userId]?.remove(channelId)
            if (userSessions[userId]?.isEmpty() == true) {
                userSessions.remove(userId)
                // 更新离线状态
                updateOnlineStatus(userId, false)
            }
            
            // 从Redis中移除
            val sessionKey = "ws_session:$userId:$channelId"
            redisTemplate.delete(sessionKey)
            
            logger.info("用户 {} 断开WebSocket会话: {}", userId, channelId)
        }
    }
    
    /**
     * 获取会话
     */
    fun getSession(channel: Channel): WebSocketSession? {
        val channelId = channel.id().asShortText()
        return localSessions[channelId]
    }
    
    /**
     * 根据用户ID获取会话
     */
    fun getSessionsByUserId(userId: Long): List<WebSocketSession> {
        val channelIds = userSessions[userId] ?: return emptyList()
        return channelIds.mapNotNull { localSessions[it] }
    }
    
    /**
     * 向用户发送消息
     */
    fun sendMessageToUser(userId: Long, message: String): Boolean {
        val sessions = getSessionsByUserId(userId)
        if (sessions.isEmpty()) {
            return false
        }
        
        sessions.forEach { session ->
            try {
                session.sendMessage(message)
            } catch (e: Exception) {
                logger.error("发送消息到用户 {} 失败", userId, e)
            }
        }
        
        return true
    }
    
    /**
     * 转发消息到消息服务
     */
    fun forwardMessage(session: WebSocketSession, frameData: Map<*, *>) {
        try {
            val payload = frameData["payload"] as? Map<*, *>
            val conversationId = payload?.get("conversationId") as? String
            val content = payload?.get("content") as? Map<*, *>
            
            if (conversationId.isNullOrBlank() || content == null) {
                throw IllegalArgumentException("消息格式不正确")
            }
            
            // 构造消息数据
            val messageData = mapOf(
                "senderId" to session.userId,
                "conversationId" to conversationId,
                "content" to content,
                "timestamp" to System.currentTimeMillis()
            )
            
            // 发送到消息队列（这里先用日志模拟，后续集成RocketMQ）
            logger.info("转发消息到消息服务: {}", objectMapper.writeValueAsString(messageData))
            
            // 发送确认
            val ack = mapOf(
                "type" to "MESSAGE_ACK",
                "payload" to mapOf(
                    "success" to true,
                    "messageId" to generateMessageId(),
                    "timestamp" to System.currentTimeMillis()
                )
            )
            
            session.sendMessage(objectMapper.writeValueAsString(ack))
            
        } catch (e: Exception) {
            logger.error("转发消息失败", e)
            
            // 发送错误确认
            val errorAck = mapOf(
                "type" to "MESSAGE_ACK",
                "payload" to mapOf(
                    "success" to false,
                    "error" to e.message,
                    "timestamp" to System.currentTimeMillis()
                )
            )
            
            session.sendMessage(objectMapper.writeValueAsString(errorAck))
        }
    }
    
    /**
     * 转发输入状态
     */
    fun forwardTypingStatus(session: WebSocketSession, frameData: Map<*, *>) {
        try {
            val payload = frameData["payload"] as? Map<*, *>
            val conversationId = payload?.get("conversationId") as? String
            val typing = payload?.get("typing") as? Boolean ?: false
            
            if (conversationId.isNullOrBlank()) {
                return
            }
            
            // 构造输入状态数据
            val typingData = mapOf(
                "userId" to session.userId,
                "conversationId" to conversationId,
                "typing" to typing,
                "timestamp" to System.currentTimeMillis()
            )
            
            logger.debug("转发输入状态: {}", objectMapper.writeValueAsString(typingData))
            
        } catch (e: Exception) {
            logger.error("转发输入状态失败", e)
        }
    }
    
    /**
     * 更新在线状态
     */
    private fun updateOnlineStatus(userId: Long, online: Boolean) {
        try {
            val statusKey = "user_online:$userId"
            if (online) {
                redisTemplate.opsForValue().set(statusKey, "true", 5, TimeUnit.MINUTES)
            } else {
                redisTemplate.delete(statusKey)
            }
        } catch (e: Exception) {
            logger.error("更新在线状态失败", e)
        }
    }
    
    /**
     * 生成会话ID
     */
    private fun generateSessionId(userId: Long, channelId: String): String {
        return "${userId}_${channelId}_${System.currentTimeMillis()}"
    }
    
    /**
     * 生成消息ID
     */
    private fun generateMessageId(): String {
        return "msg_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 获取网关ID
     */
    private fun getGatewayId(): String {
        return System.getProperty("gateway.id") ?: "gateway-${System.currentTimeMillis()}"
    }
    
    /**
     * 获取在线用户数量
     */
    fun getOnlineUserCount(): Int {
        return userSessions.size
    }
    
    /**
     * 获取连接数量
     */
    fun getConnectionCount(): Int {
        return localSessions.size
    }
}
