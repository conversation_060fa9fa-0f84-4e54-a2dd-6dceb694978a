server:
  port: 8080

spring:
  application:
    name: gateway-service
  
  profiles:
    active: dev
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
    
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      
      routes:
        # 用户服务路由
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/users/**,/api/v1/auth/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                redis-rate-limiter.requestedTokens: 1
        
        # 消息服务路由
        - id: message-service
          uri: lb://message-service
          predicates:
            - Path=/api/v1/messages/**,/api/v1/conversations/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 200
                redis-rate-limiter.burstCapacity: 400
                redis-rate-limiter.requestedTokens: 1
        
        # 社交服务路由
        - id: social-service
          uri: lb://social-service
          predicates:
            - Path=/api/v1/friends/**,/api/v1/groups/**
          filters:
            - StripPrefix=2
        
        # 媒体服务路由
        - id: media-service
          uri: lb://media-service
          predicates:
            - Path=/api/v1/media/**
          filters:
            - StripPrefix=2
        
        # 推送服务路由
        - id: push-service
          uri: lb://push-service
          predicates:
            - Path=/api/v1/push/**
          filters:
            - StripPrefix=2
        
        # 管理服务路由
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/api/v1/admin/**
          filters:
            - StripPrefix=2
        
        # 通知服务路由
        - id: notification-service
          uri: lb://notification-service
          predicates:
            - Path=/api/v1/notifications/**
          filters:
            - StripPrefix=2
      
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
        - AddResponseHeader=Access-Control-Allow-Origin, *
        - AddResponseHeader=Access-Control-Allow-Methods, GET,POST,PUT,DELETE,OPTIONS
        - AddResponseHeader=Access-Control-Allow-Headers, *
        - AddResponseHeader=Access-Control-Max-Age, 3600

  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# WebSocket配置
websocket:
  port: 9090
  boss-threads: 1
  worker-threads: 4
  max-connections: 10000
  idle-timeout: 60

# gRPC客户端配置
grpc:
  client:
    user-service:
      address: discovery:///user-service
      negotiationType: plaintext
    message-service:
      address: discovery:///message-service
      negotiationType: plaintext

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.zlim: DEBUG
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
