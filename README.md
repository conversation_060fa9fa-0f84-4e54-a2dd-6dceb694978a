# ZLIM - 微服务架构即时通讯系统

## 项目简介

ZLIM是一套基于Spring Boot 3.x和微服务架构的企业级即时通讯系统，支持单聊、群聊、媒体文件传输、推送通知等功能。

## 技术栈

### 核心框架
- **Spring Boot 3.3.2** - 微服务基础框架
- **Spring Cloud Gateway** - API网关
- **JDK 21** - Java运行环境
- **Gradle 8.x (Kotlin DSL)** - 构建工具
- **Protocol Buffers** - 服务间通信序列化
- **Netty** - WebSocket长连接实现

### 数据存储
- **PostgreSQL** - 主数据库
- **MyBatis-Plus** - ORM框架
- **Redis** - 缓存和会话存储
- **MinIO** - 对象存储服务

### 消息队列
- **Apache RocketMQ** - 消息队列

### 服务治理
- **Nacos** - 服务注册发现和配置管理

### 监控日志
- **ELK Stack** - 日志分析 (Elasticsearch + Logstash + Kibana)
- **Prometheus + Grafana** - 监控和可视化

### 容器化
- **Docker + Docker Compose** - 容器化部署
- **Undertow** - 嵌入式Web服务器

## 系统架构

### 微服务模块

1. **gateway-service** - API网关服务
   - 基于Netty实现WebSocket长连接
   - 负载均衡和路由
   - 统一鉴权和限流

2. **user-service** - 用户管理服务
   - 用户注册、登录、授权
   - 用户信息管理
   - JWT认证

3. **social-service** - 社交关系服务
   - 好友关系管理
   - 群组管理
   - 频道管理

4. **message-service** - 消息核心服务
   - 消息收发处理
   - 消息持久化存储
   - 消息搜索功能

5. **media-service** - 媒体文件服务
   - 文件上传下载
   - CDN内容分发
   - 多媒体文件处理

6. **push-service** - 消息推送服务
   - FCM (Firebase Cloud Messaging)
   - APNS (Apple Push Notification Service)
   - 国内厂商推送

7. **admin-service** - 管理后台服务
   - 运营管理界面
   - 内容审核和风控
   - 系统监控和统计

8. **notification-service** - 通知服务
   - 系统通知管理
   - 消息模板管理
   - 通知历史记录

### 消息流转架构

```
客户端A → Gateway-Service (WebSocket) → RocketMQ → Message-Service
                                                        ↓
                                                   消息存储(PostgreSQL)
                                                   消息缓存(Redis)
                                                        ↓
                                                   Push-Service
                                                        ↓
                                          Gateway-Service → 客户端B (WebSocket推送)
```

## 快速开始

### 环境要求

- JDK 21+
- Docker & Docker Compose
- Gradle 8.x

### 启动基础设施

```bash
# 启动所有基础设施服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 构建项目

```bash
# 构建所有模块
./gradlew build

# 构建指定模块
./gradlew :user-service:build
```

### 启动服务

```bash
# 启动用户服务
./gradlew :user-service:bootRun

# 启动网关服务
./gradlew :gateway-service:bootRun

# 启动消息服务
./gradlew :message-service:bootRun
```

## 开发计划

### 阶段1：基础设施和核心服务搭建 (第1-2周)
- [x] 项目结构初始化
- [x] 公共模块开发
- [x] Protocol Buffers消息定义
- [ ] Nacos服务注册发现配置
- [ ] Gateway-Service开发
- [ ] User-Service开发

### 阶段2：消息核心功能实现 (第3-4周)
- [ ] Message-Service开发
- [ ] WebSocket长连接实现
- [ ] RocketMQ消息队列集成
- [ ] Redis缓存集成

### 阶段3：社交和媒体功能开发 (第5-6周)
- [ ] Social-Service开发
- [ ] Media-Service开发
- [ ] Push-Service开发
- [ ] Notification-Service开发

### 阶段4：管理后台和运维部署 (第7-8周)
- [ ] Admin-Service开发
- [ ] ELK日志分析集成
- [ ] Docker容器化部署
- [ ] 测试和文档完善

## API文档

### 用户认证

```bash
# 用户注册
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>",
  "phone": "13800138000"
}

# 用户登录
POST /api/v1/auth/login
Content-Type: application/json

{
  "identifier": "testuser",
  "password": "password123",
  "loginType": "password"
}
```

### WebSocket连接

```javascript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:9090/ws');

// 发送认证消息
ws.send(JSON.stringify({
  type: 'CONNECT',
  payload: {
    accessToken: 'your-jwt-token'
  }
}));

// 发送聊天消息
ws.send(JSON.stringify({
  type: 'MESSAGE',
  payload: {
    conversationId: 'conv_123',
    content: {
      text: 'Hello World!'
    }
  }
}));
```

## 配置说明

### 数据库配置

```yaml
spring:
  datasource:
    url: ******************************************
    username: zlim
    password: zlim123
```

### Redis配置

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
```

### Nacos配置

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
```

## 监控和运维

### 服务监控

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

### 日志分析

- **Kibana**: http://localhost:5601

### 服务管理

- **Nacos Console**: http://localhost:8848/nacos
- **RocketMQ Console**: http://localhost:8180

### 对象存储

- **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin123)

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: ZLIM Team
- 邮箱: <EMAIL>
- 项目地址: https://github.com/zlim/zlim
