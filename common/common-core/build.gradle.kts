dependencies {
    api(project(":common:common-proto"))

    // Spring Boot核心依赖
    api("org.springframework.boot:spring-boot-starter-web:3.3.2") {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-tomcat")
    }
    api("org.springframework.boot:spring-boot-starter-undertow:3.3.2")
    api("org.springframework.boot:spring-boot-starter-data-jpa:3.3.2")
    api("org.springframework.boot:spring-boot-starter-data-redis:3.3.2")

    // MyBatis-Plus
    api("com.baomidou:mybatis-plus-boot-starter:${property("mybatisPlusVersion")}")
    api("com.baomidou:mybatis-plus-generator:${property("mybatisPlusVersion")}")

    // 数据库驱动
    api("org.postgresql:postgresql:42.7.3")
    api("com.zaxxer:HikariCP:5.1.0")

    // Redis客户端
    api("org.redisson:redisson-spring-boot-starter:${property("redissonVersion")}")

    // RocketMQ
    api("org.apache.rocketmq:rocketmq-spring-boot-starter:2.3.0")

    // JWT
    api("io.jsonwebtoken:jjwt-api:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-impl:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-jackson:${property("jjwtVersion")}")

    // 数据库迁移
    api("org.flywaydb:flyway-core:${property("flywayVersion")}")
    api("org.flywaydb:flyway-database-postgresql:${property("flywayVersion")}")

    // 工具类
    api("org.apache.commons:commons-lang3:3.14.0")
    api("org.apache.commons:commons-collections4:4.4")
    api("cn.hutool:hutool-all:5.8.29")

    // 验证
    api("org.springframework.boot:spring-boot-starter-validation:3.3.2")

    // JSON处理
    api("com.fasterxml.jackson.core:jackson-databind:2.17.2")
    api("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2")
    api("com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2")
}
