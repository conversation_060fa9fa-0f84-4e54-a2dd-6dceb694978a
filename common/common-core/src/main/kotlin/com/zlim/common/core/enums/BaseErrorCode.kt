package com.zlim.common.core.enums

/**
 * 错误码基础接口
 * 错误码格式：XXYYYY
 * XX: 模块代码 (00-99)
 * YYYY: 具体错误代码 (0001-9999)
 */
interface BaseErrorCode {
    /**
     * 错误码
     */
    val code: Int
    
    /**
     * 国际化消息键
     */
    val messageKey: String
    
    /**
     * 默认消息（英文）
     */
    val defaultMessage: String
    
    /**
     * 获取HTTP状态码
     */
    fun getHttpStatus(): Int {
        return when (code / 1000) {
            200 -> 200 // 成功
            400, 404, 405, 409, 410, 411, 412 -> 400 // 客户端错误
            401 -> 401 // 认证错误
            402 -> 401 // 认证相关错误
            403, 408 -> 403 // 权限错误
            429 -> 429 // 限流
            else -> 500 // 服务器错误
        }
    }
}

/**
 * 通用错误码 (00xxxx)
 */
enum class CommonErrorCode(
    override val code: Int,
    override val messageKey: String,
    override val defaultMessage: String
) : BaseErrorCode {
    
    // 成功
    SUCCESS(200000, "common.success", "Success"),
    
    // 系统错误
    SYSTEM_ERROR(500000, "common.system.error", "Internal server error"),
    SYSTEM_BUSY(500001, "common.system.busy", "System is busy, please try again later"),
    SYSTEM_MAINTENANCE(500002, "common.system.maintenance", "System is under maintenance"),
    
    // 参数错误
    PARAM_INVALID(400000, "common.param.invalid", "Invalid parameter"),
    PARAM_MISSING(400001, "common.param.missing", "Missing required parameter"),
    PARAM_FORMAT_ERROR(400002, "common.param.format.error", "Parameter format error"),
    PARAM_OUT_OF_RANGE(400003, "common.param.out.of.range", "Parameter out of range"),
    
    // 请求错误
    REQUEST_METHOD_NOT_SUPPORTED(405000, "common.request.method.not.supported", "Request method not supported"),
    MEDIA_TYPE_NOT_SUPPORTED(415000, "common.media.type.not.supported", "Media type not supported"),
    REQUEST_TIMEOUT(408000, "common.request.timeout", "Request timeout"),
    
    // 资源错误
    RESOURCE_NOT_FOUND(404000, "common.resource.not.found", "Resource not found"),
    RESOURCE_ALREADY_EXISTS(409000, "common.resource.already.exists", "Resource already exists"),
    RESOURCE_LOCKED(423000, "common.resource.locked", "Resource is locked"),
    
    // 权限错误
    ACCESS_DENIED(403000, "common.access.denied", "Access denied"),
    OPERATION_NOT_ALLOWED(403001, "common.operation.not.allowed", "Operation not allowed"),
    
    // 限流错误
    RATE_LIMIT_EXCEEDED(429000, "common.rate.limit.exceeded", "Rate limit exceeded"),
    CONCURRENT_LIMIT_EXCEEDED(429001, "common.concurrent.limit.exceeded", "Concurrent limit exceeded"),
    
    // 数据错误
    DATA_NOT_FOUND(404001, "common.data.not.found", "Data not found"),
    DATA_ALREADY_EXISTS(409001, "common.data.already.exists", "Data already exists"),
    DATA_INTEGRITY_VIOLATION(400004, "common.data.integrity.violation", "Data integrity violation"),
    DUPLICATE_KEY_ERROR(400005, "common.duplicate.key", "Duplicate key error"),
    OPTIMISTIC_LOCK_ERROR(409002, "common.optimistic.lock", "Data has been modified by another user"),
    
    // 网络错误
    NETWORK_ERROR(500003, "common.network.error", "Network error"),
    CONNECTION_TIMEOUT(500004, "common.connection.timeout", "Connection timeout"),
    SERVICE_UNAVAILABLE(503000, "common.service.unavailable", "Service unavailable");
}

/**
 * 错误码工厂类
 */
object ErrorCodeFactory {
    
    private val errorCodeMap = mutableMapOf<Int, BaseErrorCode>()
    
    init {
        // 注册通用错误码
        CommonErrorCode.values().forEach { errorCode ->
            errorCodeMap[errorCode.code] = errorCode
        }
    }
    
    /**
     * 注册错误码
     */
    fun register(errorCodes: Array<out BaseErrorCode>) {
        errorCodes.forEach { errorCode ->
            errorCodeMap[errorCode.code] = errorCode
        }
    }
    
    /**
     * 根据错误码获取错误信息
     */
    fun getErrorCode(code: Int): BaseErrorCode? {
        return errorCodeMap[code]
    }
    
    /**
     * 检查错误码是否已注册
     */
    fun isRegistered(code: Int): Boolean {
        return errorCodeMap.containsKey(code)
    }
    
    /**
     * 获取所有已注册的错误码
     */
    fun getAllErrorCodes(): Map<Int, BaseErrorCode> {
        return errorCodeMap.toMap()
    }
}
