package com.zlim.common.core.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.support.ResourceBundleMessageSource
import java.util.*

/**
 * 国际化配置（核心部分）
 */
@Configuration
class I18nConfig {
    
    /**
     * 消息源配置
     */
    @Bean
    fun messageSource(): ResourceBundleMessageSource {
        val messageSource = ResourceBundleMessageSource()
        messageSource.setBasenames("i18n/messages", "i18n/errors")
        messageSource.setDefaultEncoding("UTF-8")
        messageSource.setDefaultLocale(Locale.ENGLISH)
        messageSource.setCacheSeconds(3600)
        return messageSource
    }
    

}
