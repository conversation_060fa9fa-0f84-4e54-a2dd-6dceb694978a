package com.zlim.common.core.util

import com.zlim.common.core.enums.BaseErrorCode
import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.stereotype.Component
import java.util.*

/**
 * 国际化消息工具类
 */
@Component
class MessageUtils(
    private val messageSource: MessageSource
) {
    
    companion object {
        private lateinit var instance: MessageUtils
        
        /**
         * 获取国际化消息
         */
        fun getMessage(code: String, args: Array<Any>? = null, locale: Locale? = null): String {
            return instance.getI18nMessage(code, args, locale)
        }
        
        /**
         * 获取错误码对应的国际化消息
         */
        fun getMessage(errorCode: BaseErrorCode, args: Array<Any>? = null, locale: Locale? = null): String {
            return instance.getI18nMessage(errorCode.messageKey, args, locale).takeIf { it != errorCode.messageKey }
                ?: errorCode.defaultMessage
        }
        
        /**
         * 获取当前语言环境的消息
         */
        fun getMessage(code: String, vararg args: Any): String {
            return instance.getI18nMessage(code, args)
        }
    }
    
    init {
        instance = this
    }
    
    /**
     * 获取国际化消息
     */
    fun getI18nMessage(code: String, args: Array<Any>? = null, locale: Locale? = null): String {
        return try {
            val currentLocale = locale ?: LocaleContextHolder.getLocale()
            messageSource.getMessage(code, args, currentLocale)
        } catch (e: Exception) {
            code
        }
    }
    
    /**
     * 获取国际化消息（可变参数）
     */
    fun getI18nMessage(code: String, vararg args: Any): String {
        return getI18nMessage(code, args)
    }
    
    /**
     * 获取错误码消息
     */
    fun getErrorMessage(errorCode: BaseErrorCode, args: Array<Any>? = null, locale: Locale? = null): String {
        val message = getI18nMessage(errorCode.messageKey, args, locale)
        return if (message == errorCode.messageKey) {
            errorCode.defaultMessage
        } else {
            message
        }
    }
    
    /**
     * 检查消息是否存在
     */
    fun hasMessage(code: String, locale: Locale? = null): Boolean {
        return try {
            val currentLocale = locale ?: LocaleContextHolder.getLocale()
            messageSource.getMessage(code, null, currentLocale)
            true
        } catch (e: Exception) {
            false
        }
    }
}
