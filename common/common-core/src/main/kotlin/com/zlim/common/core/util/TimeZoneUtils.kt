package com.zlim.common.core.util

import java.time.*
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 时区处理工具类
 */
object TimeZoneUtils {
    
    /**
     * 默认时区（UTC）
     */
    val DEFAULT_ZONE: ZoneId = ZoneId.of("UTC")
    
    /**
     * 常用时区
     */
    object CommonZones {
        val UTC = ZoneId.of("UTC")
        val BEIJING = ZoneId.of("Asia/Shanghai")
        val TOKYO = ZoneId.of("Asia/Tokyo")
        val NEW_YORK = ZoneId.of("America/New_York")
        val LONDON = ZoneId.of("Europe/London")
        val PARIS = ZoneId.of("Europe/Paris")
    }
    
    /**
     * ISO 8601格式化器
     */
    val ISO_FORMATTER: DateTimeFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME
    
    /**
     * 获取当前UTC时间
     */
    fun nowUtc(): ZonedDateTime {
        return ZonedDateTime.now(DEFAULT_ZONE)
    }
    
    /**
     * 获取当前UTC时间戳（毫秒）
     */
    fun nowUtcMillis(): Long {
        return Instant.now().toEpochMilli()
    }
    
    /**
     * 获取当前UTC时间戳（秒）
     */
    fun nowUtcSeconds(): Long {
        return Instant.now().epochSecond
    }
    
    /**
     * 将LocalDateTime转换为UTC时间戳（毫秒）
     */
    fun toUtcMillis(localDateTime: LocalDateTime, zoneId: ZoneId = DEFAULT_ZONE): Long {
        return localDateTime.atZone(zoneId).toInstant().toEpochMilli()
    }
    
    /**
     * 将UTC时间戳转换为LocalDateTime
     */
    fun fromUtcMillis(millis: Long, zoneId: ZoneId = DEFAULT_ZONE): LocalDateTime {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), zoneId)
    }
    
    /**
     * 将UTC时间戳转换为ZonedDateTime
     */
    fun fromUtcMillisToZoned(millis: Long, zoneId: ZoneId): ZonedDateTime {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(millis), zoneId)
    }
    
    /**
     * 转换时区
     */
    fun convertTimeZone(dateTime: ZonedDateTime, targetZone: ZoneId): ZonedDateTime {
        return dateTime.withZoneSameInstant(targetZone)
    }
    
    /**
     * 格式化为ISO 8601字符串
     */
    fun formatToIso(dateTime: ZonedDateTime): String {
        return dateTime.format(ISO_FORMATTER)
    }
    
    /**
     * 从ISO 8601字符串解析
     */
    fun parseFromIso(isoString: String): ZonedDateTime {
        return ZonedDateTime.parse(isoString, ISO_FORMATTER)
    }
    
    /**
     * 获取时区偏移量（分钟）
     */
    fun getOffsetMinutes(zoneId: ZoneId, instant: Instant = Instant.now()): Int {
        return zoneId.rules.getOffset(instant).totalSeconds / 60
    }
    
    /**
     * 获取时区偏移量字符串（如：+08:00）
     */
    fun getOffsetString(zoneId: ZoneId, instant: Instant = Instant.now()): String {
        return zoneId.rules.getOffset(instant).toString()
    }
    
    /**
     * 检查是否为夏令时
     */
    fun isDaylightSaving(zoneId: ZoneId, instant: Instant = Instant.now()): Boolean {
        return zoneId.rules.isDaylightSavings(instant)
    }
    
    /**
     * 获取用户时区（从请求头或用户设置）
     */
    fun getUserTimeZone(timeZoneHeader: String?): ZoneId {
        return try {
            if (!timeZoneHeader.isNullOrBlank()) {
                ZoneId.of(timeZoneHeader)
            } else {
                DEFAULT_ZONE
            }
        } catch (e: Exception) {
            DEFAULT_ZONE
        }
    }
    
    /**
     * 时间范围查询辅助类
     */
    data class TimeRange(
        val start: ZonedDateTime,
        val end: ZonedDateTime
    ) {
        fun toUtcRange(): Pair<Long, Long> {
            return Pair(
                start.toInstant().toEpochMilli(),
                end.toInstant().toEpochMilli()
            )
        }
    }
    
    /**
     * 创建今天的时间范围（用户时区）
     */
    fun todayRange(userZone: ZoneId): TimeRange {
        val now = ZonedDateTime.now(userZone)
        val startOfDay = now.toLocalDate().atStartOfDay(userZone)
        val endOfDay = startOfDay.plusDays(1).minusNanos(1)
        return TimeRange(startOfDay, endOfDay)
    }
    
    /**
     * 创建本周的时间范围（用户时区）
     */
    fun thisWeekRange(userZone: ZoneId): TimeRange {
        val now = ZonedDateTime.now(userZone)
        val startOfWeek = now.toLocalDate()
            .with(DayOfWeek.MONDAY)
            .atStartOfDay(userZone)
        val endOfWeek = startOfWeek.plusWeeks(1).minusNanos(1)
        return TimeRange(startOfWeek, endOfWeek)
    }
    
    /**
     * 创建本月的时间范围（用户时区）
     */
    fun thisMonthRange(userZone: ZoneId): TimeRange {
        val now = ZonedDateTime.now(userZone)
        val startOfMonth = now.toLocalDate()
            .withDayOfMonth(1)
            .atStartOfDay(userZone)
        val endOfMonth = startOfMonth.plusMonths(1).minusNanos(1)
        return TimeRange(startOfMonth, endOfMonth)
    }
    
    /**
     * 格式化相对时间（如：2小时前、昨天、上周等）
     */
    fun formatRelativeTime(dateTime: ZonedDateTime, userZone: ZoneId, locale: Locale = Locale.getDefault()): String {
        val now = ZonedDateTime.now(userZone)
        val userDateTime = dateTime.withZoneSameInstant(userZone)
        
        val duration = Duration.between(userDateTime, now)
        val days = duration.toDays()
        val hours = duration.toHours()
        val minutes = duration.toMinutes()
        
        return when {
            minutes < 1 -> "刚刚"
            minutes < 60 -> "${minutes}分钟前"
            hours < 24 -> "${hours}小时前"
            days == 1L -> "昨天"
            days < 7 -> "${days}天前"
            days < 30 -> "${days / 7}周前"
            days < 365 -> "${days / 30}个月前"
            else -> "${days / 365}年前"
        }
    }
    
    /**
     * 验证时区ID是否有效
     */
    fun isValidTimeZone(timeZoneId: String): Boolean {
        return try {
            ZoneId.of(timeZoneId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取所有可用时区
     */
    fun getAvailableTimeZones(): Set<String> {
        return ZoneId.getAvailableZoneIds()
    }
}
