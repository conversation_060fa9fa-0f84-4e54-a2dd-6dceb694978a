package com.zlim.common.core.exception

import com.zlim.common.core.enums.BaseErrorCode
import com.zlim.common.core.enums.CommonErrorCode

/**
 * 业务异常
 */
class BusinessException : RuntimeException {

    val errorCode: BaseErrorCode
    val args: Array<Any>?

    constructor(
        errorCode: BaseErrorCode,
        args: Array<Any>? = null,
        cause: Throwable? = null
    ) : super(errorCode.defaultMessage, cause) {
        this.errorCode = errorCode
        this.args = args
    }

    constructor(
        message: String,
        cause: Throwable? = null
    ) : super(message, cause) {
        this.errorCode = CommonErrorCode.SYSTEM_ERROR
        this.args = null
    }

    constructor(message: String) : this(message, null)

    /**
     * 获取错误码
     */
    fun getCode(): Int = errorCode.code

    /**
     * 获取消息键
     */
    fun getMessageKey(): String = errorCode.messageKey
}

/**
 * 认证异常
 */
class AuthException(
    override val message: String = "认证失败",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 权限异常
 */
class PermissionException(
    override val message: String = "权限不足",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 资源不存在异常
 */
class NotFoundException(
    override val message: String = "资源不存在",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

/**
 * 参数验证异常
 */
class ValidationException(
    override val message: String = "参数验证失败",
    override val cause: Throwable? = null
) : RuntimeException(message, cause)
