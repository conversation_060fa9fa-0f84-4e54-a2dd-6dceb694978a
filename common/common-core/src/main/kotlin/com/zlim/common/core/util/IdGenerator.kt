package com.zlim.common.core.util

import java.time.Instant
import java.util.concurrent.atomic.AtomicLong
import kotlin.random.Random

/**
 * ID生成器
 * 基于雪花算法的分布式ID生成器
 */
object IdGenerator {
    
    // 起始时间戳 (2024-01-01 00:00:00 UTC)
    private const val EPOCH = 1704067200000L
    
    // 机器ID位数
    private const val MACHINE_ID_BITS = 10L
    
    // 序列号位数
    private const val SEQUENCE_BITS = 12L
    
    // 最大机器ID
    private const val MAX_MACHINE_ID = (1L shl MACHINE_ID_BITS.toInt()) - 1
    
    // 最大序列号
    private const val MAX_SEQUENCE = (1L shl SEQUENCE_BITS.toInt()) - 1
    
    // 时间戳左移位数
    private const val TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS
    
    // 机器ID左移位数
    private const val MACHINE_ID_LEFT_SHIFT = SEQUENCE_BITS
    
    // 机器ID (可以从配置文件或环境变量获取)
    private val machineId: Long = System.getProperty("machine.id")?.toLongOrNull() 
        ?: (System.currentTimeMillis() % MAX_MACHINE_ID)
    
    // 序列号
    private val sequence = AtomicLong(0L)
    
    // 上次生成ID的时间戳
    @Volatile
    private var lastTimestamp = -1L
    
    init {
        if (machineId > MAX_MACHINE_ID || machineId < 0) {
            throw IllegalArgumentException("Machine ID must be between 0 and $MAX_MACHINE_ID")
        }
    }
    
    /**
     * 生成下一个ID
     */
    @Synchronized
    fun nextId(): Long {
        var timestamp = System.currentTimeMillis()
        
        // 时钟回拨检查
        if (timestamp < lastTimestamp) {
            throw RuntimeException("Clock moved backwards. Refusing to generate id for ${lastTimestamp - timestamp} milliseconds")
        }
        
        // 同一毫秒内，序列号递增
        if (timestamp == lastTimestamp) {
            val seq = sequence.incrementAndGet() and MAX_SEQUENCE
            if (seq == 0L) {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp)
                sequence.set(0L)
            }
        } else {
            // 不同毫秒，序列号重置
            sequence.set(0L)
        }
        
        lastTimestamp = timestamp
        
        // 组装ID
        return ((timestamp - EPOCH) shl TIMESTAMP_LEFT_SHIFT.toInt()) or
                (machineId shl MACHINE_ID_LEFT_SHIFT.toInt()) or
                sequence.get()
    }
    
    /**
     * 等待下一毫秒
     */
    private fun waitNextMillis(lastTimestamp: Long): Long {
        var timestamp = System.currentTimeMillis()
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis()
        }
        return timestamp
    }
    
    /**
     * 生成消息ID
     * 格式: {userId}_{timestamp}_{random}
     */
    fun generateMessageId(userId: Long): String {
        val timestamp = System.currentTimeMillis()
        val random = Random.nextInt(10000, 99999)
        return "${userId}_${timestamp}_${random}"
    }
    
    /**
     * 生成会话ID
     * 格式: 私聊 - {smallerId}_{largerId}
     *      群聊 - group_{groupId}
     */
    fun generateConversationId(userId1: Long, userId2: Long): String {
        return if (userId1 < userId2) {
            "${userId1}_${userId2}"
        } else {
            "${userId2}_${userId1}"
        }
    }
    
    /**
     * 生成群聊会话ID
     */
    fun generateGroupConversationId(groupId: Long): String {
        return "group_${groupId}"
    }
    
    /**
     * 生成UUID字符串
     */
    fun generateUuid(): String {
        return java.util.UUID.randomUUID().toString().replace("-", "")
    }
}
