dependencies {
    api(project(":common:common-core"))
    api(project(":common:common-proto"))
    
    // Spring Boot Web
    api("org.springframework.boot:spring-boot-starter-web")
    
    // Protobuf HTTP支持
    api("org.springframework:spring-webmvc")
    api("com.google.protobuf:protobuf-java:${property("protobufVersion")}")
    api("com.google.protobuf:protobuf-java-util:${property("protobufVersion")}")
    
    // 全局异常处理
    api("org.springframework.boot:spring-boot-starter-validation")
    
    // OpenAPI文档
    api("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0")
}
