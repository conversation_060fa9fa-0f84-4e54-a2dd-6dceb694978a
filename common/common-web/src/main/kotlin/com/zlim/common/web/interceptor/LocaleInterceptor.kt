package com.zlim.common.web.interceptor

import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import java.util.*

/**
 * 语言环境拦截器
 * 从HTTP头部检测用户语言偏好
 */
@Component
class LocaleInterceptor : HandlerInterceptor {
    
    companion object {
        private const val ACCEPT_LANGUAGE_HEADER = "Accept-Language"
        private const val X_LANGUAGE_HEADER = "X-Language"
        private const val LANG_PARAM = "lang"
        
        // 支持的语言列表
        private val SUPPORTED_LOCALES = setOf(
            Locale.ENGLISH,
            Locale.CHINESE,
            Locale.SIMPLIFIED_CHINESE,
            Locale.TRADITIONAL_CHINESE
        )
    }
    
    override fun preHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any
    ): Boolean {
        val locale = resolveLocale(request)
        LocaleContextHolder.setLocale(locale)
        
        // 在响应头中添加当前语言信息
        response.setHeader("Content-Language", locale.toLanguageTag())
        
        return true
    }
    
    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        // 清理ThreadLocal
        LocaleContextHolder.resetLocaleContext()
    }
    
    /**
     * 解析用户语言偏好
     * 优先级：URL参数 > X-Language头 > Accept-Language头 > 默认语言
     */
    private fun resolveLocale(request: HttpServletRequest): Locale {
        // 1. 检查URL参数
        val langParam = request.getParameter(LANG_PARAM)
        if (!langParam.isNullOrBlank()) {
            val locale = parseLocale(langParam)
            if (locale != null && isSupported(locale)) {
                return locale
            }
        }
        
        // 2. 检查X-Language头
        val xLanguageHeader = request.getHeader(X_LANGUAGE_HEADER)
        if (!xLanguageHeader.isNullOrBlank()) {
            val locale = parseLocale(xLanguageHeader)
            if (locale != null && isSupported(locale)) {
                return locale
            }
        }
        
        // 3. 检查Accept-Language头
        val acceptLanguageHeader = request.getHeader(ACCEPT_LANGUAGE_HEADER)
        if (!acceptLanguageHeader.isNullOrBlank()) {
            val locale = parseAcceptLanguage(acceptLanguageHeader)
            if (locale != null && isSupported(locale)) {
                return locale
            }
        }
        
        // 4. 返回默认语言
        return Locale.ENGLISH
    }
    
    /**
     * 解析语言字符串为Locale
     */
    private fun parseLocale(languageTag: String): Locale? {
        return try {
            when (languageTag.lowercase()) {
                "zh", "zh-cn", "zh_cn" -> Locale.SIMPLIFIED_CHINESE
                "zh-tw", "zh_tw", "zh-hk", "zh_hk" -> Locale.TRADITIONAL_CHINESE
                "en", "en-us", "en_us" -> Locale.ENGLISH
                else -> Locale.forLanguageTag(languageTag)
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 解析Accept-Language头
     * 格式：zh-CN,zh;q=0.9,en;q=0.8
     */
    private fun parseAcceptLanguage(acceptLanguage: String): Locale? {
        val languages = acceptLanguage.split(",")
            .map { it.trim() }
            .map { lang ->
                val parts = lang.split(";")
                val languageTag = parts[0].trim()
                val quality = if (parts.size > 1) {
                    parts[1].substringAfter("q=").toDoubleOrNull() ?: 1.0
                } else {
                    1.0
                }
                Pair(languageTag, quality)
            }
            .sortedByDescending { it.second }
        
        for ((languageTag, _) in languages) {
            val locale = parseLocale(languageTag)
            if (locale != null && isSupported(locale)) {
                return locale
            }
        }
        
        return null
    }
    
    /**
     * 检查语言是否被支持
     */
    private fun isSupported(locale: Locale): Boolean {
        return SUPPORTED_LOCALES.any { supportedLocale ->
            supportedLocale.language == locale.language &&
            (supportedLocale.country.isEmpty() || supportedLocale.country == locale.country)
        }
    }
}

/**
 * 语言环境工具类
 */
object LocaleUtils {
    
    /**
     * 获取当前语言标签
     */
    fun getCurrentLanguageTag(): String {
        val locale = LocaleContextHolder.getLocale()
        return when {
            locale.language == "zh" && locale.country == "TW" -> "zh-TW"
            locale.language == "zh" -> "zh-CN"
            locale.language == "en" -> "en-US"
            else -> locale.toLanguageTag()
        }
    }
    
    /**
     * 获取当前语言显示名称
     */
    fun getCurrentLanguageDisplayName(): String {
        val locale = LocaleContextHolder.getLocale()
        return when {
            locale.language == "zh" && locale.country == "TW" -> "繁體中文"
            locale.language == "zh" -> "简体中文"
            locale.language == "en" -> "English"
            else -> locale.displayName
        }
    }
    
    /**
     * 检查是否为中文环境
     */
    fun isChinese(): Boolean {
        val locale = LocaleContextHolder.getLocale()
        return locale.language == "zh"
    }
    
    /**
     * 检查是否为英文环境
     */
    fun isEnglish(): Boolean {
        val locale = LocaleContextHolder.getLocale()
        return locale.language == "en"
    }
}
