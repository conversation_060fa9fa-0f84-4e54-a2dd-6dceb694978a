package com.zlim.common.web.exception

import com.zlim.common.core.exception.*
import com.zlim.common.core.result.Result
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import jakarta.validation.ConstraintViolationException

/**
 * 全局异常处理器
 */
@RestControllerAdvice
class GlobalExceptionHandler {
    
    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)
    
    /**
     * 业务异常
     */
    @ExceptionHandler(BusinessException::class)
    fun handleBusinessException(e: BusinessException): ResponseEntity<Result<Nothing>> {
        logger.warn("业务异常: {}", e.message)
        return ResponseEntity.ok(Result.error(e.code, e.message))
    }
    
    /**
     * 认证异常
     */
    @ExceptionHandler(AuthException::class)
    fun handleAuthException(e: AuthException): ResponseEntity<Result<Nothing>> {
        logger.warn("认证异常: {}", e.message)
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(Result.authError(e.message))
    }
    
    /**
     * 权限异常
     */
    @ExceptionHandler(PermissionException::class)
    fun handlePermissionException(e: PermissionException): ResponseEntity<Result<Nothing>> {
        logger.warn("权限异常: {}", e.message)
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
            .body(Result.permissionError(e.message))
    }
    
    /**
     * 资源不存在异常
     */
    @ExceptionHandler(NotFoundException::class)
    fun handleNotFoundException(e: NotFoundException): ResponseEntity<Result<Nothing>> {
        logger.warn("资源不存在: {}", e.message)
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(Result.notFound(e.message))
    }
    
    /**
     * 参数验证异常
     */
    @ExceptionHandler(ValidationException::class)
    fun handleValidationException(e: ValidationException): ResponseEntity<Result<Nothing>> {
        logger.warn("参数验证异常: {}", e.message)
        return ResponseEntity.badRequest()
            .body(Result.businessError(e.message))
    }
    
    /**
     * 方法参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleMethodArgumentNotValidException(e: MethodArgumentNotValidException): ResponseEntity<Result<Nothing>> {
        val message = e.bindingResult.fieldErrors
            .joinToString("; ") { "${it.field}: ${it.defaultMessage}" }
        logger.warn("参数验证失败: {}", message)
        return ResponseEntity.badRequest()
            .body(Result.businessError("参数验证失败: $message"))
    }
    
    /**
     * 绑定异常
     */
    @ExceptionHandler(BindException::class)
    fun handleBindException(e: BindException): ResponseEntity<Result<Nothing>> {
        val message = e.bindingResult.fieldErrors
            .joinToString("; ") { "${it.field}: ${it.defaultMessage}" }
        logger.warn("参数绑定失败: {}", message)
        return ResponseEntity.badRequest()
            .body(Result.businessError("参数绑定失败: $message"))
    }
    
    /**
     * 约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(e: ConstraintViolationException): ResponseEntity<Result<Nothing>> {
        val message = e.constraintViolations
            .joinToString("; ") { "${it.propertyPath}: ${it.message}" }
        logger.warn("约束违反: {}", message)
        return ResponseEntity.badRequest()
            .body(Result.businessError("约束违反: $message"))
    }
    
    /**
     * 通用异常
     */
    @ExceptionHandler(Exception::class)
    fun handleException(e: Exception): ResponseEntity<Result<Nothing>> {
        logger.error("系统异常", e)
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Result.serverError("系统内部错误"))
    }
}
