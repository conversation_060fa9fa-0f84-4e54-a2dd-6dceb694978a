package com.zlim.common.web.config

import com.zlim.common.web.interceptor.LocaleInterceptor
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.LocaleResolver
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor
import java.util.*

/**
 * Web国际化配置
 */
@Configuration
class WebI18nConfig(
    private val localeInterceptor: LocaleInterceptor
) : WebMvcConfigurer {
    
    /**
     * 区域解析器配置
     */
    @Bean
    fun localeResolver(): LocaleResolver {
        val resolver = AcceptHeaderLocaleResolver()
        resolver.defaultLocale = Locale.ENGLISH
        resolver.supportedLocales = listOf(
            Locale.ENGLISH,
            Locale.CHINESE,
            Locale.SIMPLIFIED_CHINESE,
            Locale.TRADITIONAL_CHINESE
        )
        return resolver
    }
    
    /**
     * 区域变更拦截器
     */
    @Bean
    fun localeChangeInterceptor(): LocaleChangeInterceptor {
        val interceptor = LocaleChangeInterceptor()
        interceptor.paramName = "lang"
        return interceptor
    }
    
    /**
     * 添加拦截器
     */
    override fun addInterceptors(registry: InterceptorRegistry) {
        // 添加语言环境拦截器
        registry.addInterceptor(localeInterceptor)
            .addPathPatterns("/**")
            .excludePathPatterns("/actuator/**", "/error")
        
        // 添加语言变更拦截器
        registry.addInterceptor(localeChangeInterceptor())
    }
}
