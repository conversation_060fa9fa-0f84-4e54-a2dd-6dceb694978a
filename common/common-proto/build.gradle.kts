plugins {
    id("com.google.protobuf")
}

dependencies {
    api("com.google.protobuf:protobuf-java:${property("protobufVersion")}")
    api("com.google.protobuf:protobuf-java-util:${property("protobufVersion")}")
    api("io.grpc:grpc-stub:${property("grpcVersion")}")
    api("io.grpc:grpc-protobuf:${property("grpcVersion")}")
    api("io.grpc:grpc-netty-shaded:${property("grpcVersion")}")

    // gRPC Kotlin支持
    api("io.grpc:grpc-kotlin-stub:1.4.1")

    // Kotlin协程支持
    api("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.1")

    // 注解支持
    compileOnly("org.apache.tomcat:annotations-api:6.0.53")
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${property("protobufVersion")}"
    }
    plugins {
        create("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${property("grpcVersion")}"
        }
        create("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:1.4.1:jdk8@jar"
        }
    }
    generateProtoTasks {
        all().forEach { task ->
            task.plugins {
                create("grpc")
                create("grpckt")
            }
        }
    }
}

sourceSets {
    main {
        proto {
            srcDir("src/main/proto")
        }
    }
}

// 处理重复文件
tasks.withType<ProcessResources> {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
