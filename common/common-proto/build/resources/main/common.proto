syntax = "proto3";

package com.zlim.common;

option java_package = "com.zlim.common.proto";
option java_outer_classname = "CommonProto";
option java_multiple_files = true;

// 通用响应结果
message Result {
  int32 code = 1;
  string message = 2;
  google.protobuf.Any data = 3;
  int64 timestamp = 4;
}

// 分页请求
message PageRequest {
  int32 page = 1;
  int32 size = 2;
  string sort = 3;
  string order = 4; // ASC, DESC
}

// 分页响应
message PageResponse {
  int64 total = 1;
  int32 page = 2;
  int32 size = 3;
  repeated google.protobuf.Any content = 4;
}

// 用户基本信息
message UserInfo {
  int64 user_id = 1;
  string username = 2;
  string nickname = 3;
  string avatar = 4;
  string email = 5;
  string phone = 6;
  UserStatus status = 7;
  int64 created_at = 8;
  int64 updated_at = 9;
}

// 用户状态枚举
enum UserStatus {
  USER_STATUS_UNKNOWN = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_BANNED = 3;
  USER_STATUS_DELETED = 4;
}

// 设备信息
message DeviceInfo {
  string device_id = 1;
  string device_type = 2; // ios, android, web, desktop
  string app_version = 3;
  string os_version = 4;
  string push_token = 5;
}

import "google/protobuf/any.proto";
