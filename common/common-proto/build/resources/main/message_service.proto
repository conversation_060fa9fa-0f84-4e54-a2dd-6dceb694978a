syntax = "proto3";

package com.zlim.message;

option java_package = "com.zlim.message.proto";
option java_outer_classname = "MessageServiceProto";
option java_multiple_files = true;

import "common.proto";
import "message.proto";

// 消息服务gRPC接口
service MessageService {
  // 发送消息
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  
  // 获取消息历史
  rpc GetMessageHistory(GetMessageHistoryRequest) returns (GetMessageHistoryResponse);
  
  // 标记消息已读
  rpc MarkMessageRead(MarkMessageReadRequest) returns (MarkMessageReadResponse);
  
  // 撤回消息
  rpc RecallMessage(RecallMessageRequest) returns (RecallMessageResponse);
  
  // 删除消息
  rpc DeleteMessage(DeleteMessageRequest) returns (DeleteMessageResponse);
  
  // 搜索消息
  rpc SearchMessage(SearchMessageRequest) returns (SearchMessageResponse);
  
  // 获取会话列表
  rpc GetConversations(GetConversationsRequest) returns (GetConversationsResponse);
  
  // 推送消息到网关
  rpc PushMessage(stream PushMessageRequest) returns (stream PushMessageResponse);
}

// 发送消息请求
message SendMessageRequest {
  string conversation_id = 1;
  int64 sender_id = 2;
  MessageType type = 3;
  MessageContent content = 4;
  map<string, string> extra = 5;
}

// 发送消息响应
message SendMessageResponse {
  bool success = 1;
  string message_id = 2;
  int64 timestamp = 3;
  string error_message = 4;
}

// 获取消息历史请求
message GetMessageHistoryRequest {
  string conversation_id = 1;
  int64 user_id = 2;
  string cursor = 3; // 分页游标
  int32 limit = 4;
  bool reverse = 5; // 是否倒序
}

// 获取消息历史响应
message GetMessageHistoryResponse {
  repeated Message messages = 1;
  string next_cursor = 2;
  bool has_more = 3;
}

// 标记消息已读请求
message MarkMessageReadRequest {
  string conversation_id = 1;
  int64 user_id = 2;
  string message_id = 3; // 最后一条已读消息ID
}

// 标记消息已读响应
message MarkMessageReadResponse {
  bool success = 1;
  string message = 2;
}

// 撤回消息请求
message RecallMessageRequest {
  string message_id = 1;
  int64 user_id = 2;
}

// 撤回消息响应
message RecallMessageResponse {
  bool success = 1;
  string message = 2;
}

// 删除消息请求
message DeleteMessageRequest {
  string message_id = 1;
  int64 user_id = 2;
  bool delete_for_all = 3; // 是否为所有人删除
}

// 删除消息响应
message DeleteMessageResponse {
  bool success = 1;
  string message = 2;
}

// 搜索消息请求
message SearchMessageRequest {
  int64 user_id = 1;
  string keyword = 2;
  string conversation_id = 3; // 可选，指定会话
  MessageType type = 4; // 可选，消息类型
  int64 start_time = 5;
  int64 end_time = 6;
  com.zlim.common.PageRequest page = 7;
}

// 搜索消息响应
message SearchMessageResponse {
  repeated Message messages = 1;
  com.zlim.common.PageResponse page = 2;
}

// 获取会话列表请求
message GetConversationsRequest {
  int64 user_id = 1;
  com.zlim.common.PageRequest page = 2;
}

// 获取会话列表响应
message GetConversationsResponse {
  repeated Conversation conversations = 1;
  com.zlim.common.PageResponse page = 2;
}

// 会话信息
message Conversation {
  string conversation_id = 1;
  ConversationType type = 2;
  string title = 3;
  string avatar = 4;
  Message last_message = 5;
  int32 unread_count = 6;
  bool muted = 7;
  bool pinned = 8;
  int64 updated_at = 9;
  map<string, string> extra = 10;
}

// 会话类型
enum ConversationType {
  CONVERSATION_TYPE_UNKNOWN = 0;
  CONVERSATION_TYPE_PRIVATE = 1; // 私聊
  CONVERSATION_TYPE_GROUP = 2;   // 群聊
  CONVERSATION_TYPE_CHANNEL = 3; // 频道
  CONVERSATION_TYPE_SYSTEM = 4;  // 系统会话
}

// 推送消息请求
message PushMessageRequest {
  string gateway_id = 1;
  repeated int64 user_ids = 2;
  Message message = 3;
}

// 推送消息响应
message PushMessageResponse {
  bool success = 1;
  string gateway_id = 2;
  int32 delivered_count = 3;
  repeated int64 failed_user_ids = 4;
}
