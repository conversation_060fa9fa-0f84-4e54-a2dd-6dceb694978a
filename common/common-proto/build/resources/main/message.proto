syntax = "proto3";

package com.zlim.message;

option java_package = "com.zlim.message.proto";
option java_outer_classname = "MessageProto";
option java_multiple_files = true;

import "common.proto";

// WebSocket消息帧
message Frame {
  FrameType type = 1;
  int64 seq = 2;
  bytes payload = 3;
  int64 timestamp = 4;
}

// 帧类型枚举
enum FrameType {
  FRAME_TYPE_UNKNOWN = 0;
  FRAME_TYPE_PING = 1;
  FRAME_TYPE_PONG = 2;
  FRAME_TYPE_CONNECT = 3;
  FRAME_TYPE_CONNECT_ACK = 4;
  FRAME_TYPE_DISCONNECT = 5;
  FRAME_TYPE_MESSAGE = 6;
  FRAME_TYPE_MESSAGE_ACK = 7;
  FRAME_TYPE_TYPING = 8;
  FRAME_TYPE_PRESENCE = 9;
}

// 连接请求
message ConnectRequest {
  string access_token = 1;
  com.zlim.common.DeviceInfo device = 2;
  bool reconnect = 3;
  int64 last_seq = 4;
}

// 连接响应
message ConnectResponse {
  bool success = 1;
  string session_id = 2;
  int64 server_time = 3;
  string error_message = 4;
}

// 消息内容
message Message {
  string message_id = 1;
  string conversation_id = 2;
  int64 sender_id = 3;
  MessageType type = 4;
  MessageContent content = 5;
  int64 timestamp = 6;
  MessageStatus status = 7;
  map<string, string> extra = 8;
}

// 消息类型
enum MessageType {
  MESSAGE_TYPE_UNKNOWN = 0;
  MESSAGE_TYPE_TEXT = 1;
  MESSAGE_TYPE_IMAGE = 2;
  MESSAGE_TYPE_AUDIO = 3;
  MESSAGE_TYPE_VIDEO = 4;
  MESSAGE_TYPE_FILE = 5;
  MESSAGE_TYPE_LOCATION = 6;
  MESSAGE_TYPE_SYSTEM = 7;
}

// 消息状态
enum MessageStatus {
  MESSAGE_STATUS_UNKNOWN = 0;
  MESSAGE_STATUS_SENDING = 1;
  MESSAGE_STATUS_SENT = 2;
  MESSAGE_STATUS_DELIVERED = 3;
  MESSAGE_STATUS_READ = 4;
  MESSAGE_STATUS_FAILED = 5;
}

// 消息内容
message MessageContent {
  oneof content {
    TextContent text = 1;
    MediaContent media = 2;
    LocationContent location = 3;
    SystemContent system = 4;
  }
}

// 文本消息
message TextContent {
  string text = 1;
  repeated MentionInfo mentions = 2;
}

// 媒体消息
message MediaContent {
  string url = 1;
  string thumbnail_url = 2;
  int64 size = 3;
  int32 width = 4;
  int32 height = 5;
  int32 duration = 6; // 音视频时长(秒)
  string mime_type = 7;
  string filename = 8;
}

// 位置消息
message LocationContent {
  double latitude = 1;
  double longitude = 2;
  string address = 3;
  string title = 4;
}

// 系统消息
message SystemContent {
  string type = 1;
  string content = 2;
  map<string, string> params = 3;
}

// @提及信息
message MentionInfo {
  int64 user_id = 1;
  string username = 2;
  int32 offset = 3;
  int32 length = 4;
}

// 消息确认
message MessageAck {
  string message_id = 1;
  MessageStatus status = 2;
  int64 timestamp = 3;
}

// 输入状态
message TypingStatus {
  string conversation_id = 1;
  int64 user_id = 2;
  bool typing = 3;
}

// 在线状态
message PresenceStatus {
  int64 user_id = 1;
  PresenceType status = 2;
  int64 last_seen = 3;
}

// 在线状态类型
enum PresenceType {
  PRESENCE_TYPE_UNKNOWN = 0;
  PRESENCE_TYPE_ONLINE = 1;
  PRESENCE_TYPE_AWAY = 2;
  PRESENCE_TYPE_BUSY = 3;
  PRESENCE_TYPE_OFFLINE = 4;
}
