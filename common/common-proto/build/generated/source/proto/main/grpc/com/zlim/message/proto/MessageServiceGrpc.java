package com.zlim.message.proto;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 消息服务gRPC接口
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.63.0)",
    comments = "Source: message_service.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class MessageServiceGrpc {

  private MessageServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.zlim.message.MessageService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.SendMessageRequest,
      com.zlim.message.proto.SendMessageResponse> getSendMessageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SendMessage",
      requestType = com.zlim.message.proto.SendMessageRequest.class,
      responseType = com.zlim.message.proto.SendMessageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.SendMessageRequest,
      com.zlim.message.proto.SendMessageResponse> getSendMessageMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.SendMessageRequest, com.zlim.message.proto.SendMessageResponse> getSendMessageMethod;
    if ((getSendMessageMethod = MessageServiceGrpc.getSendMessageMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getSendMessageMethod = MessageServiceGrpc.getSendMessageMethod) == null) {
          MessageServiceGrpc.getSendMessageMethod = getSendMessageMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.SendMessageRequest, com.zlim.message.proto.SendMessageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SendMessage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.SendMessageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.SendMessageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("SendMessage"))
              .build();
        }
      }
    }
    return getSendMessageMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.GetMessageHistoryRequest,
      com.zlim.message.proto.GetMessageHistoryResponse> getGetMessageHistoryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetMessageHistory",
      requestType = com.zlim.message.proto.GetMessageHistoryRequest.class,
      responseType = com.zlim.message.proto.GetMessageHistoryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.GetMessageHistoryRequest,
      com.zlim.message.proto.GetMessageHistoryResponse> getGetMessageHistoryMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.GetMessageHistoryRequest, com.zlim.message.proto.GetMessageHistoryResponse> getGetMessageHistoryMethod;
    if ((getGetMessageHistoryMethod = MessageServiceGrpc.getGetMessageHistoryMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getGetMessageHistoryMethod = MessageServiceGrpc.getGetMessageHistoryMethod) == null) {
          MessageServiceGrpc.getGetMessageHistoryMethod = getGetMessageHistoryMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.GetMessageHistoryRequest, com.zlim.message.proto.GetMessageHistoryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetMessageHistory"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.GetMessageHistoryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.GetMessageHistoryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("GetMessageHistory"))
              .build();
        }
      }
    }
    return getGetMessageHistoryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.MarkMessageReadRequest,
      com.zlim.message.proto.MarkMessageReadResponse> getMarkMessageReadMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "MarkMessageRead",
      requestType = com.zlim.message.proto.MarkMessageReadRequest.class,
      responseType = com.zlim.message.proto.MarkMessageReadResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.MarkMessageReadRequest,
      com.zlim.message.proto.MarkMessageReadResponse> getMarkMessageReadMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.MarkMessageReadRequest, com.zlim.message.proto.MarkMessageReadResponse> getMarkMessageReadMethod;
    if ((getMarkMessageReadMethod = MessageServiceGrpc.getMarkMessageReadMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getMarkMessageReadMethod = MessageServiceGrpc.getMarkMessageReadMethod) == null) {
          MessageServiceGrpc.getMarkMessageReadMethod = getMarkMessageReadMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.MarkMessageReadRequest, com.zlim.message.proto.MarkMessageReadResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "MarkMessageRead"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.MarkMessageReadRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.MarkMessageReadResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("MarkMessageRead"))
              .build();
        }
      }
    }
    return getMarkMessageReadMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.RecallMessageRequest,
      com.zlim.message.proto.RecallMessageResponse> getRecallMessageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "RecallMessage",
      requestType = com.zlim.message.proto.RecallMessageRequest.class,
      responseType = com.zlim.message.proto.RecallMessageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.RecallMessageRequest,
      com.zlim.message.proto.RecallMessageResponse> getRecallMessageMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.RecallMessageRequest, com.zlim.message.proto.RecallMessageResponse> getRecallMessageMethod;
    if ((getRecallMessageMethod = MessageServiceGrpc.getRecallMessageMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getRecallMessageMethod = MessageServiceGrpc.getRecallMessageMethod) == null) {
          MessageServiceGrpc.getRecallMessageMethod = getRecallMessageMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.RecallMessageRequest, com.zlim.message.proto.RecallMessageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "RecallMessage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.RecallMessageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.RecallMessageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("RecallMessage"))
              .build();
        }
      }
    }
    return getRecallMessageMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.DeleteMessageRequest,
      com.zlim.message.proto.DeleteMessageResponse> getDeleteMessageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "DeleteMessage",
      requestType = com.zlim.message.proto.DeleteMessageRequest.class,
      responseType = com.zlim.message.proto.DeleteMessageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.DeleteMessageRequest,
      com.zlim.message.proto.DeleteMessageResponse> getDeleteMessageMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.DeleteMessageRequest, com.zlim.message.proto.DeleteMessageResponse> getDeleteMessageMethod;
    if ((getDeleteMessageMethod = MessageServiceGrpc.getDeleteMessageMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getDeleteMessageMethod = MessageServiceGrpc.getDeleteMessageMethod) == null) {
          MessageServiceGrpc.getDeleteMessageMethod = getDeleteMessageMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.DeleteMessageRequest, com.zlim.message.proto.DeleteMessageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "DeleteMessage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.DeleteMessageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.DeleteMessageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("DeleteMessage"))
              .build();
        }
      }
    }
    return getDeleteMessageMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.SearchMessageRequest,
      com.zlim.message.proto.SearchMessageResponse> getSearchMessageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SearchMessage",
      requestType = com.zlim.message.proto.SearchMessageRequest.class,
      responseType = com.zlim.message.proto.SearchMessageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.SearchMessageRequest,
      com.zlim.message.proto.SearchMessageResponse> getSearchMessageMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.SearchMessageRequest, com.zlim.message.proto.SearchMessageResponse> getSearchMessageMethod;
    if ((getSearchMessageMethod = MessageServiceGrpc.getSearchMessageMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getSearchMessageMethod = MessageServiceGrpc.getSearchMessageMethod) == null) {
          MessageServiceGrpc.getSearchMessageMethod = getSearchMessageMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.SearchMessageRequest, com.zlim.message.proto.SearchMessageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SearchMessage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.SearchMessageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.SearchMessageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("SearchMessage"))
              .build();
        }
      }
    }
    return getSearchMessageMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.GetConversationsRequest,
      com.zlim.message.proto.GetConversationsResponse> getGetConversationsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetConversations",
      requestType = com.zlim.message.proto.GetConversationsRequest.class,
      responseType = com.zlim.message.proto.GetConversationsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.GetConversationsRequest,
      com.zlim.message.proto.GetConversationsResponse> getGetConversationsMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.GetConversationsRequest, com.zlim.message.proto.GetConversationsResponse> getGetConversationsMethod;
    if ((getGetConversationsMethod = MessageServiceGrpc.getGetConversationsMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getGetConversationsMethod = MessageServiceGrpc.getGetConversationsMethod) == null) {
          MessageServiceGrpc.getGetConversationsMethod = getGetConversationsMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.GetConversationsRequest, com.zlim.message.proto.GetConversationsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetConversations"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.GetConversationsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.GetConversationsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("GetConversations"))
              .build();
        }
      }
    }
    return getGetConversationsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.message.proto.PushMessageRequest,
      com.zlim.message.proto.PushMessageResponse> getPushMessageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "PushMessage",
      requestType = com.zlim.message.proto.PushMessageRequest.class,
      responseType = com.zlim.message.proto.PushMessageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
  public static io.grpc.MethodDescriptor<com.zlim.message.proto.PushMessageRequest,
      com.zlim.message.proto.PushMessageResponse> getPushMessageMethod() {
    io.grpc.MethodDescriptor<com.zlim.message.proto.PushMessageRequest, com.zlim.message.proto.PushMessageResponse> getPushMessageMethod;
    if ((getPushMessageMethod = MessageServiceGrpc.getPushMessageMethod) == null) {
      synchronized (MessageServiceGrpc.class) {
        if ((getPushMessageMethod = MessageServiceGrpc.getPushMessageMethod) == null) {
          MessageServiceGrpc.getPushMessageMethod = getPushMessageMethod =
              io.grpc.MethodDescriptor.<com.zlim.message.proto.PushMessageRequest, com.zlim.message.proto.PushMessageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "PushMessage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.PushMessageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.message.proto.PushMessageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MessageServiceMethodDescriptorSupplier("PushMessage"))
              .build();
        }
      }
    }
    return getPushMessageMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static MessageServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MessageServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MessageServiceStub>() {
        @java.lang.Override
        public MessageServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MessageServiceStub(channel, callOptions);
        }
      };
    return MessageServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static MessageServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MessageServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MessageServiceBlockingStub>() {
        @java.lang.Override
        public MessageServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MessageServiceBlockingStub(channel, callOptions);
        }
      };
    return MessageServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static MessageServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MessageServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MessageServiceFutureStub>() {
        @java.lang.Override
        public MessageServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MessageServiceFutureStub(channel, callOptions);
        }
      };
    return MessageServiceFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * 消息服务gRPC接口
   * </pre>
   */
  public interface AsyncService {

    /**
     * <pre>
     * 发送消息
     * </pre>
     */
    default void sendMessage(com.zlim.message.proto.SendMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.SendMessageResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSendMessageMethod(), responseObserver);
    }

    /**
     * <pre>
     * 获取消息历史
     * </pre>
     */
    default void getMessageHistory(com.zlim.message.proto.GetMessageHistoryRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.GetMessageHistoryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetMessageHistoryMethod(), responseObserver);
    }

    /**
     * <pre>
     * 标记消息已读
     * </pre>
     */
    default void markMessageRead(com.zlim.message.proto.MarkMessageReadRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.MarkMessageReadResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getMarkMessageReadMethod(), responseObserver);
    }

    /**
     * <pre>
     * 撤回消息
     * </pre>
     */
    default void recallMessage(com.zlim.message.proto.RecallMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.RecallMessageResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRecallMessageMethod(), responseObserver);
    }

    /**
     * <pre>
     * 删除消息
     * </pre>
     */
    default void deleteMessage(com.zlim.message.proto.DeleteMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.DeleteMessageResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteMessageMethod(), responseObserver);
    }

    /**
     * <pre>
     * 搜索消息
     * </pre>
     */
    default void searchMessage(com.zlim.message.proto.SearchMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.SearchMessageResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSearchMessageMethod(), responseObserver);
    }

    /**
     * <pre>
     * 获取会话列表
     * </pre>
     */
    default void getConversations(com.zlim.message.proto.GetConversationsRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.GetConversationsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetConversationsMethod(), responseObserver);
    }

    /**
     * <pre>
     * 推送消息到网关
     * </pre>
     */
    default io.grpc.stub.StreamObserver<com.zlim.message.proto.PushMessageRequest> pushMessage(
        io.grpc.stub.StreamObserver<com.zlim.message.proto.PushMessageResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getPushMessageMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service MessageService.
   * <pre>
   * 消息服务gRPC接口
   * </pre>
   */
  public static abstract class MessageServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return MessageServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service MessageService.
   * <pre>
   * 消息服务gRPC接口
   * </pre>
   */
  public static final class MessageServiceStub
      extends io.grpc.stub.AbstractAsyncStub<MessageServiceStub> {
    private MessageServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MessageServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MessageServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * 发送消息
     * </pre>
     */
    public void sendMessage(com.zlim.message.proto.SendMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.SendMessageResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSendMessageMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 获取消息历史
     * </pre>
     */
    public void getMessageHistory(com.zlim.message.proto.GetMessageHistoryRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.GetMessageHistoryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetMessageHistoryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 标记消息已读
     * </pre>
     */
    public void markMessageRead(com.zlim.message.proto.MarkMessageReadRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.MarkMessageReadResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getMarkMessageReadMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 撤回消息
     * </pre>
     */
    public void recallMessage(com.zlim.message.proto.RecallMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.RecallMessageResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRecallMessageMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 删除消息
     * </pre>
     */
    public void deleteMessage(com.zlim.message.proto.DeleteMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.DeleteMessageResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteMessageMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 搜索消息
     * </pre>
     */
    public void searchMessage(com.zlim.message.proto.SearchMessageRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.SearchMessageResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSearchMessageMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 获取会话列表
     * </pre>
     */
    public void getConversations(com.zlim.message.proto.GetConversationsRequest request,
        io.grpc.stub.StreamObserver<com.zlim.message.proto.GetConversationsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetConversationsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 推送消息到网关
     * </pre>
     */
    public io.grpc.stub.StreamObserver<com.zlim.message.proto.PushMessageRequest> pushMessage(
        io.grpc.stub.StreamObserver<com.zlim.message.proto.PushMessageResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncBidiStreamingCall(
          getChannel().newCall(getPushMessageMethod(), getCallOptions()), responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service MessageService.
   * <pre>
   * 消息服务gRPC接口
   * </pre>
   */
  public static final class MessageServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<MessageServiceBlockingStub> {
    private MessageServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MessageServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MessageServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 发送消息
     * </pre>
     */
    public com.zlim.message.proto.SendMessageResponse sendMessage(com.zlim.message.proto.SendMessageRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSendMessageMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 获取消息历史
     * </pre>
     */
    public com.zlim.message.proto.GetMessageHistoryResponse getMessageHistory(com.zlim.message.proto.GetMessageHistoryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetMessageHistoryMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 标记消息已读
     * </pre>
     */
    public com.zlim.message.proto.MarkMessageReadResponse markMessageRead(com.zlim.message.proto.MarkMessageReadRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getMarkMessageReadMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 撤回消息
     * </pre>
     */
    public com.zlim.message.proto.RecallMessageResponse recallMessage(com.zlim.message.proto.RecallMessageRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRecallMessageMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 删除消息
     * </pre>
     */
    public com.zlim.message.proto.DeleteMessageResponse deleteMessage(com.zlim.message.proto.DeleteMessageRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteMessageMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 搜索消息
     * </pre>
     */
    public com.zlim.message.proto.SearchMessageResponse searchMessage(com.zlim.message.proto.SearchMessageRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSearchMessageMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 获取会话列表
     * </pre>
     */
    public com.zlim.message.proto.GetConversationsResponse getConversations(com.zlim.message.proto.GetConversationsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetConversationsMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service MessageService.
   * <pre>
   * 消息服务gRPC接口
   * </pre>
   */
  public static final class MessageServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<MessageServiceFutureStub> {
    private MessageServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MessageServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MessageServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 发送消息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.SendMessageResponse> sendMessage(
        com.zlim.message.proto.SendMessageRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSendMessageMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 获取消息历史
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.GetMessageHistoryResponse> getMessageHistory(
        com.zlim.message.proto.GetMessageHistoryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetMessageHistoryMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 标记消息已读
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.MarkMessageReadResponse> markMessageRead(
        com.zlim.message.proto.MarkMessageReadRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getMarkMessageReadMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 撤回消息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.RecallMessageResponse> recallMessage(
        com.zlim.message.proto.RecallMessageRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRecallMessageMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 删除消息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.DeleteMessageResponse> deleteMessage(
        com.zlim.message.proto.DeleteMessageRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteMessageMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 搜索消息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.SearchMessageResponse> searchMessage(
        com.zlim.message.proto.SearchMessageRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSearchMessageMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 获取会话列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.message.proto.GetConversationsResponse> getConversations(
        com.zlim.message.proto.GetConversationsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetConversationsMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SEND_MESSAGE = 0;
  private static final int METHODID_GET_MESSAGE_HISTORY = 1;
  private static final int METHODID_MARK_MESSAGE_READ = 2;
  private static final int METHODID_RECALL_MESSAGE = 3;
  private static final int METHODID_DELETE_MESSAGE = 4;
  private static final int METHODID_SEARCH_MESSAGE = 5;
  private static final int METHODID_GET_CONVERSATIONS = 6;
  private static final int METHODID_PUSH_MESSAGE = 7;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SEND_MESSAGE:
          serviceImpl.sendMessage((com.zlim.message.proto.SendMessageRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.SendMessageResponse>) responseObserver);
          break;
        case METHODID_GET_MESSAGE_HISTORY:
          serviceImpl.getMessageHistory((com.zlim.message.proto.GetMessageHistoryRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.GetMessageHistoryResponse>) responseObserver);
          break;
        case METHODID_MARK_MESSAGE_READ:
          serviceImpl.markMessageRead((com.zlim.message.proto.MarkMessageReadRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.MarkMessageReadResponse>) responseObserver);
          break;
        case METHODID_RECALL_MESSAGE:
          serviceImpl.recallMessage((com.zlim.message.proto.RecallMessageRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.RecallMessageResponse>) responseObserver);
          break;
        case METHODID_DELETE_MESSAGE:
          serviceImpl.deleteMessage((com.zlim.message.proto.DeleteMessageRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.DeleteMessageResponse>) responseObserver);
          break;
        case METHODID_SEARCH_MESSAGE:
          serviceImpl.searchMessage((com.zlim.message.proto.SearchMessageRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.SearchMessageResponse>) responseObserver);
          break;
        case METHODID_GET_CONVERSATIONS:
          serviceImpl.getConversations((com.zlim.message.proto.GetConversationsRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.GetConversationsResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_PUSH_MESSAGE:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.pushMessage(
              (io.grpc.stub.StreamObserver<com.zlim.message.proto.PushMessageResponse>) responseObserver);
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSendMessageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.SendMessageRequest,
              com.zlim.message.proto.SendMessageResponse>(
                service, METHODID_SEND_MESSAGE)))
        .addMethod(
          getGetMessageHistoryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.GetMessageHistoryRequest,
              com.zlim.message.proto.GetMessageHistoryResponse>(
                service, METHODID_GET_MESSAGE_HISTORY)))
        .addMethod(
          getMarkMessageReadMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.MarkMessageReadRequest,
              com.zlim.message.proto.MarkMessageReadResponse>(
                service, METHODID_MARK_MESSAGE_READ)))
        .addMethod(
          getRecallMessageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.RecallMessageRequest,
              com.zlim.message.proto.RecallMessageResponse>(
                service, METHODID_RECALL_MESSAGE)))
        .addMethod(
          getDeleteMessageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.DeleteMessageRequest,
              com.zlim.message.proto.DeleteMessageResponse>(
                service, METHODID_DELETE_MESSAGE)))
        .addMethod(
          getSearchMessageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.SearchMessageRequest,
              com.zlim.message.proto.SearchMessageResponse>(
                service, METHODID_SEARCH_MESSAGE)))
        .addMethod(
          getGetConversationsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.message.proto.GetConversationsRequest,
              com.zlim.message.proto.GetConversationsResponse>(
                service, METHODID_GET_CONVERSATIONS)))
        .addMethod(
          getPushMessageMethod(),
          io.grpc.stub.ServerCalls.asyncBidiStreamingCall(
            new MethodHandlers<
              com.zlim.message.proto.PushMessageRequest,
              com.zlim.message.proto.PushMessageResponse>(
                service, METHODID_PUSH_MESSAGE)))
        .build();
  }

  private static abstract class MessageServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    MessageServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.zlim.message.proto.MessageServiceProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("MessageService");
    }
  }

  private static final class MessageServiceFileDescriptorSupplier
      extends MessageServiceBaseDescriptorSupplier {
    MessageServiceFileDescriptorSupplier() {}
  }

  private static final class MessageServiceMethodDescriptorSupplier
      extends MessageServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    MessageServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (MessageServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new MessageServiceFileDescriptorSupplier())
              .addMethod(getSendMessageMethod())
              .addMethod(getGetMessageHistoryMethod())
              .addMethod(getMarkMessageReadMethod())
              .addMethod(getRecallMessageMethod())
              .addMethod(getDeleteMessageMethod())
              .addMethod(getSearchMessageMethod())
              .addMethod(getGetConversationsMethod())
              .addMethod(getPushMessageMethod())
              .build();
        }
      }
    }
    return result;
  }
}
