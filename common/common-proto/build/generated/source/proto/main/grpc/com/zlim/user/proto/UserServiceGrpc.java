package com.zlim.user.proto;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 用户服务gRPC接口
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.63.0)",
    comments = "Source: user_service.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class UserServiceGrpc {

  private UserServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "com.zlim.user.UserService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.RegisterRequest,
      com.zlim.user.proto.RegisterResponse> getRegisterMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Register",
      requestType = com.zlim.user.proto.RegisterRequest.class,
      responseType = com.zlim.user.proto.RegisterResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.RegisterRequest,
      com.zlim.user.proto.RegisterResponse> getRegisterMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.RegisterRequest, com.zlim.user.proto.RegisterResponse> getRegisterMethod;
    if ((getRegisterMethod = UserServiceGrpc.getRegisterMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getRegisterMethod = UserServiceGrpc.getRegisterMethod) == null) {
          UserServiceGrpc.getRegisterMethod = getRegisterMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.RegisterRequest, com.zlim.user.proto.RegisterResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Register"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.RegisterRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.RegisterResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("Register"))
              .build();
        }
      }
    }
    return getRegisterMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.LoginRequest,
      com.zlim.user.proto.LoginResponse> getLoginMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Login",
      requestType = com.zlim.user.proto.LoginRequest.class,
      responseType = com.zlim.user.proto.LoginResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.LoginRequest,
      com.zlim.user.proto.LoginResponse> getLoginMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.LoginRequest, com.zlim.user.proto.LoginResponse> getLoginMethod;
    if ((getLoginMethod = UserServiceGrpc.getLoginMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getLoginMethod = UserServiceGrpc.getLoginMethod) == null) {
          UserServiceGrpc.getLoginMethod = getLoginMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.LoginRequest, com.zlim.user.proto.LoginResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Login"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.LoginRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.LoginResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("Login"))
              .build();
        }
      }
    }
    return getLoginMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.RefreshTokenRequest,
      com.zlim.user.proto.RefreshTokenResponse> getRefreshTokenMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "RefreshToken",
      requestType = com.zlim.user.proto.RefreshTokenRequest.class,
      responseType = com.zlim.user.proto.RefreshTokenResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.RefreshTokenRequest,
      com.zlim.user.proto.RefreshTokenResponse> getRefreshTokenMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.RefreshTokenRequest, com.zlim.user.proto.RefreshTokenResponse> getRefreshTokenMethod;
    if ((getRefreshTokenMethod = UserServiceGrpc.getRefreshTokenMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getRefreshTokenMethod = UserServiceGrpc.getRefreshTokenMethod) == null) {
          UserServiceGrpc.getRefreshTokenMethod = getRefreshTokenMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.RefreshTokenRequest, com.zlim.user.proto.RefreshTokenResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "RefreshToken"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.RefreshTokenRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.RefreshTokenResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("RefreshToken"))
              .build();
        }
      }
    }
    return getRefreshTokenMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.GetUserInfoRequest,
      com.zlim.user.proto.GetUserInfoResponse> getGetUserInfoMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetUserInfo",
      requestType = com.zlim.user.proto.GetUserInfoRequest.class,
      responseType = com.zlim.user.proto.GetUserInfoResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.GetUserInfoRequest,
      com.zlim.user.proto.GetUserInfoResponse> getGetUserInfoMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.GetUserInfoRequest, com.zlim.user.proto.GetUserInfoResponse> getGetUserInfoMethod;
    if ((getGetUserInfoMethod = UserServiceGrpc.getGetUserInfoMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getGetUserInfoMethod = UserServiceGrpc.getGetUserInfoMethod) == null) {
          UserServiceGrpc.getGetUserInfoMethod = getGetUserInfoMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.GetUserInfoRequest, com.zlim.user.proto.GetUserInfoResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetUserInfo"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.GetUserInfoRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.GetUserInfoResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("GetUserInfo"))
              .build();
        }
      }
    }
    return getGetUserInfoMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.UpdateUserInfoRequest,
      com.zlim.user.proto.UpdateUserInfoResponse> getUpdateUserInfoMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateUserInfo",
      requestType = com.zlim.user.proto.UpdateUserInfoRequest.class,
      responseType = com.zlim.user.proto.UpdateUserInfoResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.UpdateUserInfoRequest,
      com.zlim.user.proto.UpdateUserInfoResponse> getUpdateUserInfoMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.UpdateUserInfoRequest, com.zlim.user.proto.UpdateUserInfoResponse> getUpdateUserInfoMethod;
    if ((getUpdateUserInfoMethod = UserServiceGrpc.getUpdateUserInfoMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getUpdateUserInfoMethod = UserServiceGrpc.getUpdateUserInfoMethod) == null) {
          UserServiceGrpc.getUpdateUserInfoMethod = getUpdateUserInfoMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.UpdateUserInfoRequest, com.zlim.user.proto.UpdateUserInfoResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateUserInfo"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.UpdateUserInfoRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.UpdateUserInfoResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("UpdateUserInfo"))
              .build();
        }
      }
    }
    return getUpdateUserInfoMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.VerifyTokenRequest,
      com.zlim.user.proto.VerifyTokenResponse> getVerifyTokenMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "VerifyToken",
      requestType = com.zlim.user.proto.VerifyTokenRequest.class,
      responseType = com.zlim.user.proto.VerifyTokenResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.VerifyTokenRequest,
      com.zlim.user.proto.VerifyTokenResponse> getVerifyTokenMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.VerifyTokenRequest, com.zlim.user.proto.VerifyTokenResponse> getVerifyTokenMethod;
    if ((getVerifyTokenMethod = UserServiceGrpc.getVerifyTokenMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getVerifyTokenMethod = UserServiceGrpc.getVerifyTokenMethod) == null) {
          UserServiceGrpc.getVerifyTokenMethod = getVerifyTokenMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.VerifyTokenRequest, com.zlim.user.proto.VerifyTokenResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "VerifyToken"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.VerifyTokenRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.VerifyTokenResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("VerifyToken"))
              .build();
        }
      }
    }
    return getVerifyTokenMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.LogoutRequest,
      com.zlim.user.proto.LogoutResponse> getLogoutMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Logout",
      requestType = com.zlim.user.proto.LogoutRequest.class,
      responseType = com.zlim.user.proto.LogoutResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.LogoutRequest,
      com.zlim.user.proto.LogoutResponse> getLogoutMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.LogoutRequest, com.zlim.user.proto.LogoutResponse> getLogoutMethod;
    if ((getLogoutMethod = UserServiceGrpc.getLogoutMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getLogoutMethod = UserServiceGrpc.getLogoutMethod) == null) {
          UserServiceGrpc.getLogoutMethod = getLogoutMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.LogoutRequest, com.zlim.user.proto.LogoutResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Logout"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.LogoutRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.LogoutResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("Logout"))
              .build();
        }
      }
    }
    return getLogoutMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.zlim.user.proto.BatchGetUserInfoRequest,
      com.zlim.user.proto.BatchGetUserInfoResponse> getBatchGetUserInfoMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "BatchGetUserInfo",
      requestType = com.zlim.user.proto.BatchGetUserInfoRequest.class,
      responseType = com.zlim.user.proto.BatchGetUserInfoResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.zlim.user.proto.BatchGetUserInfoRequest,
      com.zlim.user.proto.BatchGetUserInfoResponse> getBatchGetUserInfoMethod() {
    io.grpc.MethodDescriptor<com.zlim.user.proto.BatchGetUserInfoRequest, com.zlim.user.proto.BatchGetUserInfoResponse> getBatchGetUserInfoMethod;
    if ((getBatchGetUserInfoMethod = UserServiceGrpc.getBatchGetUserInfoMethod) == null) {
      synchronized (UserServiceGrpc.class) {
        if ((getBatchGetUserInfoMethod = UserServiceGrpc.getBatchGetUserInfoMethod) == null) {
          UserServiceGrpc.getBatchGetUserInfoMethod = getBatchGetUserInfoMethod =
              io.grpc.MethodDescriptor.<com.zlim.user.proto.BatchGetUserInfoRequest, com.zlim.user.proto.BatchGetUserInfoResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "BatchGetUserInfo"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.BatchGetUserInfoRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.zlim.user.proto.BatchGetUserInfoResponse.getDefaultInstance()))
              .setSchemaDescriptor(new UserServiceMethodDescriptorSupplier("BatchGetUserInfo"))
              .build();
        }
      }
    }
    return getBatchGetUserInfoMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static UserServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<UserServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<UserServiceStub>() {
        @java.lang.Override
        public UserServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new UserServiceStub(channel, callOptions);
        }
      };
    return UserServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static UserServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<UserServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<UserServiceBlockingStub>() {
        @java.lang.Override
        public UserServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new UserServiceBlockingStub(channel, callOptions);
        }
      };
    return UserServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static UserServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<UserServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<UserServiceFutureStub>() {
        @java.lang.Override
        public UserServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new UserServiceFutureStub(channel, callOptions);
        }
      };
    return UserServiceFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * 用户服务gRPC接口
   * </pre>
   */
  public interface AsyncService {

    /**
     * <pre>
     * 用户注册
     * </pre>
     */
    default void register(com.zlim.user.proto.RegisterRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.RegisterResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRegisterMethod(), responseObserver);
    }

    /**
     * <pre>
     * 用户登录
     * </pre>
     */
    default void login(com.zlim.user.proto.LoginRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.LoginResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLoginMethod(), responseObserver);
    }

    /**
     * <pre>
     * 刷新Token
     * </pre>
     */
    default void refreshToken(com.zlim.user.proto.RefreshTokenRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.RefreshTokenResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRefreshTokenMethod(), responseObserver);
    }

    /**
     * <pre>
     * 获取用户信息
     * </pre>
     */
    default void getUserInfo(com.zlim.user.proto.GetUserInfoRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.GetUserInfoResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetUserInfoMethod(), responseObserver);
    }

    /**
     * <pre>
     * 更新用户信息
     * </pre>
     */
    default void updateUserInfo(com.zlim.user.proto.UpdateUserInfoRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.UpdateUserInfoResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateUserInfoMethod(), responseObserver);
    }

    /**
     * <pre>
     * 验证Token
     * </pre>
     */
    default void verifyToken(com.zlim.user.proto.VerifyTokenRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.VerifyTokenResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getVerifyTokenMethod(), responseObserver);
    }

    /**
     * <pre>
     * 用户登出
     * </pre>
     */
    default void logout(com.zlim.user.proto.LogoutRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.LogoutResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLogoutMethod(), responseObserver);
    }

    /**
     * <pre>
     * 批量获取用户信息
     * </pre>
     */
    default void batchGetUserInfo(com.zlim.user.proto.BatchGetUserInfoRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.BatchGetUserInfoResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBatchGetUserInfoMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service UserService.
   * <pre>
   * 用户服务gRPC接口
   * </pre>
   */
  public static abstract class UserServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return UserServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service UserService.
   * <pre>
   * 用户服务gRPC接口
   * </pre>
   */
  public static final class UserServiceStub
      extends io.grpc.stub.AbstractAsyncStub<UserServiceStub> {
    private UserServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected UserServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new UserServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * 用户注册
     * </pre>
     */
    public void register(com.zlim.user.proto.RegisterRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.RegisterResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRegisterMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 用户登录
     * </pre>
     */
    public void login(com.zlim.user.proto.LoginRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.LoginResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLoginMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 刷新Token
     * </pre>
     */
    public void refreshToken(com.zlim.user.proto.RefreshTokenRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.RefreshTokenResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRefreshTokenMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 获取用户信息
     * </pre>
     */
    public void getUserInfo(com.zlim.user.proto.GetUserInfoRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.GetUserInfoResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetUserInfoMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 更新用户信息
     * </pre>
     */
    public void updateUserInfo(com.zlim.user.proto.UpdateUserInfoRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.UpdateUserInfoResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateUserInfoMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 验证Token
     * </pre>
     */
    public void verifyToken(com.zlim.user.proto.VerifyTokenRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.VerifyTokenResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getVerifyTokenMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 用户登出
     * </pre>
     */
    public void logout(com.zlim.user.proto.LogoutRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.LogoutResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLogoutMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 批量获取用户信息
     * </pre>
     */
    public void batchGetUserInfo(com.zlim.user.proto.BatchGetUserInfoRequest request,
        io.grpc.stub.StreamObserver<com.zlim.user.proto.BatchGetUserInfoResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getBatchGetUserInfoMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service UserService.
   * <pre>
   * 用户服务gRPC接口
   * </pre>
   */
  public static final class UserServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<UserServiceBlockingStub> {
    private UserServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected UserServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new UserServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 用户注册
     * </pre>
     */
    public com.zlim.user.proto.RegisterResponse register(com.zlim.user.proto.RegisterRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRegisterMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 用户登录
     * </pre>
     */
    public com.zlim.user.proto.LoginResponse login(com.zlim.user.proto.LoginRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLoginMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 刷新Token
     * </pre>
     */
    public com.zlim.user.proto.RefreshTokenResponse refreshToken(com.zlim.user.proto.RefreshTokenRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRefreshTokenMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 获取用户信息
     * </pre>
     */
    public com.zlim.user.proto.GetUserInfoResponse getUserInfo(com.zlim.user.proto.GetUserInfoRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetUserInfoMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 更新用户信息
     * </pre>
     */
    public com.zlim.user.proto.UpdateUserInfoResponse updateUserInfo(com.zlim.user.proto.UpdateUserInfoRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateUserInfoMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 验证Token
     * </pre>
     */
    public com.zlim.user.proto.VerifyTokenResponse verifyToken(com.zlim.user.proto.VerifyTokenRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getVerifyTokenMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 用户登出
     * </pre>
     */
    public com.zlim.user.proto.LogoutResponse logout(com.zlim.user.proto.LogoutRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLogoutMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 批量获取用户信息
     * </pre>
     */
    public com.zlim.user.proto.BatchGetUserInfoResponse batchGetUserInfo(com.zlim.user.proto.BatchGetUserInfoRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getBatchGetUserInfoMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service UserService.
   * <pre>
   * 用户服务gRPC接口
   * </pre>
   */
  public static final class UserServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<UserServiceFutureStub> {
    private UserServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected UserServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new UserServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 用户注册
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.RegisterResponse> register(
        com.zlim.user.proto.RegisterRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRegisterMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 用户登录
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.LoginResponse> login(
        com.zlim.user.proto.LoginRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLoginMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 刷新Token
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.RefreshTokenResponse> refreshToken(
        com.zlim.user.proto.RefreshTokenRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRefreshTokenMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 获取用户信息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.GetUserInfoResponse> getUserInfo(
        com.zlim.user.proto.GetUserInfoRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetUserInfoMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 更新用户信息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.UpdateUserInfoResponse> updateUserInfo(
        com.zlim.user.proto.UpdateUserInfoRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateUserInfoMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 验证Token
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.VerifyTokenResponse> verifyToken(
        com.zlim.user.proto.VerifyTokenRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getVerifyTokenMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 用户登出
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.LogoutResponse> logout(
        com.zlim.user.proto.LogoutRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLogoutMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 批量获取用户信息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.zlim.user.proto.BatchGetUserInfoResponse> batchGetUserInfo(
        com.zlim.user.proto.BatchGetUserInfoRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getBatchGetUserInfoMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_REGISTER = 0;
  private static final int METHODID_LOGIN = 1;
  private static final int METHODID_REFRESH_TOKEN = 2;
  private static final int METHODID_GET_USER_INFO = 3;
  private static final int METHODID_UPDATE_USER_INFO = 4;
  private static final int METHODID_VERIFY_TOKEN = 5;
  private static final int METHODID_LOGOUT = 6;
  private static final int METHODID_BATCH_GET_USER_INFO = 7;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_REGISTER:
          serviceImpl.register((com.zlim.user.proto.RegisterRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.RegisterResponse>) responseObserver);
          break;
        case METHODID_LOGIN:
          serviceImpl.login((com.zlim.user.proto.LoginRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.LoginResponse>) responseObserver);
          break;
        case METHODID_REFRESH_TOKEN:
          serviceImpl.refreshToken((com.zlim.user.proto.RefreshTokenRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.RefreshTokenResponse>) responseObserver);
          break;
        case METHODID_GET_USER_INFO:
          serviceImpl.getUserInfo((com.zlim.user.proto.GetUserInfoRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.GetUserInfoResponse>) responseObserver);
          break;
        case METHODID_UPDATE_USER_INFO:
          serviceImpl.updateUserInfo((com.zlim.user.proto.UpdateUserInfoRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.UpdateUserInfoResponse>) responseObserver);
          break;
        case METHODID_VERIFY_TOKEN:
          serviceImpl.verifyToken((com.zlim.user.proto.VerifyTokenRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.VerifyTokenResponse>) responseObserver);
          break;
        case METHODID_LOGOUT:
          serviceImpl.logout((com.zlim.user.proto.LogoutRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.LogoutResponse>) responseObserver);
          break;
        case METHODID_BATCH_GET_USER_INFO:
          serviceImpl.batchGetUserInfo((com.zlim.user.proto.BatchGetUserInfoRequest) request,
              (io.grpc.stub.StreamObserver<com.zlim.user.proto.BatchGetUserInfoResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getRegisterMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.RegisterRequest,
              com.zlim.user.proto.RegisterResponse>(
                service, METHODID_REGISTER)))
        .addMethod(
          getLoginMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.LoginRequest,
              com.zlim.user.proto.LoginResponse>(
                service, METHODID_LOGIN)))
        .addMethod(
          getRefreshTokenMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.RefreshTokenRequest,
              com.zlim.user.proto.RefreshTokenResponse>(
                service, METHODID_REFRESH_TOKEN)))
        .addMethod(
          getGetUserInfoMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.GetUserInfoRequest,
              com.zlim.user.proto.GetUserInfoResponse>(
                service, METHODID_GET_USER_INFO)))
        .addMethod(
          getUpdateUserInfoMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.UpdateUserInfoRequest,
              com.zlim.user.proto.UpdateUserInfoResponse>(
                service, METHODID_UPDATE_USER_INFO)))
        .addMethod(
          getVerifyTokenMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.VerifyTokenRequest,
              com.zlim.user.proto.VerifyTokenResponse>(
                service, METHODID_VERIFY_TOKEN)))
        .addMethod(
          getLogoutMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.LogoutRequest,
              com.zlim.user.proto.LogoutResponse>(
                service, METHODID_LOGOUT)))
        .addMethod(
          getBatchGetUserInfoMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.zlim.user.proto.BatchGetUserInfoRequest,
              com.zlim.user.proto.BatchGetUserInfoResponse>(
                service, METHODID_BATCH_GET_USER_INFO)))
        .build();
  }

  private static abstract class UserServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    UserServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.zlim.user.proto.UserServiceProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("UserService");
    }
  }

  private static final class UserServiceFileDescriptorSupplier
      extends UserServiceBaseDescriptorSupplier {
    UserServiceFileDescriptorSupplier() {}
  }

  private static final class UserServiceMethodDescriptorSupplier
      extends UserServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    UserServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (UserServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new UserServiceFileDescriptorSupplier())
              .addMethod(getRegisterMethod())
              .addMethod(getLoginMethod())
              .addMethod(getRefreshTokenMethod())
              .addMethod(getGetUserInfoMethod())
              .addMethod(getUpdateUserInfoMethod())
              .addMethod(getVerifyTokenMethod())
              .addMethod(getLogoutMethod())
              .addMethod(getBatchGetUserInfoMethod())
              .build();
        }
      }
    }
    return result;
  }
}
