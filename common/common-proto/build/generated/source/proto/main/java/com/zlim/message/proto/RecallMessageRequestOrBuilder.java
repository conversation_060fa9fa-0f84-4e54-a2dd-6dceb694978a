// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface RecallMessageRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.RecallMessageRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string message_id = 1;</code>
   * @return The messageId.
   */
  java.lang.String getMessageId();
  /**
   * <code>string message_id = 1;</code>
   * @return The bytes for messageId.
   */
  com.google.protobuf.ByteString
      getMessageIdBytes();

  /**
   * <code>int64 user_id = 2;</code>
   * @return The userId.
   */
  long getUserId();
}
