// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

public interface PageResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.common.PageResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 total = 1;</code>
   * @return The total.
   */
  long getTotal();

  /**
   * <code>int32 page = 2;</code>
   * @return The page.
   */
  int getPage();

  /**
   * <code>int32 size = 3;</code>
   * @return The size.
   */
  int getSize();

  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  java.util.List<com.google.protobuf.Any> 
      getContentList();
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  com.google.protobuf.Any getContent(int index);
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  int getContentCount();
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
      getContentOrBuilderList();
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  com.google.protobuf.AnyOrBuilder getContentOrBuilder(
      int index);
}
