// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 帧类型枚举
 * </pre>
 *
 * Protobuf enum {@code com.zlim.message.FrameType}
 */
public enum FrameType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>FRAME_TYPE_UNKNOWN = 0;</code>
   */
  FRAME_TYPE_UNKNOWN(0),
  /**
   * <code>FRAME_TYPE_PING = 1;</code>
   */
  FRAME_TYPE_PING(1),
  /**
   * <code>FRAME_TYPE_PONG = 2;</code>
   */
  FRAME_TYPE_PONG(2),
  /**
   * <code>FRAME_TYPE_CONNECT = 3;</code>
   */
  FRAME_TYPE_CONNECT(3),
  /**
   * <code>FRAME_TYPE_CONNECT_ACK = 4;</code>
   */
  FRAME_TYPE_CONNECT_ACK(4),
  /**
   * <code>FRAME_TYPE_DISCONNECT = 5;</code>
   */
  FRAME_TYPE_DISCONNECT(5),
  /**
   * <code>FRAME_TYPE_MESSAGE = 6;</code>
   */
  FRAME_TYPE_MESSAGE(6),
  /**
   * <code>FRAME_TYPE_MESSAGE_ACK = 7;</code>
   */
  FRAME_TYPE_MESSAGE_ACK(7),
  /**
   * <code>FRAME_TYPE_TYPING = 8;</code>
   */
  FRAME_TYPE_TYPING(8),
  /**
   * <code>FRAME_TYPE_PRESENCE = 9;</code>
   */
  FRAME_TYPE_PRESENCE(9),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>FRAME_TYPE_UNKNOWN = 0;</code>
   */
  public static final int FRAME_TYPE_UNKNOWN_VALUE = 0;
  /**
   * <code>FRAME_TYPE_PING = 1;</code>
   */
  public static final int FRAME_TYPE_PING_VALUE = 1;
  /**
   * <code>FRAME_TYPE_PONG = 2;</code>
   */
  public static final int FRAME_TYPE_PONG_VALUE = 2;
  /**
   * <code>FRAME_TYPE_CONNECT = 3;</code>
   */
  public static final int FRAME_TYPE_CONNECT_VALUE = 3;
  /**
   * <code>FRAME_TYPE_CONNECT_ACK = 4;</code>
   */
  public static final int FRAME_TYPE_CONNECT_ACK_VALUE = 4;
  /**
   * <code>FRAME_TYPE_DISCONNECT = 5;</code>
   */
  public static final int FRAME_TYPE_DISCONNECT_VALUE = 5;
  /**
   * <code>FRAME_TYPE_MESSAGE = 6;</code>
   */
  public static final int FRAME_TYPE_MESSAGE_VALUE = 6;
  /**
   * <code>FRAME_TYPE_MESSAGE_ACK = 7;</code>
   */
  public static final int FRAME_TYPE_MESSAGE_ACK_VALUE = 7;
  /**
   * <code>FRAME_TYPE_TYPING = 8;</code>
   */
  public static final int FRAME_TYPE_TYPING_VALUE = 8;
  /**
   * <code>FRAME_TYPE_PRESENCE = 9;</code>
   */
  public static final int FRAME_TYPE_PRESENCE_VALUE = 9;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static FrameType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static FrameType forNumber(int value) {
    switch (value) {
      case 0: return FRAME_TYPE_UNKNOWN;
      case 1: return FRAME_TYPE_PING;
      case 2: return FRAME_TYPE_PONG;
      case 3: return FRAME_TYPE_CONNECT;
      case 4: return FRAME_TYPE_CONNECT_ACK;
      case 5: return FRAME_TYPE_DISCONNECT;
      case 6: return FRAME_TYPE_MESSAGE;
      case 7: return FRAME_TYPE_MESSAGE_ACK;
      case 8: return FRAME_TYPE_TYPING;
      case 9: return FRAME_TYPE_PRESENCE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<FrameType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      FrameType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<FrameType>() {
          public FrameType findValueByNumber(int number) {
            return FrameType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final FrameType[] VALUES = values();

  public static FrameType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private FrameType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.zlim.message.FrameType)
}

