// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface RegisterResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.RegisterResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>int64 user_id = 3;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <pre>
   * 错误码，客户端可用此进行国际化
   * </pre>
   *
   * <code>int32 error_code = 4;</code>
   * @return The errorCode.
   */
  int getErrorCode();
}
