// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 消息类型
 * </pre>
 *
 * Protobuf enum {@code com.zlim.message.MessageType}
 */
public enum MessageType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>MESSAGE_TYPE_UNKNOWN = 0;</code>
   */
  MESSAGE_TYPE_UNKNOWN(0),
  /**
   * <code>MESSAGE_TYPE_TEXT = 1;</code>
   */
  MESSAGE_TYPE_TEXT(1),
  /**
   * <code>MESSAGE_TYPE_IMAGE = 2;</code>
   */
  MESSAGE_TYPE_IMAGE(2),
  /**
   * <code>MESSAGE_TYPE_AUDIO = 3;</code>
   */
  MESSAGE_TYPE_AUDIO(3),
  /**
   * <code>MESSAGE_TYPE_VIDEO = 4;</code>
   */
  MESSAGE_TYPE_VIDEO(4),
  /**
   * <code>MESSAGE_TYPE_FILE = 5;</code>
   */
  MESSAGE_TYPE_FILE(5),
  /**
   * <code>MESSAGE_TYPE_LOCATION = 6;</code>
   */
  MESSAGE_TYPE_LOCATION(6),
  /**
   * <code>MESSAGE_TYPE_SYSTEM = 7;</code>
   */
  MESSAGE_TYPE_SYSTEM(7),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>MESSAGE_TYPE_UNKNOWN = 0;</code>
   */
  public static final int MESSAGE_TYPE_UNKNOWN_VALUE = 0;
  /**
   * <code>MESSAGE_TYPE_TEXT = 1;</code>
   */
  public static final int MESSAGE_TYPE_TEXT_VALUE = 1;
  /**
   * <code>MESSAGE_TYPE_IMAGE = 2;</code>
   */
  public static final int MESSAGE_TYPE_IMAGE_VALUE = 2;
  /**
   * <code>MESSAGE_TYPE_AUDIO = 3;</code>
   */
  public static final int MESSAGE_TYPE_AUDIO_VALUE = 3;
  /**
   * <code>MESSAGE_TYPE_VIDEO = 4;</code>
   */
  public static final int MESSAGE_TYPE_VIDEO_VALUE = 4;
  /**
   * <code>MESSAGE_TYPE_FILE = 5;</code>
   */
  public static final int MESSAGE_TYPE_FILE_VALUE = 5;
  /**
   * <code>MESSAGE_TYPE_LOCATION = 6;</code>
   */
  public static final int MESSAGE_TYPE_LOCATION_VALUE = 6;
  /**
   * <code>MESSAGE_TYPE_SYSTEM = 7;</code>
   */
  public static final int MESSAGE_TYPE_SYSTEM_VALUE = 7;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static MessageType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static MessageType forNumber(int value) {
    switch (value) {
      case 0: return MESSAGE_TYPE_UNKNOWN;
      case 1: return MESSAGE_TYPE_TEXT;
      case 2: return MESSAGE_TYPE_IMAGE;
      case 3: return MESSAGE_TYPE_AUDIO;
      case 4: return MESSAGE_TYPE_VIDEO;
      case 5: return MESSAGE_TYPE_FILE;
      case 6: return MESSAGE_TYPE_LOCATION;
      case 7: return MESSAGE_TYPE_SYSTEM;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<MessageType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      MessageType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<MessageType>() {
          public MessageType findValueByNumber(int number) {
            return MessageType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.getDescriptor().getEnumTypes().get(1);
  }

  private static final MessageType[] VALUES = values();

  public static MessageType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private MessageType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.zlim.message.MessageType)
}

