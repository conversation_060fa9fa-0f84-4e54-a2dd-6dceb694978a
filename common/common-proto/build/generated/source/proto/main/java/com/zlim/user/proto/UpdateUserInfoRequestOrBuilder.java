// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface UpdateUserInfoRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.UpdateUserInfoRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>string nickname = 2;</code>
   * @return The nickname.
   */
  java.lang.String getNickname();
  /**
   * <code>string nickname = 2;</code>
   * @return The bytes for nickname.
   */
  com.google.protobuf.ByteString
      getNicknameBytes();

  /**
   * <code>string avatar = 3;</code>
   * @return The avatar.
   */
  java.lang.String getAvatar();
  /**
   * <code>string avatar = 3;</code>
   * @return The bytes for avatar.
   */
  com.google.protobuf.ByteString
      getAvatarBytes();

  /**
   * <code>string bio = 4;</code>
   * @return The bio.
   */
  java.lang.String getBio();
  /**
   * <code>string bio = 4;</code>
   * @return The bytes for bio.
   */
  com.google.protobuf.ByteString
      getBioBytes();

  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  int getExtraCount();
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  boolean containsExtra(
      java.lang.String key);
  /**
   * Use {@link #getExtraMap()} instead.
   */
  @java.lang.Deprecated
  java.util.Map<java.lang.String, java.lang.String>
  getExtra();
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  java.util.Map<java.lang.String, java.lang.String>
  getExtraMap();
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  /* nullable */
java.lang.String getExtraOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue);
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  java.lang.String getExtraOrThrow(
      java.lang.String key);
}
