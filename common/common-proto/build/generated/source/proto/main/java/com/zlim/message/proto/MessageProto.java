// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public final class MessageProto {
  private MessageProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_Frame_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_Frame_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_ConnectRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_ConnectRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_ConnectResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_ConnectResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_Message_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_Message_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_Message_ExtraEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_Message_ExtraEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_MessageContent_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_MessageContent_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_TextContent_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_TextContent_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_MediaContent_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_MediaContent_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_LocationContent_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_LocationContent_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SystemContent_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SystemContent_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SystemContent_ParamsEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SystemContent_ParamsEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_MentionInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_MentionInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_MessageAck_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_MessageAck_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_TypingStatus_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_TypingStatus_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_PresenceStatus_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_PresenceStatus_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rmessage.proto\022\020com.zlim.message\032\014commo" +
      "n.proto\"c\n\005Frame\022)\n\004type\030\001 \001(\0162\033.com.zli" +
      "m.message.FrameType\022\013\n\003seq\030\002 \001(\003\022\017\n\007payl" +
      "oad\030\003 \001(\014\022\021\n\ttimestamp\030\004 \001(\003\"x\n\016ConnectR" +
      "equest\022\024\n\014access_token\030\001 \001(\t\022+\n\006device\030\002" +
      " \001(\0132\033.com.zlim.common.DeviceInfo\022\021\n\trec" +
      "onnect\030\003 \001(\010\022\020\n\010last_seq\030\004 \001(\003\"b\n\017Connec" +
      "tResponse\022\017\n\007success\030\001 \001(\010\022\022\n\nsession_id" +
      "\030\002 \001(\t\022\023\n\013server_time\030\003 \001(\003\022\025\n\rerror_mes" +
      "sage\030\004 \001(\t\"\320\002\n\007Message\022\022\n\nmessage_id\030\001 \001" +
      "(\t\022\027\n\017conversation_id\030\002 \001(\t\022\021\n\tsender_id" +
      "\030\003 \001(\003\022+\n\004type\030\004 \001(\0162\035.com.zlim.message." +
      "MessageType\0221\n\007content\030\005 \001(\0132 .com.zlim." +
      "message.MessageContent\022\021\n\ttimestamp\030\006 \001(" +
      "\003\022/\n\006status\030\007 \001(\0162\037.com.zlim.message.Mes" +
      "sageStatus\0223\n\005extra\030\010 \003(\0132$.com.zlim.mes" +
      "sage.Message.ExtraEntry\032,\n\nExtraEntry\022\013\n" +
      "\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\345\001\n\016Messag" +
      "eContent\022-\n\004text\030\001 \001(\0132\035.com.zlim.messag" +
      "e.TextContentH\000\022/\n\005media\030\002 \001(\0132\036.com.zli" +
      "m.message.MediaContentH\000\0225\n\010location\030\003 \001" +
      "(\0132!.com.zlim.message.LocationContentH\000\022" +
      "1\n\006system\030\004 \001(\0132\037.com.zlim.message.Syste" +
      "mContentH\000B\t\n\007content\"L\n\013TextContent\022\014\n\004" +
      "text\030\001 \001(\t\022/\n\010mentions\030\002 \003(\0132\035.com.zlim." +
      "message.MentionInfo\"\226\001\n\014MediaContent\022\013\n\003" +
      "url\030\001 \001(\t\022\025\n\rthumbnail_url\030\002 \001(\t\022\014\n\004size" +
      "\030\003 \001(\003\022\r\n\005width\030\004 \001(\005\022\016\n\006height\030\005 \001(\005\022\020\n" +
      "\010duration\030\006 \001(\005\022\021\n\tmime_type\030\007 \001(\t\022\020\n\010fi" +
      "lename\030\010 \001(\t\"V\n\017LocationContent\022\020\n\010latit" +
      "ude\030\001 \001(\001\022\021\n\tlongitude\030\002 \001(\001\022\017\n\007address\030" +
      "\003 \001(\t\022\r\n\005title\030\004 \001(\t\"\232\001\n\rSystemContent\022\014" +
      "\n\004type\030\001 \001(\t\022\017\n\007content\030\002 \001(\t\022;\n\006params\030" +
      "\003 \003(\0132+.com.zlim.message.SystemContent.P" +
      "aramsEntry\032-\n\013ParamsEntry\022\013\n\003key\030\001 \001(\t\022\r" +
      "\n\005value\030\002 \001(\t:\0028\001\"P\n\013MentionInfo\022\017\n\007user" +
      "_id\030\001 \001(\003\022\020\n\010username\030\002 \001(\t\022\016\n\006offset\030\003 " +
      "\001(\005\022\016\n\006length\030\004 \001(\005\"d\n\nMessageAck\022\022\n\nmes" +
      "sage_id\030\001 \001(\t\022/\n\006status\030\002 \001(\0162\037.com.zlim" +
      ".message.MessageStatus\022\021\n\ttimestamp\030\003 \001(" +
      "\003\"H\n\014TypingStatus\022\027\n\017conversation_id\030\001 \001" +
      "(\t\022\017\n\007user_id\030\002 \001(\003\022\016\n\006typing\030\003 \001(\010\"d\n\016P" +
      "resenceStatus\022\017\n\007user_id\030\001 \001(\003\022.\n\006status" +
      "\030\002 \001(\0162\036.com.zlim.message.PresenceType\022\021" +
      "\n\tlast_seen\030\003 \001(\003*\200\002\n\tFrameType\022\026\n\022FRAME" +
      "_TYPE_UNKNOWN\020\000\022\023\n\017FRAME_TYPE_PING\020\001\022\023\n\017" +
      "FRAME_TYPE_PONG\020\002\022\026\n\022FRAME_TYPE_CONNECT\020" +
      "\003\022\032\n\026FRAME_TYPE_CONNECT_ACK\020\004\022\031\n\025FRAME_T" +
      "YPE_DISCONNECT\020\005\022\026\n\022FRAME_TYPE_MESSAGE\020\006" +
      "\022\032\n\026FRAME_TYPE_MESSAGE_ACK\020\007\022\025\n\021FRAME_TY" +
      "PE_TYPING\020\010\022\027\n\023FRAME_TYPE_PRESENCE\020\t*\321\001\n" +
      "\013MessageType\022\030\n\024MESSAGE_TYPE_UNKNOWN\020\000\022\025" +
      "\n\021MESSAGE_TYPE_TEXT\020\001\022\026\n\022MESSAGE_TYPE_IM" +
      "AGE\020\002\022\026\n\022MESSAGE_TYPE_AUDIO\020\003\022\026\n\022MESSAGE" +
      "_TYPE_VIDEO\020\004\022\025\n\021MESSAGE_TYPE_FILE\020\005\022\031\n\025" +
      "MESSAGE_TYPE_LOCATION\020\006\022\027\n\023MESSAGE_TYPE_" +
      "SYSTEM\020\007*\262\001\n\rMessageStatus\022\032\n\026MESSAGE_ST" +
      "ATUS_UNKNOWN\020\000\022\032\n\026MESSAGE_STATUS_SENDING" +
      "\020\001\022\027\n\023MESSAGE_STATUS_SENT\020\002\022\034\n\030MESSAGE_S" +
      "TATUS_DELIVERED\020\003\022\027\n\023MESSAGE_STATUS_READ" +
      "\020\004\022\031\n\025MESSAGE_STATUS_FAILED\020\005*\216\001\n\014Presen" +
      "ceType\022\031\n\025PRESENCE_TYPE_UNKNOWN\020\000\022\030\n\024PRE" +
      "SENCE_TYPE_ONLINE\020\001\022\026\n\022PRESENCE_TYPE_AWA" +
      "Y\020\002\022\026\n\022PRESENCE_TYPE_BUSY\020\003\022\031\n\025PRESENCE_" +
      "TYPE_OFFLINE\020\004B(\n\026com.zlim.message.proto" +
      "B\014MessageProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.zlim.common.proto.CommonProto.getDescriptor(),
        });
    internal_static_com_zlim_message_Frame_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_zlim_message_Frame_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_Frame_descriptor,
        new java.lang.String[] { "Type", "Seq", "Payload", "Timestamp", });
    internal_static_com_zlim_message_ConnectRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_zlim_message_ConnectRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_ConnectRequest_descriptor,
        new java.lang.String[] { "AccessToken", "Device", "Reconnect", "LastSeq", });
    internal_static_com_zlim_message_ConnectResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_zlim_message_ConnectResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_ConnectResponse_descriptor,
        new java.lang.String[] { "Success", "SessionId", "ServerTime", "ErrorMessage", });
    internal_static_com_zlim_message_Message_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_zlim_message_Message_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_Message_descriptor,
        new java.lang.String[] { "MessageId", "ConversationId", "SenderId", "Type", "Content", "Timestamp", "Status", "Extra", });
    internal_static_com_zlim_message_Message_ExtraEntry_descriptor =
      internal_static_com_zlim_message_Message_descriptor.getNestedTypes().get(0);
    internal_static_com_zlim_message_Message_ExtraEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_Message_ExtraEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_zlim_message_MessageContent_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_zlim_message_MessageContent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_MessageContent_descriptor,
        new java.lang.String[] { "Text", "Media", "Location", "System", "Content", });
    internal_static_com_zlim_message_TextContent_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_zlim_message_TextContent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_TextContent_descriptor,
        new java.lang.String[] { "Text", "Mentions", });
    internal_static_com_zlim_message_MediaContent_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_zlim_message_MediaContent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_MediaContent_descriptor,
        new java.lang.String[] { "Url", "ThumbnailUrl", "Size", "Width", "Height", "Duration", "MimeType", "Filename", });
    internal_static_com_zlim_message_LocationContent_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_zlim_message_LocationContent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_LocationContent_descriptor,
        new java.lang.String[] { "Latitude", "Longitude", "Address", "Title", });
    internal_static_com_zlim_message_SystemContent_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_zlim_message_SystemContent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SystemContent_descriptor,
        new java.lang.String[] { "Type", "Content", "Params", });
    internal_static_com_zlim_message_SystemContent_ParamsEntry_descriptor =
      internal_static_com_zlim_message_SystemContent_descriptor.getNestedTypes().get(0);
    internal_static_com_zlim_message_SystemContent_ParamsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SystemContent_ParamsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_zlim_message_MentionInfo_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_zlim_message_MentionInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_MentionInfo_descriptor,
        new java.lang.String[] { "UserId", "Username", "Offset", "Length", });
    internal_static_com_zlim_message_MessageAck_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_zlim_message_MessageAck_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_MessageAck_descriptor,
        new java.lang.String[] { "MessageId", "Status", "Timestamp", });
    internal_static_com_zlim_message_TypingStatus_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_zlim_message_TypingStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_TypingStatus_descriptor,
        new java.lang.String[] { "ConversationId", "UserId", "Typing", });
    internal_static_com_zlim_message_PresenceStatus_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_zlim_message_PresenceStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_PresenceStatus_descriptor,
        new java.lang.String[] { "UserId", "Status", "LastSeen", });
    com.zlim.common.proto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
