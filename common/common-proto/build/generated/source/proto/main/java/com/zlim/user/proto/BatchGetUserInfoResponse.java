// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

/**
 * <pre>
 * 批量获取用户信息响应
 * </pre>
 *
 * Protobuf type {@code com.zlim.user.BatchGetUserInfoResponse}
 */
public final class BatchGetUserInfoResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.user.BatchGetUserInfoResponse)
    BatchGetUserInfoResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use BatchGetUserInfoResponse.newBuilder() to construct.
  private BatchGetUserInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private BatchGetUserInfoResponse() {
    userInfos_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new BatchGetUserInfoResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_BatchGetUserInfoResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_BatchGetUserInfoResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.user.proto.BatchGetUserInfoResponse.class, com.zlim.user.proto.BatchGetUserInfoResponse.Builder.class);
  }

  public static final int USER_INFOS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.zlim.common.proto.UserInfo> userInfos_;
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.zlim.common.proto.UserInfo> getUserInfosList() {
    return userInfos_;
  }
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.zlim.common.proto.UserInfoOrBuilder> 
      getUserInfosOrBuilderList() {
    return userInfos_;
  }
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  @java.lang.Override
  public int getUserInfosCount() {
    return userInfos_.size();
  }
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  @java.lang.Override
  public com.zlim.common.proto.UserInfo getUserInfos(int index) {
    return userInfos_.get(index);
  }
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  @java.lang.Override
  public com.zlim.common.proto.UserInfoOrBuilder getUserInfosOrBuilder(
      int index) {
    return userInfos_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < userInfos_.size(); i++) {
      output.writeMessage(1, userInfos_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < userInfos_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, userInfos_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.user.proto.BatchGetUserInfoResponse)) {
      return super.equals(obj);
    }
    com.zlim.user.proto.BatchGetUserInfoResponse other = (com.zlim.user.proto.BatchGetUserInfoResponse) obj;

    if (!getUserInfosList()
        .equals(other.getUserInfosList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getUserInfosCount() > 0) {
      hash = (37 * hash) + USER_INFOS_FIELD_NUMBER;
      hash = (53 * hash) + getUserInfosList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.user.proto.BatchGetUserInfoResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.user.proto.BatchGetUserInfoResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.user.proto.BatchGetUserInfoResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.user.proto.BatchGetUserInfoResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 批量获取用户信息响应
   * </pre>
   *
   * Protobuf type {@code com.zlim.user.BatchGetUserInfoResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.user.BatchGetUserInfoResponse)
      com.zlim.user.proto.BatchGetUserInfoResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_BatchGetUserInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_BatchGetUserInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.user.proto.BatchGetUserInfoResponse.class, com.zlim.user.proto.BatchGetUserInfoResponse.Builder.class);
    }

    // Construct using com.zlim.user.proto.BatchGetUserInfoResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (userInfosBuilder_ == null) {
        userInfos_ = java.util.Collections.emptyList();
      } else {
        userInfos_ = null;
        userInfosBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_BatchGetUserInfoResponse_descriptor;
    }

    @java.lang.Override
    public com.zlim.user.proto.BatchGetUserInfoResponse getDefaultInstanceForType() {
      return com.zlim.user.proto.BatchGetUserInfoResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.user.proto.BatchGetUserInfoResponse build() {
      com.zlim.user.proto.BatchGetUserInfoResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.user.proto.BatchGetUserInfoResponse buildPartial() {
      com.zlim.user.proto.BatchGetUserInfoResponse result = new com.zlim.user.proto.BatchGetUserInfoResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.zlim.user.proto.BatchGetUserInfoResponse result) {
      if (userInfosBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          userInfos_ = java.util.Collections.unmodifiableList(userInfos_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.userInfos_ = userInfos_;
      } else {
        result.userInfos_ = userInfosBuilder_.build();
      }
    }

    private void buildPartial0(com.zlim.user.proto.BatchGetUserInfoResponse result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.user.proto.BatchGetUserInfoResponse) {
        return mergeFrom((com.zlim.user.proto.BatchGetUserInfoResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.user.proto.BatchGetUserInfoResponse other) {
      if (other == com.zlim.user.proto.BatchGetUserInfoResponse.getDefaultInstance()) return this;
      if (userInfosBuilder_ == null) {
        if (!other.userInfos_.isEmpty()) {
          if (userInfos_.isEmpty()) {
            userInfos_ = other.userInfos_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureUserInfosIsMutable();
            userInfos_.addAll(other.userInfos_);
          }
          onChanged();
        }
      } else {
        if (!other.userInfos_.isEmpty()) {
          if (userInfosBuilder_.isEmpty()) {
            userInfosBuilder_.dispose();
            userInfosBuilder_ = null;
            userInfos_ = other.userInfos_;
            bitField0_ = (bitField0_ & ~0x00000001);
            userInfosBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getUserInfosFieldBuilder() : null;
          } else {
            userInfosBuilder_.addAllMessages(other.userInfos_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.zlim.common.proto.UserInfo m =
                  input.readMessage(
                      com.zlim.common.proto.UserInfo.parser(),
                      extensionRegistry);
              if (userInfosBuilder_ == null) {
                ensureUserInfosIsMutable();
                userInfos_.add(m);
              } else {
                userInfosBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.zlim.common.proto.UserInfo> userInfos_ =
      java.util.Collections.emptyList();
    private void ensureUserInfosIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        userInfos_ = new java.util.ArrayList<com.zlim.common.proto.UserInfo>(userInfos_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.common.proto.UserInfo, com.zlim.common.proto.UserInfo.Builder, com.zlim.common.proto.UserInfoOrBuilder> userInfosBuilder_;

    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public java.util.List<com.zlim.common.proto.UserInfo> getUserInfosList() {
      if (userInfosBuilder_ == null) {
        return java.util.Collections.unmodifiableList(userInfos_);
      } else {
        return userInfosBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public int getUserInfosCount() {
      if (userInfosBuilder_ == null) {
        return userInfos_.size();
      } else {
        return userInfosBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public com.zlim.common.proto.UserInfo getUserInfos(int index) {
      if (userInfosBuilder_ == null) {
        return userInfos_.get(index);
      } else {
        return userInfosBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder setUserInfos(
        int index, com.zlim.common.proto.UserInfo value) {
      if (userInfosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUserInfosIsMutable();
        userInfos_.set(index, value);
        onChanged();
      } else {
        userInfosBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder setUserInfos(
        int index, com.zlim.common.proto.UserInfo.Builder builderForValue) {
      if (userInfosBuilder_ == null) {
        ensureUserInfosIsMutable();
        userInfos_.set(index, builderForValue.build());
        onChanged();
      } else {
        userInfosBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder addUserInfos(com.zlim.common.proto.UserInfo value) {
      if (userInfosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUserInfosIsMutable();
        userInfos_.add(value);
        onChanged();
      } else {
        userInfosBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder addUserInfos(
        int index, com.zlim.common.proto.UserInfo value) {
      if (userInfosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUserInfosIsMutable();
        userInfos_.add(index, value);
        onChanged();
      } else {
        userInfosBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder addUserInfos(
        com.zlim.common.proto.UserInfo.Builder builderForValue) {
      if (userInfosBuilder_ == null) {
        ensureUserInfosIsMutable();
        userInfos_.add(builderForValue.build());
        onChanged();
      } else {
        userInfosBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder addUserInfos(
        int index, com.zlim.common.proto.UserInfo.Builder builderForValue) {
      if (userInfosBuilder_ == null) {
        ensureUserInfosIsMutable();
        userInfos_.add(index, builderForValue.build());
        onChanged();
      } else {
        userInfosBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder addAllUserInfos(
        java.lang.Iterable<? extends com.zlim.common.proto.UserInfo> values) {
      if (userInfosBuilder_ == null) {
        ensureUserInfosIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, userInfos_);
        onChanged();
      } else {
        userInfosBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder clearUserInfos() {
      if (userInfosBuilder_ == null) {
        userInfos_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        userInfosBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public Builder removeUserInfos(int index) {
      if (userInfosBuilder_ == null) {
        ensureUserInfosIsMutable();
        userInfos_.remove(index);
        onChanged();
      } else {
        userInfosBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public com.zlim.common.proto.UserInfo.Builder getUserInfosBuilder(
        int index) {
      return getUserInfosFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public com.zlim.common.proto.UserInfoOrBuilder getUserInfosOrBuilder(
        int index) {
      if (userInfosBuilder_ == null) {
        return userInfos_.get(index);  } else {
        return userInfosBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public java.util.List<? extends com.zlim.common.proto.UserInfoOrBuilder> 
         getUserInfosOrBuilderList() {
      if (userInfosBuilder_ != null) {
        return userInfosBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(userInfos_);
      }
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public com.zlim.common.proto.UserInfo.Builder addUserInfosBuilder() {
      return getUserInfosFieldBuilder().addBuilder(
          com.zlim.common.proto.UserInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public com.zlim.common.proto.UserInfo.Builder addUserInfosBuilder(
        int index) {
      return getUserInfosFieldBuilder().addBuilder(
          index, com.zlim.common.proto.UserInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
     */
    public java.util.List<com.zlim.common.proto.UserInfo.Builder> 
         getUserInfosBuilderList() {
      return getUserInfosFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.common.proto.UserInfo, com.zlim.common.proto.UserInfo.Builder, com.zlim.common.proto.UserInfoOrBuilder> 
        getUserInfosFieldBuilder() {
      if (userInfosBuilder_ == null) {
        userInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.zlim.common.proto.UserInfo, com.zlim.common.proto.UserInfo.Builder, com.zlim.common.proto.UserInfoOrBuilder>(
                userInfos_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        userInfos_ = null;
      }
      return userInfosBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.user.BatchGetUserInfoResponse)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.user.BatchGetUserInfoResponse)
  private static final com.zlim.user.proto.BatchGetUserInfoResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.user.proto.BatchGetUserInfoResponse();
  }

  public static com.zlim.user.proto.BatchGetUserInfoResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BatchGetUserInfoResponse>
      PARSER = new com.google.protobuf.AbstractParser<BatchGetUserInfoResponse>() {
    @java.lang.Override
    public BatchGetUserInfoResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BatchGetUserInfoResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BatchGetUserInfoResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.user.proto.BatchGetUserInfoResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

