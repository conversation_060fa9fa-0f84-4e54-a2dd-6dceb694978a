// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 消息内容
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.MessageContent}
 */
public final class MessageContent extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.MessageContent)
    MessageContentOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MessageContent.newBuilder() to construct.
  private MessageContent(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MessageContent() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MessageContent();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_MessageContent_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_MessageContent_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.MessageContent.class, com.zlim.message.proto.MessageContent.Builder.class);
  }

  private int contentCase_ = 0;
  @SuppressWarnings("serial")
  private java.lang.Object content_;
  public enum ContentCase
      implements com.google.protobuf.Internal.EnumLite,
          com.google.protobuf.AbstractMessage.InternalOneOfEnum {
    TEXT(1),
    MEDIA(2),
    LOCATION(3),
    SYSTEM(4),
    CONTENT_NOT_SET(0);
    private final int value;
    private ContentCase(int value) {
      this.value = value;
    }
    /**
     * @param value The number of the enum to look for.
     * @return The enum associated with the given number.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ContentCase valueOf(int value) {
      return forNumber(value);
    }

    public static ContentCase forNumber(int value) {
      switch (value) {
        case 1: return TEXT;
        case 2: return MEDIA;
        case 3: return LOCATION;
        case 4: return SYSTEM;
        case 0: return CONTENT_NOT_SET;
        default: return null;
      }
    }
    public int getNumber() {
      return this.value;
    }
  };

  public ContentCase
  getContentCase() {
    return ContentCase.forNumber(
        contentCase_);
  }

  public static final int TEXT_FIELD_NUMBER = 1;
  /**
   * <code>.com.zlim.message.TextContent text = 1;</code>
   * @return Whether the text field is set.
   */
  @java.lang.Override
  public boolean hasText() {
    return contentCase_ == 1;
  }
  /**
   * <code>.com.zlim.message.TextContent text = 1;</code>
   * @return The text.
   */
  @java.lang.Override
  public com.zlim.message.proto.TextContent getText() {
    if (contentCase_ == 1) {
       return (com.zlim.message.proto.TextContent) content_;
    }
    return com.zlim.message.proto.TextContent.getDefaultInstance();
  }
  /**
   * <code>.com.zlim.message.TextContent text = 1;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.TextContentOrBuilder getTextOrBuilder() {
    if (contentCase_ == 1) {
       return (com.zlim.message.proto.TextContent) content_;
    }
    return com.zlim.message.proto.TextContent.getDefaultInstance();
  }

  public static final int MEDIA_FIELD_NUMBER = 2;
  /**
   * <code>.com.zlim.message.MediaContent media = 2;</code>
   * @return Whether the media field is set.
   */
  @java.lang.Override
  public boolean hasMedia() {
    return contentCase_ == 2;
  }
  /**
   * <code>.com.zlim.message.MediaContent media = 2;</code>
   * @return The media.
   */
  @java.lang.Override
  public com.zlim.message.proto.MediaContent getMedia() {
    if (contentCase_ == 2) {
       return (com.zlim.message.proto.MediaContent) content_;
    }
    return com.zlim.message.proto.MediaContent.getDefaultInstance();
  }
  /**
   * <code>.com.zlim.message.MediaContent media = 2;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.MediaContentOrBuilder getMediaOrBuilder() {
    if (contentCase_ == 2) {
       return (com.zlim.message.proto.MediaContent) content_;
    }
    return com.zlim.message.proto.MediaContent.getDefaultInstance();
  }

  public static final int LOCATION_FIELD_NUMBER = 3;
  /**
   * <code>.com.zlim.message.LocationContent location = 3;</code>
   * @return Whether the location field is set.
   */
  @java.lang.Override
  public boolean hasLocation() {
    return contentCase_ == 3;
  }
  /**
   * <code>.com.zlim.message.LocationContent location = 3;</code>
   * @return The location.
   */
  @java.lang.Override
  public com.zlim.message.proto.LocationContent getLocation() {
    if (contentCase_ == 3) {
       return (com.zlim.message.proto.LocationContent) content_;
    }
    return com.zlim.message.proto.LocationContent.getDefaultInstance();
  }
  /**
   * <code>.com.zlim.message.LocationContent location = 3;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.LocationContentOrBuilder getLocationOrBuilder() {
    if (contentCase_ == 3) {
       return (com.zlim.message.proto.LocationContent) content_;
    }
    return com.zlim.message.proto.LocationContent.getDefaultInstance();
  }

  public static final int SYSTEM_FIELD_NUMBER = 4;
  /**
   * <code>.com.zlim.message.SystemContent system = 4;</code>
   * @return Whether the system field is set.
   */
  @java.lang.Override
  public boolean hasSystem() {
    return contentCase_ == 4;
  }
  /**
   * <code>.com.zlim.message.SystemContent system = 4;</code>
   * @return The system.
   */
  @java.lang.Override
  public com.zlim.message.proto.SystemContent getSystem() {
    if (contentCase_ == 4) {
       return (com.zlim.message.proto.SystemContent) content_;
    }
    return com.zlim.message.proto.SystemContent.getDefaultInstance();
  }
  /**
   * <code>.com.zlim.message.SystemContent system = 4;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.SystemContentOrBuilder getSystemOrBuilder() {
    if (contentCase_ == 4) {
       return (com.zlim.message.proto.SystemContent) content_;
    }
    return com.zlim.message.proto.SystemContent.getDefaultInstance();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (contentCase_ == 1) {
      output.writeMessage(1, (com.zlim.message.proto.TextContent) content_);
    }
    if (contentCase_ == 2) {
      output.writeMessage(2, (com.zlim.message.proto.MediaContent) content_);
    }
    if (contentCase_ == 3) {
      output.writeMessage(3, (com.zlim.message.proto.LocationContent) content_);
    }
    if (contentCase_ == 4) {
      output.writeMessage(4, (com.zlim.message.proto.SystemContent) content_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (contentCase_ == 1) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, (com.zlim.message.proto.TextContent) content_);
    }
    if (contentCase_ == 2) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, (com.zlim.message.proto.MediaContent) content_);
    }
    if (contentCase_ == 3) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, (com.zlim.message.proto.LocationContent) content_);
    }
    if (contentCase_ == 4) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, (com.zlim.message.proto.SystemContent) content_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.MessageContent)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.MessageContent other = (com.zlim.message.proto.MessageContent) obj;

    if (!getContentCase().equals(other.getContentCase())) return false;
    switch (contentCase_) {
      case 1:
        if (!getText()
            .equals(other.getText())) return false;
        break;
      case 2:
        if (!getMedia()
            .equals(other.getMedia())) return false;
        break;
      case 3:
        if (!getLocation()
            .equals(other.getLocation())) return false;
        break;
      case 4:
        if (!getSystem()
            .equals(other.getSystem())) return false;
        break;
      case 0:
      default:
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    switch (contentCase_) {
      case 1:
        hash = (37 * hash) + TEXT_FIELD_NUMBER;
        hash = (53 * hash) + getText().hashCode();
        break;
      case 2:
        hash = (37 * hash) + MEDIA_FIELD_NUMBER;
        hash = (53 * hash) + getMedia().hashCode();
        break;
      case 3:
        hash = (37 * hash) + LOCATION_FIELD_NUMBER;
        hash = (53 * hash) + getLocation().hashCode();
        break;
      case 4:
        hash = (37 * hash) + SYSTEM_FIELD_NUMBER;
        hash = (53 * hash) + getSystem().hashCode();
        break;
      case 0:
      default:
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.MessageContent parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.MessageContent parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.MessageContent parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.MessageContent parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.MessageContent prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 消息内容
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.MessageContent}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.MessageContent)
      com.zlim.message.proto.MessageContentOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_MessageContent_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_MessageContent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.MessageContent.class, com.zlim.message.proto.MessageContent.Builder.class);
    }

    // Construct using com.zlim.message.proto.MessageContent.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (textBuilder_ != null) {
        textBuilder_.clear();
      }
      if (mediaBuilder_ != null) {
        mediaBuilder_.clear();
      }
      if (locationBuilder_ != null) {
        locationBuilder_.clear();
      }
      if (systemBuilder_ != null) {
        systemBuilder_.clear();
      }
      contentCase_ = 0;
      content_ = null;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_MessageContent_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.MessageContent getDefaultInstanceForType() {
      return com.zlim.message.proto.MessageContent.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.MessageContent build() {
      com.zlim.message.proto.MessageContent result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.MessageContent buildPartial() {
      com.zlim.message.proto.MessageContent result = new com.zlim.message.proto.MessageContent(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      buildPartialOneofs(result);
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.message.proto.MessageContent result) {
      int from_bitField0_ = bitField0_;
    }

    private void buildPartialOneofs(com.zlim.message.proto.MessageContent result) {
      result.contentCase_ = contentCase_;
      result.content_ = this.content_;
      if (contentCase_ == 1 &&
          textBuilder_ != null) {
        result.content_ = textBuilder_.build();
      }
      if (contentCase_ == 2 &&
          mediaBuilder_ != null) {
        result.content_ = mediaBuilder_.build();
      }
      if (contentCase_ == 3 &&
          locationBuilder_ != null) {
        result.content_ = locationBuilder_.build();
      }
      if (contentCase_ == 4 &&
          systemBuilder_ != null) {
        result.content_ = systemBuilder_.build();
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.MessageContent) {
        return mergeFrom((com.zlim.message.proto.MessageContent)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.MessageContent other) {
      if (other == com.zlim.message.proto.MessageContent.getDefaultInstance()) return this;
      switch (other.getContentCase()) {
        case TEXT: {
          mergeText(other.getText());
          break;
        }
        case MEDIA: {
          mergeMedia(other.getMedia());
          break;
        }
        case LOCATION: {
          mergeLocation(other.getLocation());
          break;
        }
        case SYSTEM: {
          mergeSystem(other.getSystem());
          break;
        }
        case CONTENT_NOT_SET: {
          break;
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  getTextFieldBuilder().getBuilder(),
                  extensionRegistry);
              contentCase_ = 1;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getMediaFieldBuilder().getBuilder(),
                  extensionRegistry);
              contentCase_ = 2;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  getLocationFieldBuilder().getBuilder(),
                  extensionRegistry);
              contentCase_ = 3;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  getSystemFieldBuilder().getBuilder(),
                  extensionRegistry);
              contentCase_ = 4;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int contentCase_ = 0;
    private java.lang.Object content_;
    public ContentCase
        getContentCase() {
      return ContentCase.forNumber(
          contentCase_);
    }

    public Builder clearContent() {
      contentCase_ = 0;
      content_ = null;
      onChanged();
      return this;
    }

    private int bitField0_;

    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.TextContent, com.zlim.message.proto.TextContent.Builder, com.zlim.message.proto.TextContentOrBuilder> textBuilder_;
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     * @return Whether the text field is set.
     */
    @java.lang.Override
    public boolean hasText() {
      return contentCase_ == 1;
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     * @return The text.
     */
    @java.lang.Override
    public com.zlim.message.proto.TextContent getText() {
      if (textBuilder_ == null) {
        if (contentCase_ == 1) {
          return (com.zlim.message.proto.TextContent) content_;
        }
        return com.zlim.message.proto.TextContent.getDefaultInstance();
      } else {
        if (contentCase_ == 1) {
          return textBuilder_.getMessage();
        }
        return com.zlim.message.proto.TextContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    public Builder setText(com.zlim.message.proto.TextContent value) {
      if (textBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        content_ = value;
        onChanged();
      } else {
        textBuilder_.setMessage(value);
      }
      contentCase_ = 1;
      return this;
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    public Builder setText(
        com.zlim.message.proto.TextContent.Builder builderForValue) {
      if (textBuilder_ == null) {
        content_ = builderForValue.build();
        onChanged();
      } else {
        textBuilder_.setMessage(builderForValue.build());
      }
      contentCase_ = 1;
      return this;
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    public Builder mergeText(com.zlim.message.proto.TextContent value) {
      if (textBuilder_ == null) {
        if (contentCase_ == 1 &&
            content_ != com.zlim.message.proto.TextContent.getDefaultInstance()) {
          content_ = com.zlim.message.proto.TextContent.newBuilder((com.zlim.message.proto.TextContent) content_)
              .mergeFrom(value).buildPartial();
        } else {
          content_ = value;
        }
        onChanged();
      } else {
        if (contentCase_ == 1) {
          textBuilder_.mergeFrom(value);
        } else {
          textBuilder_.setMessage(value);
        }
      }
      contentCase_ = 1;
      return this;
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    public Builder clearText() {
      if (textBuilder_ == null) {
        if (contentCase_ == 1) {
          contentCase_ = 0;
          content_ = null;
          onChanged();
        }
      } else {
        if (contentCase_ == 1) {
          contentCase_ = 0;
          content_ = null;
        }
        textBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    public com.zlim.message.proto.TextContent.Builder getTextBuilder() {
      return getTextFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    @java.lang.Override
    public com.zlim.message.proto.TextContentOrBuilder getTextOrBuilder() {
      if ((contentCase_ == 1) && (textBuilder_ != null)) {
        return textBuilder_.getMessageOrBuilder();
      } else {
        if (contentCase_ == 1) {
          return (com.zlim.message.proto.TextContent) content_;
        }
        return com.zlim.message.proto.TextContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.TextContent text = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.TextContent, com.zlim.message.proto.TextContent.Builder, com.zlim.message.proto.TextContentOrBuilder> 
        getTextFieldBuilder() {
      if (textBuilder_ == null) {
        if (!(contentCase_ == 1)) {
          content_ = com.zlim.message.proto.TextContent.getDefaultInstance();
        }
        textBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.message.proto.TextContent, com.zlim.message.proto.TextContent.Builder, com.zlim.message.proto.TextContentOrBuilder>(
                (com.zlim.message.proto.TextContent) content_,
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      contentCase_ = 1;
      onChanged();
      return textBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.MediaContent, com.zlim.message.proto.MediaContent.Builder, com.zlim.message.proto.MediaContentOrBuilder> mediaBuilder_;
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     * @return Whether the media field is set.
     */
    @java.lang.Override
    public boolean hasMedia() {
      return contentCase_ == 2;
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     * @return The media.
     */
    @java.lang.Override
    public com.zlim.message.proto.MediaContent getMedia() {
      if (mediaBuilder_ == null) {
        if (contentCase_ == 2) {
          return (com.zlim.message.proto.MediaContent) content_;
        }
        return com.zlim.message.proto.MediaContent.getDefaultInstance();
      } else {
        if (contentCase_ == 2) {
          return mediaBuilder_.getMessage();
        }
        return com.zlim.message.proto.MediaContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    public Builder setMedia(com.zlim.message.proto.MediaContent value) {
      if (mediaBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        content_ = value;
        onChanged();
      } else {
        mediaBuilder_.setMessage(value);
      }
      contentCase_ = 2;
      return this;
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    public Builder setMedia(
        com.zlim.message.proto.MediaContent.Builder builderForValue) {
      if (mediaBuilder_ == null) {
        content_ = builderForValue.build();
        onChanged();
      } else {
        mediaBuilder_.setMessage(builderForValue.build());
      }
      contentCase_ = 2;
      return this;
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    public Builder mergeMedia(com.zlim.message.proto.MediaContent value) {
      if (mediaBuilder_ == null) {
        if (contentCase_ == 2 &&
            content_ != com.zlim.message.proto.MediaContent.getDefaultInstance()) {
          content_ = com.zlim.message.proto.MediaContent.newBuilder((com.zlim.message.proto.MediaContent) content_)
              .mergeFrom(value).buildPartial();
        } else {
          content_ = value;
        }
        onChanged();
      } else {
        if (contentCase_ == 2) {
          mediaBuilder_.mergeFrom(value);
        } else {
          mediaBuilder_.setMessage(value);
        }
      }
      contentCase_ = 2;
      return this;
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    public Builder clearMedia() {
      if (mediaBuilder_ == null) {
        if (contentCase_ == 2) {
          contentCase_ = 0;
          content_ = null;
          onChanged();
        }
      } else {
        if (contentCase_ == 2) {
          contentCase_ = 0;
          content_ = null;
        }
        mediaBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    public com.zlim.message.proto.MediaContent.Builder getMediaBuilder() {
      return getMediaFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    @java.lang.Override
    public com.zlim.message.proto.MediaContentOrBuilder getMediaOrBuilder() {
      if ((contentCase_ == 2) && (mediaBuilder_ != null)) {
        return mediaBuilder_.getMessageOrBuilder();
      } else {
        if (contentCase_ == 2) {
          return (com.zlim.message.proto.MediaContent) content_;
        }
        return com.zlim.message.proto.MediaContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.MediaContent media = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.MediaContent, com.zlim.message.proto.MediaContent.Builder, com.zlim.message.proto.MediaContentOrBuilder> 
        getMediaFieldBuilder() {
      if (mediaBuilder_ == null) {
        if (!(contentCase_ == 2)) {
          content_ = com.zlim.message.proto.MediaContent.getDefaultInstance();
        }
        mediaBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.message.proto.MediaContent, com.zlim.message.proto.MediaContent.Builder, com.zlim.message.proto.MediaContentOrBuilder>(
                (com.zlim.message.proto.MediaContent) content_,
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      contentCase_ = 2;
      onChanged();
      return mediaBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.LocationContent, com.zlim.message.proto.LocationContent.Builder, com.zlim.message.proto.LocationContentOrBuilder> locationBuilder_;
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     * @return Whether the location field is set.
     */
    @java.lang.Override
    public boolean hasLocation() {
      return contentCase_ == 3;
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     * @return The location.
     */
    @java.lang.Override
    public com.zlim.message.proto.LocationContent getLocation() {
      if (locationBuilder_ == null) {
        if (contentCase_ == 3) {
          return (com.zlim.message.proto.LocationContent) content_;
        }
        return com.zlim.message.proto.LocationContent.getDefaultInstance();
      } else {
        if (contentCase_ == 3) {
          return locationBuilder_.getMessage();
        }
        return com.zlim.message.proto.LocationContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    public Builder setLocation(com.zlim.message.proto.LocationContent value) {
      if (locationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        content_ = value;
        onChanged();
      } else {
        locationBuilder_.setMessage(value);
      }
      contentCase_ = 3;
      return this;
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    public Builder setLocation(
        com.zlim.message.proto.LocationContent.Builder builderForValue) {
      if (locationBuilder_ == null) {
        content_ = builderForValue.build();
        onChanged();
      } else {
        locationBuilder_.setMessage(builderForValue.build());
      }
      contentCase_ = 3;
      return this;
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    public Builder mergeLocation(com.zlim.message.proto.LocationContent value) {
      if (locationBuilder_ == null) {
        if (contentCase_ == 3 &&
            content_ != com.zlim.message.proto.LocationContent.getDefaultInstance()) {
          content_ = com.zlim.message.proto.LocationContent.newBuilder((com.zlim.message.proto.LocationContent) content_)
              .mergeFrom(value).buildPartial();
        } else {
          content_ = value;
        }
        onChanged();
      } else {
        if (contentCase_ == 3) {
          locationBuilder_.mergeFrom(value);
        } else {
          locationBuilder_.setMessage(value);
        }
      }
      contentCase_ = 3;
      return this;
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    public Builder clearLocation() {
      if (locationBuilder_ == null) {
        if (contentCase_ == 3) {
          contentCase_ = 0;
          content_ = null;
          onChanged();
        }
      } else {
        if (contentCase_ == 3) {
          contentCase_ = 0;
          content_ = null;
        }
        locationBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    public com.zlim.message.proto.LocationContent.Builder getLocationBuilder() {
      return getLocationFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    @java.lang.Override
    public com.zlim.message.proto.LocationContentOrBuilder getLocationOrBuilder() {
      if ((contentCase_ == 3) && (locationBuilder_ != null)) {
        return locationBuilder_.getMessageOrBuilder();
      } else {
        if (contentCase_ == 3) {
          return (com.zlim.message.proto.LocationContent) content_;
        }
        return com.zlim.message.proto.LocationContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.LocationContent location = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.LocationContent, com.zlim.message.proto.LocationContent.Builder, com.zlim.message.proto.LocationContentOrBuilder> 
        getLocationFieldBuilder() {
      if (locationBuilder_ == null) {
        if (!(contentCase_ == 3)) {
          content_ = com.zlim.message.proto.LocationContent.getDefaultInstance();
        }
        locationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.message.proto.LocationContent, com.zlim.message.proto.LocationContent.Builder, com.zlim.message.proto.LocationContentOrBuilder>(
                (com.zlim.message.proto.LocationContent) content_,
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      contentCase_ = 3;
      onChanged();
      return locationBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.SystemContent, com.zlim.message.proto.SystemContent.Builder, com.zlim.message.proto.SystemContentOrBuilder> systemBuilder_;
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     * @return Whether the system field is set.
     */
    @java.lang.Override
    public boolean hasSystem() {
      return contentCase_ == 4;
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     * @return The system.
     */
    @java.lang.Override
    public com.zlim.message.proto.SystemContent getSystem() {
      if (systemBuilder_ == null) {
        if (contentCase_ == 4) {
          return (com.zlim.message.proto.SystemContent) content_;
        }
        return com.zlim.message.proto.SystemContent.getDefaultInstance();
      } else {
        if (contentCase_ == 4) {
          return systemBuilder_.getMessage();
        }
        return com.zlim.message.proto.SystemContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    public Builder setSystem(com.zlim.message.proto.SystemContent value) {
      if (systemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        content_ = value;
        onChanged();
      } else {
        systemBuilder_.setMessage(value);
      }
      contentCase_ = 4;
      return this;
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    public Builder setSystem(
        com.zlim.message.proto.SystemContent.Builder builderForValue) {
      if (systemBuilder_ == null) {
        content_ = builderForValue.build();
        onChanged();
      } else {
        systemBuilder_.setMessage(builderForValue.build());
      }
      contentCase_ = 4;
      return this;
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    public Builder mergeSystem(com.zlim.message.proto.SystemContent value) {
      if (systemBuilder_ == null) {
        if (contentCase_ == 4 &&
            content_ != com.zlim.message.proto.SystemContent.getDefaultInstance()) {
          content_ = com.zlim.message.proto.SystemContent.newBuilder((com.zlim.message.proto.SystemContent) content_)
              .mergeFrom(value).buildPartial();
        } else {
          content_ = value;
        }
        onChanged();
      } else {
        if (contentCase_ == 4) {
          systemBuilder_.mergeFrom(value);
        } else {
          systemBuilder_.setMessage(value);
        }
      }
      contentCase_ = 4;
      return this;
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    public Builder clearSystem() {
      if (systemBuilder_ == null) {
        if (contentCase_ == 4) {
          contentCase_ = 0;
          content_ = null;
          onChanged();
        }
      } else {
        if (contentCase_ == 4) {
          contentCase_ = 0;
          content_ = null;
        }
        systemBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    public com.zlim.message.proto.SystemContent.Builder getSystemBuilder() {
      return getSystemFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    @java.lang.Override
    public com.zlim.message.proto.SystemContentOrBuilder getSystemOrBuilder() {
      if ((contentCase_ == 4) && (systemBuilder_ != null)) {
        return systemBuilder_.getMessageOrBuilder();
      } else {
        if (contentCase_ == 4) {
          return (com.zlim.message.proto.SystemContent) content_;
        }
        return com.zlim.message.proto.SystemContent.getDefaultInstance();
      }
    }
    /**
     * <code>.com.zlim.message.SystemContent system = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.SystemContent, com.zlim.message.proto.SystemContent.Builder, com.zlim.message.proto.SystemContentOrBuilder> 
        getSystemFieldBuilder() {
      if (systemBuilder_ == null) {
        if (!(contentCase_ == 4)) {
          content_ = com.zlim.message.proto.SystemContent.getDefaultInstance();
        }
        systemBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.message.proto.SystemContent, com.zlim.message.proto.SystemContent.Builder, com.zlim.message.proto.SystemContentOrBuilder>(
                (com.zlim.message.proto.SystemContent) content_,
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      contentCase_ = 4;
      onChanged();
      return systemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.MessageContent)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.MessageContent)
  private static final com.zlim.message.proto.MessageContent DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.MessageContent();
  }

  public static com.zlim.message.proto.MessageContent getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MessageContent>
      PARSER = new com.google.protobuf.AbstractParser<MessageContent>() {
    @java.lang.Override
    public MessageContent parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MessageContent> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MessageContent> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.MessageContent getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

