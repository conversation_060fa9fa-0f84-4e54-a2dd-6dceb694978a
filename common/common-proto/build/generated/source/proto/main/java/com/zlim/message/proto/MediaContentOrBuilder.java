// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface MediaContentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.MediaContent)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string url = 1;</code>
   * @return The url.
   */
  java.lang.String getUrl();
  /**
   * <code>string url = 1;</code>
   * @return The bytes for url.
   */
  com.google.protobuf.ByteString
      getUrlBytes();

  /**
   * <code>string thumbnail_url = 2;</code>
   * @return The thumbnailUrl.
   */
  java.lang.String getThumbnailUrl();
  /**
   * <code>string thumbnail_url = 2;</code>
   * @return The bytes for thumbnailUrl.
   */
  com.google.protobuf.ByteString
      getThumbnailUrlBytes();

  /**
   * <code>int64 size = 3;</code>
   * @return The size.
   */
  long getSize();

  /**
   * <code>int32 width = 4;</code>
   * @return The width.
   */
  int getWidth();

  /**
   * <code>int32 height = 5;</code>
   * @return The height.
   */
  int getHeight();

  /**
   * <pre>
   * 音视频时长(秒)
   * </pre>
   *
   * <code>int32 duration = 6;</code>
   * @return The duration.
   */
  int getDuration();

  /**
   * <code>string mime_type = 7;</code>
   * @return The mimeType.
   */
  java.lang.String getMimeType();
  /**
   * <code>string mime_type = 7;</code>
   * @return The bytes for mimeType.
   */
  com.google.protobuf.ByteString
      getMimeTypeBytes();

  /**
   * <code>string filename = 8;</code>
   * @return The filename.
   */
  java.lang.String getFilename();
  /**
   * <code>string filename = 8;</code>
   * @return The bytes for filename.
   */
  com.google.protobuf.ByteString
      getFilenameBytes();
}
