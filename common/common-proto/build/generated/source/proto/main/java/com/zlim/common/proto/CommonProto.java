// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

public final class CommonProto {
  private CommonProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_common_Result_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_common_Result_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_common_PageRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_common_PageRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_common_PageResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_common_PageResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_common_UserInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_common_UserInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_common_DeviceInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_common_DeviceInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014common.proto\022\017com.zlim.common\032\031google/" +
      "protobuf/any.proto\"^\n\006Result\022\014\n\004code\030\001 \001" +
      "(\005\022\017\n\007message\030\002 \001(\t\022\"\n\004data\030\003 \001(\0132\024.goog" +
      "le.protobuf.Any\022\021\n\ttimestamp\030\004 \001(\003\"F\n\013Pa" +
      "geRequest\022\014\n\004page\030\001 \001(\005\022\014\n\004size\030\002 \001(\005\022\014\n" +
      "\004sort\030\003 \001(\t\022\r\n\005order\030\004 \001(\t\"`\n\014PageRespon" +
      "se\022\r\n\005total\030\001 \001(\003\022\014\n\004page\030\002 \001(\005\022\014\n\004size\030" +
      "\003 \001(\005\022%\n\007content\030\004 \003(\0132\024.google.protobuf" +
      ".Any\"\302\001\n\010UserInfo\022\017\n\007user_id\030\001 \001(\003\022\020\n\010us" +
      "ername\030\002 \001(\t\022\020\n\010nickname\030\003 \001(\t\022\016\n\006avatar" +
      "\030\004 \001(\t\022\r\n\005email\030\005 \001(\t\022\r\n\005phone\030\006 \001(\t\022+\n\006" +
      "status\030\007 \001(\0162\033.com.zlim.common.UserStatu" +
      "s\022\022\n\ncreated_at\030\010 \001(\003\022\022\n\nupdated_at\030\t \001(" +
      "\003\"q\n\nDeviceInfo\022\021\n\tdevice_id\030\001 \001(\t\022\023\n\013de" +
      "vice_type\030\002 \001(\t\022\023\n\013app_version\030\003 \001(\t\022\022\n\n" +
      "os_version\030\004 \001(\t\022\022\n\npush_token\030\005 \001(\t*\210\001\n" +
      "\nUserStatus\022\027\n\023USER_STATUS_UNKNOWN\020\000\022\026\n\022" +
      "USER_STATUS_ACTIVE\020\001\022\030\n\024USER_STATUS_INAC" +
      "TIVE\020\002\022\026\n\022USER_STATUS_BANNED\020\003\022\027\n\023USER_S" +
      "TATUS_DELETED\020\004B&\n\025com.zlim.common.proto" +
      "B\013CommonProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.AnyProto.getDescriptor(),
        });
    internal_static_com_zlim_common_Result_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_zlim_common_Result_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_common_Result_descriptor,
        new java.lang.String[] { "Code", "Message", "Data", "Timestamp", });
    internal_static_com_zlim_common_PageRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_zlim_common_PageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_common_PageRequest_descriptor,
        new java.lang.String[] { "Page", "Size", "Sort", "Order", });
    internal_static_com_zlim_common_PageResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_zlim_common_PageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_common_PageResponse_descriptor,
        new java.lang.String[] { "Total", "Page", "Size", "Content", });
    internal_static_com_zlim_common_UserInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_zlim_common_UserInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_common_UserInfo_descriptor,
        new java.lang.String[] { "UserId", "Username", "Nickname", "Avatar", "Email", "Phone", "Status", "CreatedAt", "UpdatedAt", });
    internal_static_com_zlim_common_DeviceInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_zlim_common_DeviceInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_common_DeviceInfo_descriptor,
        new java.lang.String[] { "DeviceId", "DeviceType", "AppVersion", "OsVersion", "PushToken", });
    com.google.protobuf.AnyProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
