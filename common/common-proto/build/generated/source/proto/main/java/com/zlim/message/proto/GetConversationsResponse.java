// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 获取会话列表响应
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.GetConversationsResponse}
 */
public final class GetConversationsResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.GetConversationsResponse)
    GetConversationsResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetConversationsResponse.newBuilder() to construct.
  private GetConversationsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetConversationsResponse() {
    conversations_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetConversationsResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetConversationsResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetConversationsResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.GetConversationsResponse.class, com.zlim.message.proto.GetConversationsResponse.Builder.class);
  }

  private int bitField0_;
  public static final int CONVERSATIONS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.zlim.message.proto.Conversation> conversations_;
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.zlim.message.proto.Conversation> getConversationsList() {
    return conversations_;
  }
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.zlim.message.proto.ConversationOrBuilder> 
      getConversationsOrBuilderList() {
    return conversations_;
  }
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  @java.lang.Override
  public int getConversationsCount() {
    return conversations_.size();
  }
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.Conversation getConversations(int index) {
    return conversations_.get(index);
  }
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.ConversationOrBuilder getConversationsOrBuilder(
      int index) {
    return conversations_.get(index);
  }

  public static final int PAGE_FIELD_NUMBER = 2;
  private com.zlim.common.proto.PageResponse page_;
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   * @return Whether the page field is set.
   */
  @java.lang.Override
  public boolean hasPage() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   * @return The page.
   */
  @java.lang.Override
  public com.zlim.common.proto.PageResponse getPage() {
    return page_ == null ? com.zlim.common.proto.PageResponse.getDefaultInstance() : page_;
  }
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   */
  @java.lang.Override
  public com.zlim.common.proto.PageResponseOrBuilder getPageOrBuilder() {
    return page_ == null ? com.zlim.common.proto.PageResponse.getDefaultInstance() : page_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < conversations_.size(); i++) {
      output.writeMessage(1, conversations_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getPage());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < conversations_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, conversations_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPage());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.GetConversationsResponse)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.GetConversationsResponse other = (com.zlim.message.proto.GetConversationsResponse) obj;

    if (!getConversationsList()
        .equals(other.getConversationsList())) return false;
    if (hasPage() != other.hasPage()) return false;
    if (hasPage()) {
      if (!getPage()
          .equals(other.getPage())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getConversationsCount() > 0) {
      hash = (37 * hash) + CONVERSATIONS_FIELD_NUMBER;
      hash = (53 * hash) + getConversationsList().hashCode();
    }
    if (hasPage()) {
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.GetConversationsResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.GetConversationsResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.GetConversationsResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.GetConversationsResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 获取会话列表响应
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.GetConversationsResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.GetConversationsResponse)
      com.zlim.message.proto.GetConversationsResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetConversationsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetConversationsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.GetConversationsResponse.class, com.zlim.message.proto.GetConversationsResponse.Builder.class);
    }

    // Construct using com.zlim.message.proto.GetConversationsResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getConversationsFieldBuilder();
        getPageFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (conversationsBuilder_ == null) {
        conversations_ = java.util.Collections.emptyList();
      } else {
        conversations_ = null;
        conversationsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      page_ = null;
      if (pageBuilder_ != null) {
        pageBuilder_.dispose();
        pageBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetConversationsResponse_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.GetConversationsResponse getDefaultInstanceForType() {
      return com.zlim.message.proto.GetConversationsResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.GetConversationsResponse build() {
      com.zlim.message.proto.GetConversationsResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.GetConversationsResponse buildPartial() {
      com.zlim.message.proto.GetConversationsResponse result = new com.zlim.message.proto.GetConversationsResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.zlim.message.proto.GetConversationsResponse result) {
      if (conversationsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          conversations_ = java.util.Collections.unmodifiableList(conversations_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.conversations_ = conversations_;
      } else {
        result.conversations_ = conversationsBuilder_.build();
      }
    }

    private void buildPartial0(com.zlim.message.proto.GetConversationsResponse result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.page_ = pageBuilder_ == null
            ? page_
            : pageBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.GetConversationsResponse) {
        return mergeFrom((com.zlim.message.proto.GetConversationsResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.GetConversationsResponse other) {
      if (other == com.zlim.message.proto.GetConversationsResponse.getDefaultInstance()) return this;
      if (conversationsBuilder_ == null) {
        if (!other.conversations_.isEmpty()) {
          if (conversations_.isEmpty()) {
            conversations_ = other.conversations_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureConversationsIsMutable();
            conversations_.addAll(other.conversations_);
          }
          onChanged();
        }
      } else {
        if (!other.conversations_.isEmpty()) {
          if (conversationsBuilder_.isEmpty()) {
            conversationsBuilder_.dispose();
            conversationsBuilder_ = null;
            conversations_ = other.conversations_;
            bitField0_ = (bitField0_ & ~0x00000001);
            conversationsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConversationsFieldBuilder() : null;
          } else {
            conversationsBuilder_.addAllMessages(other.conversations_);
          }
        }
      }
      if (other.hasPage()) {
        mergePage(other.getPage());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.zlim.message.proto.Conversation m =
                  input.readMessage(
                      com.zlim.message.proto.Conversation.parser(),
                      extensionRegistry);
              if (conversationsBuilder_ == null) {
                ensureConversationsIsMutable();
                conversations_.add(m);
              } else {
                conversationsBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getPageFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.zlim.message.proto.Conversation> conversations_ =
      java.util.Collections.emptyList();
    private void ensureConversationsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        conversations_ = new java.util.ArrayList<com.zlim.message.proto.Conversation>(conversations_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.message.proto.Conversation, com.zlim.message.proto.Conversation.Builder, com.zlim.message.proto.ConversationOrBuilder> conversationsBuilder_;

    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public java.util.List<com.zlim.message.proto.Conversation> getConversationsList() {
      if (conversationsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(conversations_);
      } else {
        return conversationsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public int getConversationsCount() {
      if (conversationsBuilder_ == null) {
        return conversations_.size();
      } else {
        return conversationsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public com.zlim.message.proto.Conversation getConversations(int index) {
      if (conversationsBuilder_ == null) {
        return conversations_.get(index);
      } else {
        return conversationsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder setConversations(
        int index, com.zlim.message.proto.Conversation value) {
      if (conversationsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConversationsIsMutable();
        conversations_.set(index, value);
        onChanged();
      } else {
        conversationsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder setConversations(
        int index, com.zlim.message.proto.Conversation.Builder builderForValue) {
      if (conversationsBuilder_ == null) {
        ensureConversationsIsMutable();
        conversations_.set(index, builderForValue.build());
        onChanged();
      } else {
        conversationsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder addConversations(com.zlim.message.proto.Conversation value) {
      if (conversationsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConversationsIsMutable();
        conversations_.add(value);
        onChanged();
      } else {
        conversationsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder addConversations(
        int index, com.zlim.message.proto.Conversation value) {
      if (conversationsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConversationsIsMutable();
        conversations_.add(index, value);
        onChanged();
      } else {
        conversationsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder addConversations(
        com.zlim.message.proto.Conversation.Builder builderForValue) {
      if (conversationsBuilder_ == null) {
        ensureConversationsIsMutable();
        conversations_.add(builderForValue.build());
        onChanged();
      } else {
        conversationsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder addConversations(
        int index, com.zlim.message.proto.Conversation.Builder builderForValue) {
      if (conversationsBuilder_ == null) {
        ensureConversationsIsMutable();
        conversations_.add(index, builderForValue.build());
        onChanged();
      } else {
        conversationsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder addAllConversations(
        java.lang.Iterable<? extends com.zlim.message.proto.Conversation> values) {
      if (conversationsBuilder_ == null) {
        ensureConversationsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, conversations_);
        onChanged();
      } else {
        conversationsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder clearConversations() {
      if (conversationsBuilder_ == null) {
        conversations_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        conversationsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public Builder removeConversations(int index) {
      if (conversationsBuilder_ == null) {
        ensureConversationsIsMutable();
        conversations_.remove(index);
        onChanged();
      } else {
        conversationsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public com.zlim.message.proto.Conversation.Builder getConversationsBuilder(
        int index) {
      return getConversationsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public com.zlim.message.proto.ConversationOrBuilder getConversationsOrBuilder(
        int index) {
      if (conversationsBuilder_ == null) {
        return conversations_.get(index);  } else {
        return conversationsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public java.util.List<? extends com.zlim.message.proto.ConversationOrBuilder> 
         getConversationsOrBuilderList() {
      if (conversationsBuilder_ != null) {
        return conversationsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(conversations_);
      }
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public com.zlim.message.proto.Conversation.Builder addConversationsBuilder() {
      return getConversationsFieldBuilder().addBuilder(
          com.zlim.message.proto.Conversation.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public com.zlim.message.proto.Conversation.Builder addConversationsBuilder(
        int index) {
      return getConversationsFieldBuilder().addBuilder(
          index, com.zlim.message.proto.Conversation.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
     */
    public java.util.List<com.zlim.message.proto.Conversation.Builder> 
         getConversationsBuilderList() {
      return getConversationsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.message.proto.Conversation, com.zlim.message.proto.Conversation.Builder, com.zlim.message.proto.ConversationOrBuilder> 
        getConversationsFieldBuilder() {
      if (conversationsBuilder_ == null) {
        conversationsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.zlim.message.proto.Conversation, com.zlim.message.proto.Conversation.Builder, com.zlim.message.proto.ConversationOrBuilder>(
                conversations_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        conversations_ = null;
      }
      return conversationsBuilder_;
    }

    private com.zlim.common.proto.PageResponse page_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.common.proto.PageResponse, com.zlim.common.proto.PageResponse.Builder, com.zlim.common.proto.PageResponseOrBuilder> pageBuilder_;
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     * @return Whether the page field is set.
     */
    public boolean hasPage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     * @return The page.
     */
    public com.zlim.common.proto.PageResponse getPage() {
      if (pageBuilder_ == null) {
        return page_ == null ? com.zlim.common.proto.PageResponse.getDefaultInstance() : page_;
      } else {
        return pageBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    public Builder setPage(com.zlim.common.proto.PageResponse value) {
      if (pageBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        page_ = value;
      } else {
        pageBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    public Builder setPage(
        com.zlim.common.proto.PageResponse.Builder builderForValue) {
      if (pageBuilder_ == null) {
        page_ = builderForValue.build();
      } else {
        pageBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    public Builder mergePage(com.zlim.common.proto.PageResponse value) {
      if (pageBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          page_ != null &&
          page_ != com.zlim.common.proto.PageResponse.getDefaultInstance()) {
          getPageBuilder().mergeFrom(value);
        } else {
          page_ = value;
        }
      } else {
        pageBuilder_.mergeFrom(value);
      }
      if (page_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    public Builder clearPage() {
      bitField0_ = (bitField0_ & ~0x00000002);
      page_ = null;
      if (pageBuilder_ != null) {
        pageBuilder_.dispose();
        pageBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    public com.zlim.common.proto.PageResponse.Builder getPageBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getPageFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    public com.zlim.common.proto.PageResponseOrBuilder getPageOrBuilder() {
      if (pageBuilder_ != null) {
        return pageBuilder_.getMessageOrBuilder();
      } else {
        return page_ == null ?
            com.zlim.common.proto.PageResponse.getDefaultInstance() : page_;
      }
    }
    /**
     * <code>.com.zlim.common.PageResponse page = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.common.proto.PageResponse, com.zlim.common.proto.PageResponse.Builder, com.zlim.common.proto.PageResponseOrBuilder> 
        getPageFieldBuilder() {
      if (pageBuilder_ == null) {
        pageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.common.proto.PageResponse, com.zlim.common.proto.PageResponse.Builder, com.zlim.common.proto.PageResponseOrBuilder>(
                getPage(),
                getParentForChildren(),
                isClean());
        page_ = null;
      }
      return pageBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.GetConversationsResponse)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.GetConversationsResponse)
  private static final com.zlim.message.proto.GetConversationsResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.GetConversationsResponse();
  }

  public static com.zlim.message.proto.GetConversationsResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetConversationsResponse>
      PARSER = new com.google.protobuf.AbstractParser<GetConversationsResponse>() {
    @java.lang.Override
    public GetConversationsResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GetConversationsResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetConversationsResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.GetConversationsResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

