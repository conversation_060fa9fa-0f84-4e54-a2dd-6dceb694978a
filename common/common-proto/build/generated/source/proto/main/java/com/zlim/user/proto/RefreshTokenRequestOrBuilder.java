// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface RefreshTokenRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.RefreshTokenRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string refresh_token = 1;</code>
   * @return The refreshToken.
   */
  java.lang.String getRefreshToken();
  /**
   * <code>string refresh_token = 1;</code>
   * @return The bytes for refreshToken.
   */
  com.google.protobuf.ByteString
      getRefreshTokenBytes();
}
