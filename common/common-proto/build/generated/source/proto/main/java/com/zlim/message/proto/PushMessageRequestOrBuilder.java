// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface PushMessageRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.PushMessageRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string gateway_id = 1;</code>
   * @return The gatewayId.
   */
  java.lang.String getGatewayId();
  /**
   * <code>string gateway_id = 1;</code>
   * @return The bytes for gatewayId.
   */
  com.google.protobuf.ByteString
      getGatewayIdBytes();

  /**
   * <code>repeated int64 user_ids = 2;</code>
   * @return A list containing the userIds.
   */
  java.util.List<java.lang.Long> getUserIdsList();
  /**
   * <code>repeated int64 user_ids = 2;</code>
   * @return The count of userIds.
   */
  int getUserIdsCount();
  /**
   * <code>repeated int64 user_ids = 2;</code>
   * @param index The index of the element to return.
   * @return The userIds at the given index.
   */
  long getUserIds(int index);

  /**
   * <code>.com.zlim.message.Message message = 3;</code>
   * @return Whether the message field is set.
   */
  boolean hasMessage();
  /**
   * <code>.com.zlim.message.Message message = 3;</code>
   * @return The message.
   */
  com.zlim.message.proto.Message getMessage();
  /**
   * <code>.com.zlim.message.Message message = 3;</code>
   */
  com.zlim.message.proto.MessageOrBuilder getMessageOrBuilder();
}
