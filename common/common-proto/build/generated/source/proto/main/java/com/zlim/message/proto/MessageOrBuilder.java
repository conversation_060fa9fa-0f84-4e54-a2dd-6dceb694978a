// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface MessageOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.Message)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string message_id = 1;</code>
   * @return The messageId.
   */
  java.lang.String getMessageId();
  /**
   * <code>string message_id = 1;</code>
   * @return The bytes for messageId.
   */
  com.google.protobuf.ByteString
      getMessageIdBytes();

  /**
   * <code>string conversation_id = 2;</code>
   * @return The conversationId.
   */
  java.lang.String getConversationId();
  /**
   * <code>string conversation_id = 2;</code>
   * @return The bytes for conversationId.
   */
  com.google.protobuf.ByteString
      getConversationIdBytes();

  /**
   * <code>int64 sender_id = 3;</code>
   * @return The senderId.
   */
  long getSenderId();

  /**
   * <code>.com.zlim.message.MessageType type = 4;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.zlim.message.MessageType type = 4;</code>
   * @return The type.
   */
  com.zlim.message.proto.MessageType getType();

  /**
   * <code>.com.zlim.message.MessageContent content = 5;</code>
   * @return Whether the content field is set.
   */
  boolean hasContent();
  /**
   * <code>.com.zlim.message.MessageContent content = 5;</code>
   * @return The content.
   */
  com.zlim.message.proto.MessageContent getContent();
  /**
   * <code>.com.zlim.message.MessageContent content = 5;</code>
   */
  com.zlim.message.proto.MessageContentOrBuilder getContentOrBuilder();

  /**
   * <code>int64 timestamp = 6;</code>
   * @return The timestamp.
   */
  long getTimestamp();

  /**
   * <code>.com.zlim.message.MessageStatus status = 7;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.zlim.message.MessageStatus status = 7;</code>
   * @return The status.
   */
  com.zlim.message.proto.MessageStatus getStatus();

  /**
   * <code>map&lt;string, string&gt; extra = 8;</code>
   */
  int getExtraCount();
  /**
   * <code>map&lt;string, string&gt; extra = 8;</code>
   */
  boolean containsExtra(
      java.lang.String key);
  /**
   * Use {@link #getExtraMap()} instead.
   */
  @java.lang.Deprecated
  java.util.Map<java.lang.String, java.lang.String>
  getExtra();
  /**
   * <code>map&lt;string, string&gt; extra = 8;</code>
   */
  java.util.Map<java.lang.String, java.lang.String>
  getExtraMap();
  /**
   * <code>map&lt;string, string&gt; extra = 8;</code>
   */
  /* nullable */
java.lang.String getExtraOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue);
  /**
   * <code>map&lt;string, string&gt; extra = 8;</code>
   */
  java.lang.String getExtraOrThrow(
      java.lang.String key);
}
