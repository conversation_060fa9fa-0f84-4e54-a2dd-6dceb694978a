// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface SearchMessageRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.SearchMessageRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>string keyword = 2;</code>
   * @return The keyword.
   */
  java.lang.String getKeyword();
  /**
   * <code>string keyword = 2;</code>
   * @return The bytes for keyword.
   */
  com.google.protobuf.ByteString
      getKeywordBytes();

  /**
   * <pre>
   * 可选，指定会话
   * </pre>
   *
   * <code>string conversation_id = 3;</code>
   * @return The conversationId.
   */
  java.lang.String getConversationId();
  /**
   * <pre>
   * 可选，指定会话
   * </pre>
   *
   * <code>string conversation_id = 3;</code>
   * @return The bytes for conversationId.
   */
  com.google.protobuf.ByteString
      getConversationIdBytes();

  /**
   * <pre>
   * 可选，消息类型
   * </pre>
   *
   * <code>.com.zlim.message.MessageType type = 4;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <pre>
   * 可选，消息类型
   * </pre>
   *
   * <code>.com.zlim.message.MessageType type = 4;</code>
   * @return The type.
   */
  com.zlim.message.proto.MessageType getType();

  /**
   * <code>int64 start_time = 5;</code>
   * @return The startTime.
   */
  long getStartTime();

  /**
   * <code>int64 end_time = 6;</code>
   * @return The endTime.
   */
  long getEndTime();

  /**
   * <code>.com.zlim.common.PageRequest page = 7;</code>
   * @return Whether the page field is set.
   */
  boolean hasPage();
  /**
   * <code>.com.zlim.common.PageRequest page = 7;</code>
   * @return The page.
   */
  com.zlim.common.proto.PageRequest getPage();
  /**
   * <code>.com.zlim.common.PageRequest page = 7;</code>
   */
  com.zlim.common.proto.PageRequestOrBuilder getPageOrBuilder();
}
