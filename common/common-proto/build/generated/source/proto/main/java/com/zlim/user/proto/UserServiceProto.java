// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public final class UserServiceProto {
  private UserServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_RegisterRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_RegisterRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_RegisterResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_RegisterResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_LoginRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_LoginRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_LoginResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_LoginResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_RefreshTokenRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_RefreshTokenRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_RefreshTokenResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_RefreshTokenResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_GetUserInfoRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_GetUserInfoRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_GetUserInfoResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_GetUserInfoResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_UpdateUserInfoRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_UpdateUserInfoRequest_ExtraEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_UpdateUserInfoRequest_ExtraEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_UpdateUserInfoResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_UpdateUserInfoResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_VerifyTokenRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_VerifyTokenRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_VerifyTokenResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_VerifyTokenResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_LogoutRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_LogoutRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_LogoutResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_LogoutResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_BatchGetUserInfoRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_BatchGetUserInfoRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_user_BatchGetUserInfoResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_user_BatchGetUserInfoResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022user_service.proto\022\rcom.zlim.user\032\014com" +
      "mon.proto\"\200\001\n\017RegisterRequest\022\020\n\010usernam" +
      "e\030\001 \001(\t\022\020\n\010password\030\002 \001(\t\022\r\n\005email\030\003 \001(\t" +
      "\022\r\n\005phone\030\004 \001(\t\022\020\n\010nickname\030\005 \001(\t\022\031\n\021ver" +
      "ification_code\030\006 \001(\t\"Y\n\020RegisterResponse" +
      "\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\017\n\007us" +
      "er_id\030\003 \001(\003\022\022\n\nerror_code\030\004 \001(\005\"u\n\014Login" +
      "Request\022\022\n\nidentifier\030\001 \001(\t\022\020\n\010password\030" +
      "\002 \001(\t\022\022\n\nlogin_type\030\003 \001(\t\022+\n\006device\030\004 \001(" +
      "\0132\033.com.zlim.common.DeviceInfo\"\264\001\n\rLogin" +
      "Response\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001" +
      "(\t\022\024\n\014access_token\030\003 \001(\t\022\025\n\rrefresh_toke" +
      "n\030\004 \001(\t\022\022\n\nexpires_in\030\005 \001(\003\022,\n\tuser_info" +
      "\030\006 \001(\0132\031.com.zlim.common.UserInfo\022\022\n\nerr" +
      "or_code\030\007 \001(\005\",\n\023RefreshTokenRequest\022\025\n\r" +
      "refresh_token\030\001 \001(\t\"h\n\024RefreshTokenRespo" +
      "nse\022\017\n\007success\030\001 \001(\010\022\024\n\014access_token\030\002 \001" +
      "(\t\022\025\n\rrefresh_token\030\003 \001(\t\022\022\n\nexpires_in\030" +
      "\004 \001(\003\"%\n\022GetUserInfoRequest\022\017\n\007user_id\030\001" +
      " \001(\003\"C\n\023GetUserInfoResponse\022,\n\tuser_info" +
      "\030\001 \001(\0132\031.com.zlim.common.UserInfo\"\305\001\n\025Up" +
      "dateUserInfoRequest\022\017\n\007user_id\030\001 \001(\003\022\020\n\010" +
      "nickname\030\002 \001(\t\022\016\n\006avatar\030\003 \001(\t\022\013\n\003bio\030\004 " +
      "\001(\t\022>\n\005extra\030\005 \003(\0132/.com.zlim.user.Updat" +
      "eUserInfoRequest.ExtraEntry\032,\n\nExtraEntr" +
      "y\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\":\n\026Upd" +
      "ateUserInfoResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007" +
      "message\030\002 \001(\t\"*\n\022VerifyTokenRequest\022\024\n\014a" +
      "ccess_token\030\001 \001(\t\"[\n\023VerifyTokenResponse" +
      "\022\r\n\005valid\030\001 \001(\010\022\017\n\007user_id\030\002 \001(\003\022\020\n\010user" +
      "name\030\003 \001(\t\022\022\n\nexpires_at\030\004 \001(\003\"6\n\rLogout" +
      "Request\022\017\n\007user_id\030\001 \001(\003\022\024\n\014access_token" +
      "\030\002 \001(\t\"F\n\016LogoutResponse\022\017\n\007success\030\001 \001(" +
      "\010\022\017\n\007message\030\002 \001(\t\022\022\n\nerror_code\030\003 \001(\005\"+" +
      "\n\027BatchGetUserInfoRequest\022\020\n\010user_ids\030\001 " +
      "\003(\003\"I\n\030BatchGetUserInfoResponse\022-\n\nuser_" +
      "infos\030\001 \003(\0132\031.com.zlim.common.UserInfo2\256" +
      "\005\n\013UserService\022K\n\010Register\022\036.com.zlim.us" +
      "er.RegisterRequest\032\037.com.zlim.user.Regis" +
      "terResponse\022B\n\005Login\022\033.com.zlim.user.Log" +
      "inRequest\032\034.com.zlim.user.LoginResponse\022" +
      "W\n\014RefreshToken\022\".com.zlim.user.RefreshT" +
      "okenRequest\032#.com.zlim.user.RefreshToken" +
      "Response\022T\n\013GetUserInfo\022!.com.zlim.user." +
      "GetUserInfoRequest\032\".com.zlim.user.GetUs" +
      "erInfoResponse\022]\n\016UpdateUserInfo\022$.com.z" +
      "lim.user.UpdateUserInfoRequest\032%.com.zli" +
      "m.user.UpdateUserInfoResponse\022T\n\013VerifyT" +
      "oken\022!.com.zlim.user.VerifyTokenRequest\032" +
      "\".com.zlim.user.VerifyTokenResponse\022E\n\006L" +
      "ogout\022\034.com.zlim.user.LogoutRequest\032\035.co" +
      "m.zlim.user.LogoutResponse\022c\n\020BatchGetUs" +
      "erInfo\022&.com.zlim.user.BatchGetUserInfoR" +
      "equest\032\'.com.zlim.user.BatchGetUserInfoR" +
      "esponseB)\n\023com.zlim.user.protoB\020UserServ" +
      "iceProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.zlim.common.proto.CommonProto.getDescriptor(),
        });
    internal_static_com_zlim_user_RegisterRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_zlim_user_RegisterRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_RegisterRequest_descriptor,
        new java.lang.String[] { "Username", "Password", "Email", "Phone", "Nickname", "VerificationCode", });
    internal_static_com_zlim_user_RegisterResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_zlim_user_RegisterResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_RegisterResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "UserId", "ErrorCode", });
    internal_static_com_zlim_user_LoginRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_zlim_user_LoginRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_LoginRequest_descriptor,
        new java.lang.String[] { "Identifier", "Password", "LoginType", "Device", });
    internal_static_com_zlim_user_LoginResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_zlim_user_LoginResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_LoginResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "AccessToken", "RefreshToken", "ExpiresIn", "UserInfo", "ErrorCode", });
    internal_static_com_zlim_user_RefreshTokenRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_zlim_user_RefreshTokenRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_RefreshTokenRequest_descriptor,
        new java.lang.String[] { "RefreshToken", });
    internal_static_com_zlim_user_RefreshTokenResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_zlim_user_RefreshTokenResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_RefreshTokenResponse_descriptor,
        new java.lang.String[] { "Success", "AccessToken", "RefreshToken", "ExpiresIn", });
    internal_static_com_zlim_user_GetUserInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_zlim_user_GetUserInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_GetUserInfoRequest_descriptor,
        new java.lang.String[] { "UserId", });
    internal_static_com_zlim_user_GetUserInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_zlim_user_GetUserInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_GetUserInfoResponse_descriptor,
        new java.lang.String[] { "UserInfo", });
    internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_zlim_user_UpdateUserInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor,
        new java.lang.String[] { "UserId", "Nickname", "Avatar", "Bio", "Extra", });
    internal_static_com_zlim_user_UpdateUserInfoRequest_ExtraEntry_descriptor =
      internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor.getNestedTypes().get(0);
    internal_static_com_zlim_user_UpdateUserInfoRequest_ExtraEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_UpdateUserInfoRequest_ExtraEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_zlim_user_UpdateUserInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_zlim_user_UpdateUserInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_UpdateUserInfoResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_zlim_user_VerifyTokenRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_zlim_user_VerifyTokenRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_VerifyTokenRequest_descriptor,
        new java.lang.String[] { "AccessToken", });
    internal_static_com_zlim_user_VerifyTokenResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_zlim_user_VerifyTokenResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_VerifyTokenResponse_descriptor,
        new java.lang.String[] { "Valid", "UserId", "Username", "ExpiresAt", });
    internal_static_com_zlim_user_LogoutRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_zlim_user_LogoutRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_LogoutRequest_descriptor,
        new java.lang.String[] { "UserId", "AccessToken", });
    internal_static_com_zlim_user_LogoutResponse_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_zlim_user_LogoutResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_LogoutResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "ErrorCode", });
    internal_static_com_zlim_user_BatchGetUserInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_zlim_user_BatchGetUserInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_BatchGetUserInfoRequest_descriptor,
        new java.lang.String[] { "UserIds", });
    internal_static_com_zlim_user_BatchGetUserInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_zlim_user_BatchGetUserInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_user_BatchGetUserInfoResponse_descriptor,
        new java.lang.String[] { "UserInfos", });
    com.zlim.common.proto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
