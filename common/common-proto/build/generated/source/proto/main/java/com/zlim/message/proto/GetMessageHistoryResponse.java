// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 获取消息历史响应
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.GetMessageHistoryResponse}
 */
public final class GetMessageHistoryResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.GetMessageHistoryResponse)
    GetMessageHistoryResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetMessageHistoryResponse.newBuilder() to construct.
  private GetMessageHistoryResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetMessageHistoryResponse() {
    messages_ = java.util.Collections.emptyList();
    nextCursor_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetMessageHistoryResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetMessageHistoryResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetMessageHistoryResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.GetMessageHistoryResponse.class, com.zlim.message.proto.GetMessageHistoryResponse.Builder.class);
  }

  public static final int MESSAGES_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.zlim.message.proto.Message> messages_;
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.zlim.message.proto.Message> getMessagesList() {
    return messages_;
  }
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.zlim.message.proto.MessageOrBuilder> 
      getMessagesOrBuilderList() {
    return messages_;
  }
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  @java.lang.Override
  public int getMessagesCount() {
    return messages_.size();
  }
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.Message getMessages(int index) {
    return messages_.get(index);
  }
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.MessageOrBuilder getMessagesOrBuilder(
      int index) {
    return messages_.get(index);
  }

  public static final int NEXT_CURSOR_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nextCursor_ = "";
  /**
   * <code>string next_cursor = 2;</code>
   * @return The nextCursor.
   */
  @java.lang.Override
  public java.lang.String getNextCursor() {
    java.lang.Object ref = nextCursor_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      nextCursor_ = s;
      return s;
    }
  }
  /**
   * <code>string next_cursor = 2;</code>
   * @return The bytes for nextCursor.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNextCursorBytes() {
    java.lang.Object ref = nextCursor_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nextCursor_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HAS_MORE_FIELD_NUMBER = 3;
  private boolean hasMore_ = false;
  /**
   * <code>bool has_more = 3;</code>
   * @return The hasMore.
   */
  @java.lang.Override
  public boolean getHasMore() {
    return hasMore_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < messages_.size(); i++) {
      output.writeMessage(1, messages_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nextCursor_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, nextCursor_);
    }
    if (hasMore_ != false) {
      output.writeBool(3, hasMore_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < messages_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, messages_.get(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nextCursor_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, nextCursor_);
    }
    if (hasMore_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, hasMore_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.GetMessageHistoryResponse)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.GetMessageHistoryResponse other = (com.zlim.message.proto.GetMessageHistoryResponse) obj;

    if (!getMessagesList()
        .equals(other.getMessagesList())) return false;
    if (!getNextCursor()
        .equals(other.getNextCursor())) return false;
    if (getHasMore()
        != other.getHasMore()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getMessagesCount() > 0) {
      hash = (37 * hash) + MESSAGES_FIELD_NUMBER;
      hash = (53 * hash) + getMessagesList().hashCode();
    }
    hash = (37 * hash) + NEXT_CURSOR_FIELD_NUMBER;
    hash = (53 * hash) + getNextCursor().hashCode();
    hash = (37 * hash) + HAS_MORE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHasMore());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.GetMessageHistoryResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.GetMessageHistoryResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.GetMessageHistoryResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.GetMessageHistoryResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 获取消息历史响应
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.GetMessageHistoryResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.GetMessageHistoryResponse)
      com.zlim.message.proto.GetMessageHistoryResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetMessageHistoryResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetMessageHistoryResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.GetMessageHistoryResponse.class, com.zlim.message.proto.GetMessageHistoryResponse.Builder.class);
    }

    // Construct using com.zlim.message.proto.GetMessageHistoryResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (messagesBuilder_ == null) {
        messages_ = java.util.Collections.emptyList();
      } else {
        messages_ = null;
        messagesBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      nextCursor_ = "";
      hasMore_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_GetMessageHistoryResponse_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.GetMessageHistoryResponse getDefaultInstanceForType() {
      return com.zlim.message.proto.GetMessageHistoryResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.GetMessageHistoryResponse build() {
      com.zlim.message.proto.GetMessageHistoryResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.GetMessageHistoryResponse buildPartial() {
      com.zlim.message.proto.GetMessageHistoryResponse result = new com.zlim.message.proto.GetMessageHistoryResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.zlim.message.proto.GetMessageHistoryResponse result) {
      if (messagesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          messages_ = java.util.Collections.unmodifiableList(messages_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.messages_ = messages_;
      } else {
        result.messages_ = messagesBuilder_.build();
      }
    }

    private void buildPartial0(com.zlim.message.proto.GetMessageHistoryResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.nextCursor_ = nextCursor_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.hasMore_ = hasMore_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.GetMessageHistoryResponse) {
        return mergeFrom((com.zlim.message.proto.GetMessageHistoryResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.GetMessageHistoryResponse other) {
      if (other == com.zlim.message.proto.GetMessageHistoryResponse.getDefaultInstance()) return this;
      if (messagesBuilder_ == null) {
        if (!other.messages_.isEmpty()) {
          if (messages_.isEmpty()) {
            messages_ = other.messages_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureMessagesIsMutable();
            messages_.addAll(other.messages_);
          }
          onChanged();
        }
      } else {
        if (!other.messages_.isEmpty()) {
          if (messagesBuilder_.isEmpty()) {
            messagesBuilder_.dispose();
            messagesBuilder_ = null;
            messages_ = other.messages_;
            bitField0_ = (bitField0_ & ~0x00000001);
            messagesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMessagesFieldBuilder() : null;
          } else {
            messagesBuilder_.addAllMessages(other.messages_);
          }
        }
      }
      if (!other.getNextCursor().isEmpty()) {
        nextCursor_ = other.nextCursor_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.getHasMore() != false) {
        setHasMore(other.getHasMore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.zlim.message.proto.Message m =
                  input.readMessage(
                      com.zlim.message.proto.Message.parser(),
                      extensionRegistry);
              if (messagesBuilder_ == null) {
                ensureMessagesIsMutable();
                messages_.add(m);
              } else {
                messagesBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              nextCursor_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              hasMore_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.zlim.message.proto.Message> messages_ =
      java.util.Collections.emptyList();
    private void ensureMessagesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        messages_ = new java.util.ArrayList<com.zlim.message.proto.Message>(messages_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.message.proto.Message, com.zlim.message.proto.Message.Builder, com.zlim.message.proto.MessageOrBuilder> messagesBuilder_;

    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public java.util.List<com.zlim.message.proto.Message> getMessagesList() {
      if (messagesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(messages_);
      } else {
        return messagesBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public int getMessagesCount() {
      if (messagesBuilder_ == null) {
        return messages_.size();
      } else {
        return messagesBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public com.zlim.message.proto.Message getMessages(int index) {
      if (messagesBuilder_ == null) {
        return messages_.get(index);
      } else {
        return messagesBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder setMessages(
        int index, com.zlim.message.proto.Message value) {
      if (messagesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMessagesIsMutable();
        messages_.set(index, value);
        onChanged();
      } else {
        messagesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder setMessages(
        int index, com.zlim.message.proto.Message.Builder builderForValue) {
      if (messagesBuilder_ == null) {
        ensureMessagesIsMutable();
        messages_.set(index, builderForValue.build());
        onChanged();
      } else {
        messagesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder addMessages(com.zlim.message.proto.Message value) {
      if (messagesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMessagesIsMutable();
        messages_.add(value);
        onChanged();
      } else {
        messagesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder addMessages(
        int index, com.zlim.message.proto.Message value) {
      if (messagesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMessagesIsMutable();
        messages_.add(index, value);
        onChanged();
      } else {
        messagesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder addMessages(
        com.zlim.message.proto.Message.Builder builderForValue) {
      if (messagesBuilder_ == null) {
        ensureMessagesIsMutable();
        messages_.add(builderForValue.build());
        onChanged();
      } else {
        messagesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder addMessages(
        int index, com.zlim.message.proto.Message.Builder builderForValue) {
      if (messagesBuilder_ == null) {
        ensureMessagesIsMutable();
        messages_.add(index, builderForValue.build());
        onChanged();
      } else {
        messagesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder addAllMessages(
        java.lang.Iterable<? extends com.zlim.message.proto.Message> values) {
      if (messagesBuilder_ == null) {
        ensureMessagesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, messages_);
        onChanged();
      } else {
        messagesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder clearMessages() {
      if (messagesBuilder_ == null) {
        messages_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        messagesBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public Builder removeMessages(int index) {
      if (messagesBuilder_ == null) {
        ensureMessagesIsMutable();
        messages_.remove(index);
        onChanged();
      } else {
        messagesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public com.zlim.message.proto.Message.Builder getMessagesBuilder(
        int index) {
      return getMessagesFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public com.zlim.message.proto.MessageOrBuilder getMessagesOrBuilder(
        int index) {
      if (messagesBuilder_ == null) {
        return messages_.get(index);  } else {
        return messagesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public java.util.List<? extends com.zlim.message.proto.MessageOrBuilder> 
         getMessagesOrBuilderList() {
      if (messagesBuilder_ != null) {
        return messagesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(messages_);
      }
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public com.zlim.message.proto.Message.Builder addMessagesBuilder() {
      return getMessagesFieldBuilder().addBuilder(
          com.zlim.message.proto.Message.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public com.zlim.message.proto.Message.Builder addMessagesBuilder(
        int index) {
      return getMessagesFieldBuilder().addBuilder(
          index, com.zlim.message.proto.Message.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.message.Message messages = 1;</code>
     */
    public java.util.List<com.zlim.message.proto.Message.Builder> 
         getMessagesBuilderList() {
      return getMessagesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.message.proto.Message, com.zlim.message.proto.Message.Builder, com.zlim.message.proto.MessageOrBuilder> 
        getMessagesFieldBuilder() {
      if (messagesBuilder_ == null) {
        messagesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.zlim.message.proto.Message, com.zlim.message.proto.Message.Builder, com.zlim.message.proto.MessageOrBuilder>(
                messages_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        messages_ = null;
      }
      return messagesBuilder_;
    }

    private java.lang.Object nextCursor_ = "";
    /**
     * <code>string next_cursor = 2;</code>
     * @return The nextCursor.
     */
    public java.lang.String getNextCursor() {
      java.lang.Object ref = nextCursor_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nextCursor_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string next_cursor = 2;</code>
     * @return The bytes for nextCursor.
     */
    public com.google.protobuf.ByteString
        getNextCursorBytes() {
      java.lang.Object ref = nextCursor_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nextCursor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string next_cursor = 2;</code>
     * @param value The nextCursor to set.
     * @return This builder for chaining.
     */
    public Builder setNextCursor(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nextCursor_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string next_cursor = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearNextCursor() {
      nextCursor_ = getDefaultInstance().getNextCursor();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string next_cursor = 2;</code>
     * @param value The bytes for nextCursor to set.
     * @return This builder for chaining.
     */
    public Builder setNextCursorBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      nextCursor_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private boolean hasMore_ ;
    /**
     * <code>bool has_more = 3;</code>
     * @return The hasMore.
     */
    @java.lang.Override
    public boolean getHasMore() {
      return hasMore_;
    }
    /**
     * <code>bool has_more = 3;</code>
     * @param value The hasMore to set.
     * @return This builder for chaining.
     */
    public Builder setHasMore(boolean value) {

      hasMore_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>bool has_more = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasMore() {
      bitField0_ = (bitField0_ & ~0x00000004);
      hasMore_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.GetMessageHistoryResponse)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.GetMessageHistoryResponse)
  private static final com.zlim.message.proto.GetMessageHistoryResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.GetMessageHistoryResponse();
  }

  public static com.zlim.message.proto.GetMessageHistoryResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetMessageHistoryResponse>
      PARSER = new com.google.protobuf.AbstractParser<GetMessageHistoryResponse>() {
    @java.lang.Override
    public GetMessageHistoryResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GetMessageHistoryResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetMessageHistoryResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.GetMessageHistoryResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

