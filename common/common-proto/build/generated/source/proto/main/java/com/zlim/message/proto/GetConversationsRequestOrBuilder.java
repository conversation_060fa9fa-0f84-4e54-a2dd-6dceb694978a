// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface GetConversationsRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.GetConversationsRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>.com.zlim.common.PageRequest page = 2;</code>
   * @return Whether the page field is set.
   */
  boolean hasPage();
  /**
   * <code>.com.zlim.common.PageRequest page = 2;</code>
   * @return The page.
   */
  com.zlim.common.proto.PageRequest getPage();
  /**
   * <code>.com.zlim.common.PageRequest page = 2;</code>
   */
  com.zlim.common.proto.PageRequestOrBuilder getPageOrBuilder();
}
