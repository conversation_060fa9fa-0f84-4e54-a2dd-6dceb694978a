// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

/**
 * <pre>
 * 更新用户信息请求
 * </pre>
 *
 * Protobuf type {@code com.zlim.user.UpdateUserInfoRequest}
 */
public final class UpdateUserInfoRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.user.UpdateUserInfoRequest)
    UpdateUserInfoRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use UpdateUserInfoRequest.newBuilder() to construct.
  private UpdateUserInfoRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private UpdateUserInfoRequest() {
    nickname_ = "";
    avatar_ = "";
    bio_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new UpdateUserInfoRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor;
  }

  @SuppressWarnings({"rawtypes"})
  @java.lang.Override
  protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
      int number) {
    switch (number) {
      case 5:
        return internalGetExtra();
      default:
        throw new RuntimeException(
            "Invalid map field number: " + number);
    }
  }
  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_UpdateUserInfoRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.user.proto.UpdateUserInfoRequest.class, com.zlim.user.proto.UpdateUserInfoRequest.Builder.class);
  }

  public static final int USER_ID_FIELD_NUMBER = 1;
  private long userId_ = 0L;
  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  @java.lang.Override
  public long getUserId() {
    return userId_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickname_ = "";
  /**
   * <code>string nickname = 2;</code>
   * @return The nickname.
   */
  @java.lang.Override
  public java.lang.String getNickname() {
    java.lang.Object ref = nickname_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      nickname_ = s;
      return s;
    }
  }
  /**
   * <code>string nickname = 2;</code>
   * @return The bytes for nickname.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNicknameBytes() {
    java.lang.Object ref = nickname_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickname_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AVATAR_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object avatar_ = "";
  /**
   * <code>string avatar = 3;</code>
   * @return The avatar.
   */
  @java.lang.Override
  public java.lang.String getAvatar() {
    java.lang.Object ref = avatar_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      avatar_ = s;
      return s;
    }
  }
  /**
   * <code>string avatar = 3;</code>
   * @return The bytes for avatar.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAvatarBytes() {
    java.lang.Object ref = avatar_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      avatar_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BIO_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bio_ = "";
  /**
   * <code>string bio = 4;</code>
   * @return The bio.
   */
  @java.lang.Override
  public java.lang.String getBio() {
    java.lang.Object ref = bio_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      bio_ = s;
      return s;
    }
  }
  /**
   * <code>string bio = 4;</code>
   * @return The bytes for bio.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBioBytes() {
    java.lang.Object ref = bio_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bio_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EXTRA_FIELD_NUMBER = 5;
  private static final class ExtraDefaultEntryHolder {
    static final com.google.protobuf.MapEntry<
        java.lang.String, java.lang.String> defaultEntry =
            com.google.protobuf.MapEntry
            .<java.lang.String, java.lang.String>newDefaultInstance(
                com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_UpdateUserInfoRequest_ExtraEntry_descriptor, 
                com.google.protobuf.WireFormat.FieldType.STRING,
                "",
                com.google.protobuf.WireFormat.FieldType.STRING,
                "");
  }
  @SuppressWarnings("serial")
  private com.google.protobuf.MapField<
      java.lang.String, java.lang.String> extra_;
  private com.google.protobuf.MapField<java.lang.String, java.lang.String>
  internalGetExtra() {
    if (extra_ == null) {
      return com.google.protobuf.MapField.emptyMapField(
          ExtraDefaultEntryHolder.defaultEntry);
    }
    return extra_;
  }
  public int getExtraCount() {
    return internalGetExtra().getMap().size();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public boolean containsExtra(
      java.lang.String key) {
    if (key == null) { throw new NullPointerException("map key"); }
    return internalGetExtra().getMap().containsKey(key);
  }
  /**
   * Use {@link #getExtraMap()} instead.
   */
  @java.lang.Override
  @java.lang.Deprecated
  public java.util.Map<java.lang.String, java.lang.String> getExtra() {
    return getExtraMap();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public java.util.Map<java.lang.String, java.lang.String> getExtraMap() {
    return internalGetExtra().getMap();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public /* nullable */
java.lang.String getExtraOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue) {
    if (key == null) { throw new NullPointerException("map key"); }
    java.util.Map<java.lang.String, java.lang.String> map =
        internalGetExtra().getMap();
    return map.containsKey(key) ? map.get(key) : defaultValue;
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public java.lang.String getExtraOrThrow(
      java.lang.String key) {
    if (key == null) { throw new NullPointerException("map key"); }
    java.util.Map<java.lang.String, java.lang.String> map =
        internalGetExtra().getMap();
    if (!map.containsKey(key)) {
      throw new java.lang.IllegalArgumentException();
    }
    return map.get(key);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (userId_ != 0L) {
      output.writeInt64(1, userId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nickname_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, nickname_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(avatar_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, avatar_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(bio_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, bio_);
    }
    com.google.protobuf.GeneratedMessageV3
      .serializeStringMapTo(
        output,
        internalGetExtra(),
        ExtraDefaultEntryHolder.defaultEntry,
        5);
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (userId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, userId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nickname_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, nickname_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(avatar_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, avatar_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(bio_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, bio_);
    }
    for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
         : internalGetExtra().getMap().entrySet()) {
      com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
      extra__ = ExtraDefaultEntryHolder.defaultEntry.newBuilderForType()
          .setKey(entry.getKey())
          .setValue(entry.getValue())
          .build();
      size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, extra__);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.user.proto.UpdateUserInfoRequest)) {
      return super.equals(obj);
    }
    com.zlim.user.proto.UpdateUserInfoRequest other = (com.zlim.user.proto.UpdateUserInfoRequest) obj;

    if (getUserId()
        != other.getUserId()) return false;
    if (!getNickname()
        .equals(other.getNickname())) return false;
    if (!getAvatar()
        .equals(other.getAvatar())) return false;
    if (!getBio()
        .equals(other.getBio())) return false;
    if (!internalGetExtra().equals(
        other.internalGetExtra())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + USER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUserId());
    hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
    hash = (53 * hash) + getNickname().hashCode();
    hash = (37 * hash) + AVATAR_FIELD_NUMBER;
    hash = (53 * hash) + getAvatar().hashCode();
    hash = (37 * hash) + BIO_FIELD_NUMBER;
    hash = (53 * hash) + getBio().hashCode();
    if (!internalGetExtra().getMap().isEmpty()) {
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + internalGetExtra().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.user.proto.UpdateUserInfoRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.user.proto.UpdateUserInfoRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.user.proto.UpdateUserInfoRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.user.proto.UpdateUserInfoRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 更新用户信息请求
   * </pre>
   *
   * Protobuf type {@code com.zlim.user.UpdateUserInfoRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.user.UpdateUserInfoRequest)
      com.zlim.user.proto.UpdateUserInfoRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
        int number) {
      switch (number) {
        case 5:
          return internalGetExtra();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
        int number) {
      switch (number) {
        case 5:
          return internalGetMutableExtra();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_UpdateUserInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.user.proto.UpdateUserInfoRequest.class, com.zlim.user.proto.UpdateUserInfoRequest.Builder.class);
    }

    // Construct using com.zlim.user.proto.UpdateUserInfoRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      userId_ = 0L;
      nickname_ = "";
      avatar_ = "";
      bio_ = "";
      internalGetMutableExtra().clear();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.user.proto.UserServiceProto.internal_static_com_zlim_user_UpdateUserInfoRequest_descriptor;
    }

    @java.lang.Override
    public com.zlim.user.proto.UpdateUserInfoRequest getDefaultInstanceForType() {
      return com.zlim.user.proto.UpdateUserInfoRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.user.proto.UpdateUserInfoRequest build() {
      com.zlim.user.proto.UpdateUserInfoRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.user.proto.UpdateUserInfoRequest buildPartial() {
      com.zlim.user.proto.UpdateUserInfoRequest result = new com.zlim.user.proto.UpdateUserInfoRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.user.proto.UpdateUserInfoRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.userId_ = userId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.nickname_ = nickname_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.avatar_ = avatar_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.bio_ = bio_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.extra_ = internalGetExtra();
        result.extra_.makeImmutable();
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.user.proto.UpdateUserInfoRequest) {
        return mergeFrom((com.zlim.user.proto.UpdateUserInfoRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.user.proto.UpdateUserInfoRequest other) {
      if (other == com.zlim.user.proto.UpdateUserInfoRequest.getDefaultInstance()) return this;
      if (other.getUserId() != 0L) {
        setUserId(other.getUserId());
      }
      if (!other.getNickname().isEmpty()) {
        nickname_ = other.nickname_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getAvatar().isEmpty()) {
        avatar_ = other.avatar_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getBio().isEmpty()) {
        bio_ = other.bio_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      internalGetMutableExtra().mergeFrom(
          other.internalGetExtra());
      bitField0_ |= 0x00000010;
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              userId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              nickname_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              avatar_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              bio_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extra__ = input.readMessage(
                  ExtraDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              internalGetMutableExtra().getMutableMap().put(
                  extra__.getKey(), extra__.getValue());
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long userId_ ;
    /**
     * <code>int64 user_id = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }
    /**
     * <code>int64 user_id = 1;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(long value) {

      userId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int64 user_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      userId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object nickname_ = "";
    /**
     * <code>string nickname = 2;</code>
     * @return The nickname.
     */
    public java.lang.String getNickname() {
      java.lang.Object ref = nickname_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickname_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string nickname = 2;</code>
     * @return The bytes for nickname.
     */
    public com.google.protobuf.ByteString
        getNicknameBytes() {
      java.lang.Object ref = nickname_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string nickname = 2;</code>
     * @param value The nickname to set.
     * @return This builder for chaining.
     */
    public Builder setNickname(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickname_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string nickname = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickname() {
      nickname_ = getDefaultInstance().getNickname();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string nickname = 2;</code>
     * @param value The bytes for nickname to set.
     * @return This builder for chaining.
     */
    public Builder setNicknameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      nickname_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object avatar_ = "";
    /**
     * <code>string avatar = 3;</code>
     * @return The avatar.
     */
    public java.lang.String getAvatar() {
      java.lang.Object ref = avatar_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        avatar_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string avatar = 3;</code>
     * @return The bytes for avatar.
     */
    public com.google.protobuf.ByteString
        getAvatarBytes() {
      java.lang.Object ref = avatar_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        avatar_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string avatar = 3;</code>
     * @param value The avatar to set.
     * @return This builder for chaining.
     */
    public Builder setAvatar(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      avatar_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string avatar = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAvatar() {
      avatar_ = getDefaultInstance().getAvatar();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string avatar = 3;</code>
     * @param value The bytes for avatar to set.
     * @return This builder for chaining.
     */
    public Builder setAvatarBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      avatar_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object bio_ = "";
    /**
     * <code>string bio = 4;</code>
     * @return The bio.
     */
    public java.lang.String getBio() {
      java.lang.Object ref = bio_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bio_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string bio = 4;</code>
     * @return The bytes for bio.
     */
    public com.google.protobuf.ByteString
        getBioBytes() {
      java.lang.Object ref = bio_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bio_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string bio = 4;</code>
     * @param value The bio to set.
     * @return This builder for chaining.
     */
    public Builder setBio(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bio_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string bio = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearBio() {
      bio_ = getDefaultInstance().getBio();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string bio = 4;</code>
     * @param value The bytes for bio to set.
     * @return This builder for chaining.
     */
    public Builder setBioBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      bio_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extra_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetExtra() {
      if (extra_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtraDefaultEntryHolder.defaultEntry);
      }
      return extra_;
    }
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetMutableExtra() {
      if (extra_ == null) {
        extra_ = com.google.protobuf.MapField.newMapField(
            ExtraDefaultEntryHolder.defaultEntry);
      }
      if (!extra_.isMutable()) {
        extra_ = extra_.copy();
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return extra_;
    }
    public int getExtraCount() {
      return internalGetExtra().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public boolean containsExtra(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetExtra().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtraMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtra() {
      return getExtraMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public java.util.Map<java.lang.String, java.lang.String> getExtraMap() {
      return internalGetExtra().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public /* nullable */
java.lang.String getExtraOrDefault(
        java.lang.String key,
        /* nullable */
java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtra().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public java.lang.String getExtraOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtra().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }
    public Builder clearExtra() {
      bitField0_ = (bitField0_ & ~0x00000010);
      internalGetMutableExtra().getMutableMap()
          .clear();
      return this;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    public Builder removeExtra(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      internalGetMutableExtra().getMutableMap()
          .remove(key);
      return this;
    }
    /**
     * Use alternate mutation accessors instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String>
        getMutableExtra() {
      bitField0_ |= 0x00000010;
      return internalGetMutableExtra().getMutableMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    public Builder putExtra(
        java.lang.String key,
        java.lang.String value) {
      if (key == null) { throw new NullPointerException("map key"); }
      if (value == null) { throw new NullPointerException("map value"); }
      internalGetMutableExtra().getMutableMap()
          .put(key, value);
      bitField0_ |= 0x00000010;
      return this;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    public Builder putAllExtra(
        java.util.Map<java.lang.String, java.lang.String> values) {
      internalGetMutableExtra().getMutableMap()
          .putAll(values);
      bitField0_ |= 0x00000010;
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.user.UpdateUserInfoRequest)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.user.UpdateUserInfoRequest)
  private static final com.zlim.user.proto.UpdateUserInfoRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.user.proto.UpdateUserInfoRequest();
  }

  public static com.zlim.user.proto.UpdateUserInfoRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UpdateUserInfoRequest>
      PARSER = new com.google.protobuf.AbstractParser<UpdateUserInfoRequest>() {
    @java.lang.Override
    public UpdateUserInfoRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UpdateUserInfoRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UpdateUserInfoRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.user.proto.UpdateUserInfoRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

