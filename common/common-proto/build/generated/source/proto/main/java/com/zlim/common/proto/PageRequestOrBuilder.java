// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

public interface PageRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.common.PageRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 page = 1;</code>
   * @return The page.
   */
  int getPage();

  /**
   * <code>int32 size = 2;</code>
   * @return The size.
   */
  int getSize();

  /**
   * <code>string sort = 3;</code>
   * @return The sort.
   */
  java.lang.String getSort();
  /**
   * <code>string sort = 3;</code>
   * @return The bytes for sort.
   */
  com.google.protobuf.ByteString
      getSortBytes();

  /**
   * <pre>
   * ASC, DESC
   * </pre>
   *
   * <code>string order = 4;</code>
   * @return The order.
   */
  java.lang.String getOrder();
  /**
   * <pre>
   * ASC, DESC
   * </pre>
   *
   * <code>string order = 4;</code>
   * @return The bytes for order.
   */
  com.google.protobuf.ByteString
      getOrderBytes();
}
