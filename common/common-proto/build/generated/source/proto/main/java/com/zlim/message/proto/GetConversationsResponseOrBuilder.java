// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface GetConversationsResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.GetConversationsResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  java.util.List<com.zlim.message.proto.Conversation> 
      getConversationsList();
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  com.zlim.message.proto.Conversation getConversations(int index);
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  int getConversationsCount();
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  java.util.List<? extends com.zlim.message.proto.ConversationOrBuilder> 
      getConversationsOrBuilderList();
  /**
   * <code>repeated .com.zlim.message.Conversation conversations = 1;</code>
   */
  com.zlim.message.proto.ConversationOrBuilder getConversationsOrBuilder(
      int index);

  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   * @return Whether the page field is set.
   */
  boolean hasPage();
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   * @return The page.
   */
  com.zlim.common.proto.PageResponse getPage();
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   */
  com.zlim.common.proto.PageResponseOrBuilder getPageOrBuilder();
}
