// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface SystemContentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.SystemContent)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string type = 1;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <code>string type = 1;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <code>string content = 2;</code>
   * @return The content.
   */
  java.lang.String getContent();
  /**
   * <code>string content = 2;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();

  /**
   * <code>map&lt;string, string&gt; params = 3;</code>
   */
  int getParamsCount();
  /**
   * <code>map&lt;string, string&gt; params = 3;</code>
   */
  boolean containsParams(
      java.lang.String key);
  /**
   * Use {@link #getParamsMap()} instead.
   */
  @java.lang.Deprecated
  java.util.Map<java.lang.String, java.lang.String>
  getParams();
  /**
   * <code>map&lt;string, string&gt; params = 3;</code>
   */
  java.util.Map<java.lang.String, java.lang.String>
  getParamsMap();
  /**
   * <code>map&lt;string, string&gt; params = 3;</code>
   */
  /* nullable */
java.lang.String getParamsOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue);
  /**
   * <code>map&lt;string, string&gt; params = 3;</code>
   */
  java.lang.String getParamsOrThrow(
      java.lang.String key);
}
