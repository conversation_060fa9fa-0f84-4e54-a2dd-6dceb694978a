// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface LoginRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.LoginRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 用户名、邮箱或手机号
   * </pre>
   *
   * <code>string identifier = 1;</code>
   * @return The identifier.
   */
  java.lang.String getIdentifier();
  /**
   * <pre>
   * 用户名、邮箱或手机号
   * </pre>
   *
   * <code>string identifier = 1;</code>
   * @return The bytes for identifier.
   */
  com.google.protobuf.ByteString
      getIdentifierBytes();

  /**
   * <code>string password = 2;</code>
   * @return The password.
   */
  java.lang.String getPassword();
  /**
   * <code>string password = 2;</code>
   * @return The bytes for password.
   */
  com.google.protobuf.ByteString
      getPasswordBytes();

  /**
   * <pre>
   * password, sms, oauth
   * </pre>
   *
   * <code>string login_type = 3;</code>
   * @return The loginType.
   */
  java.lang.String getLoginType();
  /**
   * <pre>
   * password, sms, oauth
   * </pre>
   *
   * <code>string login_type = 3;</code>
   * @return The bytes for loginType.
   */
  com.google.protobuf.ByteString
      getLoginTypeBytes();

  /**
   * <code>.com.zlim.common.DeviceInfo device = 4;</code>
   * @return Whether the device field is set.
   */
  boolean hasDevice();
  /**
   * <code>.com.zlim.common.DeviceInfo device = 4;</code>
   * @return The device.
   */
  com.zlim.common.proto.DeviceInfo getDevice();
  /**
   * <code>.com.zlim.common.DeviceInfo device = 4;</code>
   */
  com.zlim.common.proto.DeviceInfoOrBuilder getDeviceOrBuilder();
}
