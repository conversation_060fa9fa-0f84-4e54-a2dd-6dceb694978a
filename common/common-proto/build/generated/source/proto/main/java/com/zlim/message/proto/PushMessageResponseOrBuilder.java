// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface PushMessageResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.PushMessageResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string gateway_id = 2;</code>
   * @return The gatewayId.
   */
  java.lang.String getGatewayId();
  /**
   * <code>string gateway_id = 2;</code>
   * @return The bytes for gatewayId.
   */
  com.google.protobuf.ByteString
      getGatewayIdBytes();

  /**
   * <code>int32 delivered_count = 3;</code>
   * @return The deliveredCount.
   */
  int getDeliveredCount();

  /**
   * <code>repeated int64 failed_user_ids = 4;</code>
   * @return A list containing the failedUserIds.
   */
  java.util.List<java.lang.Long> getFailedUserIdsList();
  /**
   * <code>repeated int64 failed_user_ids = 4;</code>
   * @return The count of failedUserIds.
   */
  int getFailedUserIdsCount();
  /**
   * <code>repeated int64 failed_user_ids = 4;</code>
   * @param index The index of the element to return.
   * @return The failedUserIds at the given index.
   */
  long getFailedUserIds(int index);
}
