// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

/**
 * <pre>
 * 设备信息
 * </pre>
 *
 * Protobuf type {@code com.zlim.common.DeviceInfo}
 */
public final class DeviceInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.common.DeviceInfo)
    DeviceInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DeviceInfo.newBuilder() to construct.
  private DeviceInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DeviceInfo() {
    deviceId_ = "";
    deviceType_ = "";
    appVersion_ = "";
    osVersion_ = "";
    pushToken_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DeviceInfo();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_DeviceInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_DeviceInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.common.proto.DeviceInfo.class, com.zlim.common.proto.DeviceInfo.Builder.class);
  }

  public static final int DEVICE_ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object deviceId_ = "";
  /**
   * <code>string device_id = 1;</code>
   * @return The deviceId.
   */
  @java.lang.Override
  public java.lang.String getDeviceId() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceId_ = s;
      return s;
    }
  }
  /**
   * <code>string device_id = 1;</code>
   * @return The bytes for deviceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeviceIdBytes() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DEVICE_TYPE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object deviceType_ = "";
  /**
   * <pre>
   * ios, android, web, desktop
   * </pre>
   *
   * <code>string device_type = 2;</code>
   * @return The deviceType.
   */
  @java.lang.Override
  public java.lang.String getDeviceType() {
    java.lang.Object ref = deviceType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * ios, android, web, desktop
   * </pre>
   *
   * <code>string device_type = 2;</code>
   * @return The bytes for deviceType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeviceTypeBytes() {
    java.lang.Object ref = deviceType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_VERSION_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object appVersion_ = "";
  /**
   * <code>string app_version = 3;</code>
   * @return The appVersion.
   */
  @java.lang.Override
  public java.lang.String getAppVersion() {
    java.lang.Object ref = appVersion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appVersion_ = s;
      return s;
    }
  }
  /**
   * <code>string app_version = 3;</code>
   * @return The bytes for appVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppVersionBytes() {
    java.lang.Object ref = appVersion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OS_VERSION_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object osVersion_ = "";
  /**
   * <code>string os_version = 4;</code>
   * @return The osVersion.
   */
  @java.lang.Override
  public java.lang.String getOsVersion() {
    java.lang.Object ref = osVersion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      osVersion_ = s;
      return s;
    }
  }
  /**
   * <code>string os_version = 4;</code>
   * @return The bytes for osVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOsVersionBytes() {
    java.lang.Object ref = osVersion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      osVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PUSH_TOKEN_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object pushToken_ = "";
  /**
   * <code>string push_token = 5;</code>
   * @return The pushToken.
   */
  @java.lang.Override
  public java.lang.String getPushToken() {
    java.lang.Object ref = pushToken_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      pushToken_ = s;
      return s;
    }
  }
  /**
   * <code>string push_token = 5;</code>
   * @return The bytes for pushToken.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPushTokenBytes() {
    java.lang.Object ref = pushToken_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      pushToken_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, deviceId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceType_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, deviceType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, appVersion_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osVersion_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, osVersion_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(pushToken_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, pushToken_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, deviceId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceType_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, deviceType_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, appVersion_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osVersion_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, osVersion_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(pushToken_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, pushToken_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.common.proto.DeviceInfo)) {
      return super.equals(obj);
    }
    com.zlim.common.proto.DeviceInfo other = (com.zlim.common.proto.DeviceInfo) obj;

    if (!getDeviceId()
        .equals(other.getDeviceId())) return false;
    if (!getDeviceType()
        .equals(other.getDeviceType())) return false;
    if (!getAppVersion()
        .equals(other.getAppVersion())) return false;
    if (!getOsVersion()
        .equals(other.getOsVersion())) return false;
    if (!getPushToken()
        .equals(other.getPushToken())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceId().hashCode();
    hash = (37 * hash) + DEVICE_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceType().hashCode();
    hash = (37 * hash) + APP_VERSION_FIELD_NUMBER;
    hash = (53 * hash) + getAppVersion().hashCode();
    hash = (37 * hash) + OS_VERSION_FIELD_NUMBER;
    hash = (53 * hash) + getOsVersion().hashCode();
    hash = (37 * hash) + PUSH_TOKEN_FIELD_NUMBER;
    hash = (53 * hash) + getPushToken().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.common.proto.DeviceInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.common.proto.DeviceInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.common.proto.DeviceInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.common.proto.DeviceInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.common.proto.DeviceInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 设备信息
   * </pre>
   *
   * Protobuf type {@code com.zlim.common.DeviceInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.common.DeviceInfo)
      com.zlim.common.proto.DeviceInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_DeviceInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_DeviceInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.common.proto.DeviceInfo.class, com.zlim.common.proto.DeviceInfo.Builder.class);
    }

    // Construct using com.zlim.common.proto.DeviceInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      deviceId_ = "";
      deviceType_ = "";
      appVersion_ = "";
      osVersion_ = "";
      pushToken_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_DeviceInfo_descriptor;
    }

    @java.lang.Override
    public com.zlim.common.proto.DeviceInfo getDefaultInstanceForType() {
      return com.zlim.common.proto.DeviceInfo.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.common.proto.DeviceInfo build() {
      com.zlim.common.proto.DeviceInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.common.proto.DeviceInfo buildPartial() {
      com.zlim.common.proto.DeviceInfo result = new com.zlim.common.proto.DeviceInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.common.proto.DeviceInfo result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.deviceId_ = deviceId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.deviceType_ = deviceType_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.appVersion_ = appVersion_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.osVersion_ = osVersion_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.pushToken_ = pushToken_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.common.proto.DeviceInfo) {
        return mergeFrom((com.zlim.common.proto.DeviceInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.common.proto.DeviceInfo other) {
      if (other == com.zlim.common.proto.DeviceInfo.getDefaultInstance()) return this;
      if (!other.getDeviceId().isEmpty()) {
        deviceId_ = other.deviceId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getDeviceType().isEmpty()) {
        deviceType_ = other.deviceType_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getAppVersion().isEmpty()) {
        appVersion_ = other.appVersion_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getOsVersion().isEmpty()) {
        osVersion_ = other.osVersion_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getPushToken().isEmpty()) {
        pushToken_ = other.pushToken_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              deviceId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              deviceType_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              appVersion_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              osVersion_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              pushToken_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object deviceId_ = "";
    /**
     * <code>string device_id = 1;</code>
     * @return The deviceId.
     */
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string device_id = 1;</code>
     * @return The bytes for deviceId.
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string device_id = 1;</code>
     * @param value The deviceId to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      deviceId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string device_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeviceId() {
      deviceId_ = getDefaultInstance().getDeviceId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string device_id = 1;</code>
     * @param value The bytes for deviceId to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      deviceId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object deviceType_ = "";
    /**
     * <pre>
     * ios, android, web, desktop
     * </pre>
     *
     * <code>string device_type = 2;</code>
     * @return The deviceType.
     */
    public java.lang.String getDeviceType() {
      java.lang.Object ref = deviceType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * ios, android, web, desktop
     * </pre>
     *
     * <code>string device_type = 2;</code>
     * @return The bytes for deviceType.
     */
    public com.google.protobuf.ByteString
        getDeviceTypeBytes() {
      java.lang.Object ref = deviceType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * ios, android, web, desktop
     * </pre>
     *
     * <code>string device_type = 2;</code>
     * @param value The deviceType to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      deviceType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * ios, android, web, desktop
     * </pre>
     *
     * <code>string device_type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeviceType() {
      deviceType_ = getDefaultInstance().getDeviceType();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * ios, android, web, desktop
     * </pre>
     *
     * <code>string device_type = 2;</code>
     * @param value The bytes for deviceType to set.
     * @return This builder for chaining.
     */
    public Builder setDeviceTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      deviceType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object appVersion_ = "";
    /**
     * <code>string app_version = 3;</code>
     * @return The appVersion.
     */
    public java.lang.String getAppVersion() {
      java.lang.Object ref = appVersion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appVersion_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string app_version = 3;</code>
     * @return The bytes for appVersion.
     */
    public com.google.protobuf.ByteString
        getAppVersionBytes() {
      java.lang.Object ref = appVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string app_version = 3;</code>
     * @param value The appVersion to set.
     * @return This builder for chaining.
     */
    public Builder setAppVersion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      appVersion_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string app_version = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppVersion() {
      appVersion_ = getDefaultInstance().getAppVersion();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string app_version = 3;</code>
     * @param value The bytes for appVersion to set.
     * @return This builder for chaining.
     */
    public Builder setAppVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      appVersion_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object osVersion_ = "";
    /**
     * <code>string os_version = 4;</code>
     * @return The osVersion.
     */
    public java.lang.String getOsVersion() {
      java.lang.Object ref = osVersion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        osVersion_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string os_version = 4;</code>
     * @return The bytes for osVersion.
     */
    public com.google.protobuf.ByteString
        getOsVersionBytes() {
      java.lang.Object ref = osVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        osVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string os_version = 4;</code>
     * @param value The osVersion to set.
     * @return This builder for chaining.
     */
    public Builder setOsVersion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      osVersion_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string os_version = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearOsVersion() {
      osVersion_ = getDefaultInstance().getOsVersion();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string os_version = 4;</code>
     * @param value The bytes for osVersion to set.
     * @return This builder for chaining.
     */
    public Builder setOsVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      osVersion_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object pushToken_ = "";
    /**
     * <code>string push_token = 5;</code>
     * @return The pushToken.
     */
    public java.lang.String getPushToken() {
      java.lang.Object ref = pushToken_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        pushToken_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string push_token = 5;</code>
     * @return The bytes for pushToken.
     */
    public com.google.protobuf.ByteString
        getPushTokenBytes() {
      java.lang.Object ref = pushToken_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pushToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string push_token = 5;</code>
     * @param value The pushToken to set.
     * @return This builder for chaining.
     */
    public Builder setPushToken(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      pushToken_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>string push_token = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPushToken() {
      pushToken_ = getDefaultInstance().getPushToken();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>string push_token = 5;</code>
     * @param value The bytes for pushToken to set.
     * @return This builder for chaining.
     */
    public Builder setPushTokenBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      pushToken_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.common.DeviceInfo)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.common.DeviceInfo)
  private static final com.zlim.common.proto.DeviceInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.common.proto.DeviceInfo();
  }

  public static com.zlim.common.proto.DeviceInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DeviceInfo>
      PARSER = new com.google.protobuf.AbstractParser<DeviceInfo>() {
    @java.lang.Override
    public DeviceInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DeviceInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DeviceInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.common.proto.DeviceInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

