// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 会话信息
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.Conversation}
 */
public final class Conversation extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.Conversation)
    ConversationOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Conversation.newBuilder() to construct.
  private Conversation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Conversation() {
    conversationId_ = "";
    type_ = 0;
    title_ = "";
    avatar_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Conversation();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_Conversation_descriptor;
  }

  @SuppressWarnings({"rawtypes"})
  @java.lang.Override
  protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
      int number) {
    switch (number) {
      case 10:
        return internalGetExtra();
      default:
        throw new RuntimeException(
            "Invalid map field number: " + number);
    }
  }
  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_Conversation_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.Conversation.class, com.zlim.message.proto.Conversation.Builder.class);
  }

  private int bitField0_;
  public static final int CONVERSATION_ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object conversationId_ = "";
  /**
   * <code>string conversation_id = 1;</code>
   * @return The conversationId.
   */
  @java.lang.Override
  public java.lang.String getConversationId() {
    java.lang.Object ref = conversationId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      conversationId_ = s;
      return s;
    }
  }
  /**
   * <code>string conversation_id = 1;</code>
   * @return The bytes for conversationId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConversationIdBytes() {
    java.lang.Object ref = conversationId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      conversationId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_ = 0;
  /**
   * <code>.com.zlim.message.ConversationType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.zlim.message.ConversationType type = 2;</code>
   * @return The type.
   */
  @java.lang.Override public com.zlim.message.proto.ConversationType getType() {
    com.zlim.message.proto.ConversationType result = com.zlim.message.proto.ConversationType.forNumber(type_);
    return result == null ? com.zlim.message.proto.ConversationType.UNRECOGNIZED : result;
  }

  public static final int TITLE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object title_ = "";
  /**
   * <code>string title = 3;</code>
   * @return The title.
   */
  @java.lang.Override
  public java.lang.String getTitle() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      title_ = s;
      return s;
    }
  }
  /**
   * <code>string title = 3;</code>
   * @return The bytes for title.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTitleBytes() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      title_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AVATAR_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object avatar_ = "";
  /**
   * <code>string avatar = 4;</code>
   * @return The avatar.
   */
  @java.lang.Override
  public java.lang.String getAvatar() {
    java.lang.Object ref = avatar_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      avatar_ = s;
      return s;
    }
  }
  /**
   * <code>string avatar = 4;</code>
   * @return The bytes for avatar.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAvatarBytes() {
    java.lang.Object ref = avatar_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      avatar_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAST_MESSAGE_FIELD_NUMBER = 5;
  private com.zlim.message.proto.Message lastMessage_;
  /**
   * <code>.com.zlim.message.Message last_message = 5;</code>
   * @return Whether the lastMessage field is set.
   */
  @java.lang.Override
  public boolean hasLastMessage() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.zlim.message.Message last_message = 5;</code>
   * @return The lastMessage.
   */
  @java.lang.Override
  public com.zlim.message.proto.Message getLastMessage() {
    return lastMessage_ == null ? com.zlim.message.proto.Message.getDefaultInstance() : lastMessage_;
  }
  /**
   * <code>.com.zlim.message.Message last_message = 5;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.MessageOrBuilder getLastMessageOrBuilder() {
    return lastMessage_ == null ? com.zlim.message.proto.Message.getDefaultInstance() : lastMessage_;
  }

  public static final int UNREAD_COUNT_FIELD_NUMBER = 6;
  private int unreadCount_ = 0;
  /**
   * <code>int32 unread_count = 6;</code>
   * @return The unreadCount.
   */
  @java.lang.Override
  public int getUnreadCount() {
    return unreadCount_;
  }

  public static final int MUTED_FIELD_NUMBER = 7;
  private boolean muted_ = false;
  /**
   * <code>bool muted = 7;</code>
   * @return The muted.
   */
  @java.lang.Override
  public boolean getMuted() {
    return muted_;
  }

  public static final int PINNED_FIELD_NUMBER = 8;
  private boolean pinned_ = false;
  /**
   * <code>bool pinned = 8;</code>
   * @return The pinned.
   */
  @java.lang.Override
  public boolean getPinned() {
    return pinned_;
  }

  public static final int UPDATED_AT_FIELD_NUMBER = 9;
  private long updatedAt_ = 0L;
  /**
   * <code>int64 updated_at = 9;</code>
   * @return The updatedAt.
   */
  @java.lang.Override
  public long getUpdatedAt() {
    return updatedAt_;
  }

  public static final int EXTRA_FIELD_NUMBER = 10;
  private static final class ExtraDefaultEntryHolder {
    static final com.google.protobuf.MapEntry<
        java.lang.String, java.lang.String> defaultEntry =
            com.google.protobuf.MapEntry
            .<java.lang.String, java.lang.String>newDefaultInstance(
                com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_Conversation_ExtraEntry_descriptor, 
                com.google.protobuf.WireFormat.FieldType.STRING,
                "",
                com.google.protobuf.WireFormat.FieldType.STRING,
                "");
  }
  @SuppressWarnings("serial")
  private com.google.protobuf.MapField<
      java.lang.String, java.lang.String> extra_;
  private com.google.protobuf.MapField<java.lang.String, java.lang.String>
  internalGetExtra() {
    if (extra_ == null) {
      return com.google.protobuf.MapField.emptyMapField(
          ExtraDefaultEntryHolder.defaultEntry);
    }
    return extra_;
  }
  public int getExtraCount() {
    return internalGetExtra().getMap().size();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  @java.lang.Override
  public boolean containsExtra(
      java.lang.String key) {
    if (key == null) { throw new NullPointerException("map key"); }
    return internalGetExtra().getMap().containsKey(key);
  }
  /**
   * Use {@link #getExtraMap()} instead.
   */
  @java.lang.Override
  @java.lang.Deprecated
  public java.util.Map<java.lang.String, java.lang.String> getExtra() {
    return getExtraMap();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  @java.lang.Override
  public java.util.Map<java.lang.String, java.lang.String> getExtraMap() {
    return internalGetExtra().getMap();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  @java.lang.Override
  public /* nullable */
java.lang.String getExtraOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue) {
    if (key == null) { throw new NullPointerException("map key"); }
    java.util.Map<java.lang.String, java.lang.String> map =
        internalGetExtra().getMap();
    return map.containsKey(key) ? map.get(key) : defaultValue;
  }
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  @java.lang.Override
  public java.lang.String getExtraOrThrow(
      java.lang.String key) {
    if (key == null) { throw new NullPointerException("map key"); }
    java.util.Map<java.lang.String, java.lang.String> map =
        internalGetExtra().getMap();
    if (!map.containsKey(key)) {
      throw new java.lang.IllegalArgumentException();
    }
    return map.get(key);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conversationId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, conversationId_);
    }
    if (type_ != com.zlim.message.proto.ConversationType.CONVERSATION_TYPE_UNKNOWN.getNumber()) {
      output.writeEnum(2, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(title_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, title_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(avatar_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, avatar_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(5, getLastMessage());
    }
    if (unreadCount_ != 0) {
      output.writeInt32(6, unreadCount_);
    }
    if (muted_ != false) {
      output.writeBool(7, muted_);
    }
    if (pinned_ != false) {
      output.writeBool(8, pinned_);
    }
    if (updatedAt_ != 0L) {
      output.writeInt64(9, updatedAt_);
    }
    com.google.protobuf.GeneratedMessageV3
      .serializeStringMapTo(
        output,
        internalGetExtra(),
        ExtraDefaultEntryHolder.defaultEntry,
        10);
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conversationId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, conversationId_);
    }
    if (type_ != com.zlim.message.proto.ConversationType.CONVERSATION_TYPE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, type_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(title_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, title_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(avatar_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, avatar_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getLastMessage());
    }
    if (unreadCount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, unreadCount_);
    }
    if (muted_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, muted_);
    }
    if (pinned_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, pinned_);
    }
    if (updatedAt_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(9, updatedAt_);
    }
    for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
         : internalGetExtra().getMap().entrySet()) {
      com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
      extra__ = ExtraDefaultEntryHolder.defaultEntry.newBuilderForType()
          .setKey(entry.getKey())
          .setValue(entry.getValue())
          .build();
      size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, extra__);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.Conversation)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.Conversation other = (com.zlim.message.proto.Conversation) obj;

    if (!getConversationId()
        .equals(other.getConversationId())) return false;
    if (type_ != other.type_) return false;
    if (!getTitle()
        .equals(other.getTitle())) return false;
    if (!getAvatar()
        .equals(other.getAvatar())) return false;
    if (hasLastMessage() != other.hasLastMessage()) return false;
    if (hasLastMessage()) {
      if (!getLastMessage()
          .equals(other.getLastMessage())) return false;
    }
    if (getUnreadCount()
        != other.getUnreadCount()) return false;
    if (getMuted()
        != other.getMuted()) return false;
    if (getPinned()
        != other.getPinned()) return false;
    if (getUpdatedAt()
        != other.getUpdatedAt()) return false;
    if (!internalGetExtra().equals(
        other.internalGetExtra())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CONVERSATION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getConversationId().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + TITLE_FIELD_NUMBER;
    hash = (53 * hash) + getTitle().hashCode();
    hash = (37 * hash) + AVATAR_FIELD_NUMBER;
    hash = (53 * hash) + getAvatar().hashCode();
    if (hasLastMessage()) {
      hash = (37 * hash) + LAST_MESSAGE_FIELD_NUMBER;
      hash = (53 * hash) + getLastMessage().hashCode();
    }
    hash = (37 * hash) + UNREAD_COUNT_FIELD_NUMBER;
    hash = (53 * hash) + getUnreadCount();
    hash = (37 * hash) + MUTED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getMuted());
    hash = (37 * hash) + PINNED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getPinned());
    hash = (37 * hash) + UPDATED_AT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUpdatedAt());
    if (!internalGetExtra().getMap().isEmpty()) {
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + internalGetExtra().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.Conversation parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.Conversation parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.Conversation parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.Conversation parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.Conversation parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.Conversation parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.Conversation prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 会话信息
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.Conversation}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.Conversation)
      com.zlim.message.proto.ConversationOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_Conversation_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
        int number) {
      switch (number) {
        case 10:
          return internalGetExtra();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
        int number) {
      switch (number) {
        case 10:
          return internalGetMutableExtra();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_Conversation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.Conversation.class, com.zlim.message.proto.Conversation.Builder.class);
    }

    // Construct using com.zlim.message.proto.Conversation.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getLastMessageFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      conversationId_ = "";
      type_ = 0;
      title_ = "";
      avatar_ = "";
      lastMessage_ = null;
      if (lastMessageBuilder_ != null) {
        lastMessageBuilder_.dispose();
        lastMessageBuilder_ = null;
      }
      unreadCount_ = 0;
      muted_ = false;
      pinned_ = false;
      updatedAt_ = 0L;
      internalGetMutableExtra().clear();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_Conversation_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.Conversation getDefaultInstanceForType() {
      return com.zlim.message.proto.Conversation.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.Conversation build() {
      com.zlim.message.proto.Conversation result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.Conversation buildPartial() {
      com.zlim.message.proto.Conversation result = new com.zlim.message.proto.Conversation(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.message.proto.Conversation result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.conversationId_ = conversationId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.title_ = title_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.avatar_ = avatar_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.lastMessage_ = lastMessageBuilder_ == null
            ? lastMessage_
            : lastMessageBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.unreadCount_ = unreadCount_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.muted_ = muted_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.pinned_ = pinned_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.updatedAt_ = updatedAt_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.extra_ = internalGetExtra();
        result.extra_.makeImmutable();
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.Conversation) {
        return mergeFrom((com.zlim.message.proto.Conversation)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.Conversation other) {
      if (other == com.zlim.message.proto.Conversation.getDefaultInstance()) return this;
      if (!other.getConversationId().isEmpty()) {
        conversationId_ = other.conversationId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (!other.getTitle().isEmpty()) {
        title_ = other.title_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getAvatar().isEmpty()) {
        avatar_ = other.avatar_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasLastMessage()) {
        mergeLastMessage(other.getLastMessage());
      }
      if (other.getUnreadCount() != 0) {
        setUnreadCount(other.getUnreadCount());
      }
      if (other.getMuted() != false) {
        setMuted(other.getMuted());
      }
      if (other.getPinned() != false) {
        setPinned(other.getPinned());
      }
      if (other.getUpdatedAt() != 0L) {
        setUpdatedAt(other.getUpdatedAt());
      }
      internalGetMutableExtra().mergeFrom(
          other.internalGetExtra());
      bitField0_ |= 0x00000200;
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              conversationId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              title_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              avatar_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  getLastMessageFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              unreadCount_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              muted_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              pinned_ = input.readBool();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              updatedAt_ = input.readInt64();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extra__ = input.readMessage(
                  ExtraDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              internalGetMutableExtra().getMutableMap().put(
                  extra__.getKey(), extra__.getValue());
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object conversationId_ = "";
    /**
     * <code>string conversation_id = 1;</code>
     * @return The conversationId.
     */
    public java.lang.String getConversationId() {
      java.lang.Object ref = conversationId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        conversationId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @return The bytes for conversationId.
     */
    public com.google.protobuf.ByteString
        getConversationIdBytes() {
      java.lang.Object ref = conversationId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        conversationId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @param value The conversationId to set.
     * @return This builder for chaining.
     */
    public Builder setConversationId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      conversationId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConversationId() {
      conversationId_ = getDefaultInstance().getConversationId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @param value The bytes for conversationId to set.
     * @return This builder for chaining.
     */
    public Builder setConversationIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      conversationId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.zlim.message.ConversationType type = 2;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.zlim.message.ConversationType type = 2;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.ConversationType type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.zlim.message.proto.ConversationType getType() {
      com.zlim.message.proto.ConversationType result = com.zlim.message.proto.ConversationType.forNumber(type_);
      return result == null ? com.zlim.message.proto.ConversationType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.zlim.message.ConversationType type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.zlim.message.proto.ConversationType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000002;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.ConversationType type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object title_ = "";
    /**
     * <code>string title = 3;</code>
     * @return The title.
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string title = 3;</code>
     * @return The bytes for title.
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string title = 3;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string title = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      title_ = getDefaultInstance().getTitle();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string title = 3;</code>
     * @param value The bytes for title to set.
     * @return This builder for chaining.
     */
    public Builder setTitleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      title_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object avatar_ = "";
    /**
     * <code>string avatar = 4;</code>
     * @return The avatar.
     */
    public java.lang.String getAvatar() {
      java.lang.Object ref = avatar_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        avatar_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string avatar = 4;</code>
     * @return The bytes for avatar.
     */
    public com.google.protobuf.ByteString
        getAvatarBytes() {
      java.lang.Object ref = avatar_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        avatar_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string avatar = 4;</code>
     * @param value The avatar to set.
     * @return This builder for chaining.
     */
    public Builder setAvatar(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      avatar_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string avatar = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAvatar() {
      avatar_ = getDefaultInstance().getAvatar();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string avatar = 4;</code>
     * @param value The bytes for avatar to set.
     * @return This builder for chaining.
     */
    public Builder setAvatarBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      avatar_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private com.zlim.message.proto.Message lastMessage_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.Message, com.zlim.message.proto.Message.Builder, com.zlim.message.proto.MessageOrBuilder> lastMessageBuilder_;
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     * @return Whether the lastMessage field is set.
     */
    public boolean hasLastMessage() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     * @return The lastMessage.
     */
    public com.zlim.message.proto.Message getLastMessage() {
      if (lastMessageBuilder_ == null) {
        return lastMessage_ == null ? com.zlim.message.proto.Message.getDefaultInstance() : lastMessage_;
      } else {
        return lastMessageBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    public Builder setLastMessage(com.zlim.message.proto.Message value) {
      if (lastMessageBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        lastMessage_ = value;
      } else {
        lastMessageBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    public Builder setLastMessage(
        com.zlim.message.proto.Message.Builder builderForValue) {
      if (lastMessageBuilder_ == null) {
        lastMessage_ = builderForValue.build();
      } else {
        lastMessageBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    public Builder mergeLastMessage(com.zlim.message.proto.Message value) {
      if (lastMessageBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          lastMessage_ != null &&
          lastMessage_ != com.zlim.message.proto.Message.getDefaultInstance()) {
          getLastMessageBuilder().mergeFrom(value);
        } else {
          lastMessage_ = value;
        }
      } else {
        lastMessageBuilder_.mergeFrom(value);
      }
      if (lastMessage_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    public Builder clearLastMessage() {
      bitField0_ = (bitField0_ & ~0x00000010);
      lastMessage_ = null;
      if (lastMessageBuilder_ != null) {
        lastMessageBuilder_.dispose();
        lastMessageBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    public com.zlim.message.proto.Message.Builder getLastMessageBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return getLastMessageFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    public com.zlim.message.proto.MessageOrBuilder getLastMessageOrBuilder() {
      if (lastMessageBuilder_ != null) {
        return lastMessageBuilder_.getMessageOrBuilder();
      } else {
        return lastMessage_ == null ?
            com.zlim.message.proto.Message.getDefaultInstance() : lastMessage_;
      }
    }
    /**
     * <code>.com.zlim.message.Message last_message = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.Message, com.zlim.message.proto.Message.Builder, com.zlim.message.proto.MessageOrBuilder> 
        getLastMessageFieldBuilder() {
      if (lastMessageBuilder_ == null) {
        lastMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.message.proto.Message, com.zlim.message.proto.Message.Builder, com.zlim.message.proto.MessageOrBuilder>(
                getLastMessage(),
                getParentForChildren(),
                isClean());
        lastMessage_ = null;
      }
      return lastMessageBuilder_;
    }

    private int unreadCount_ ;
    /**
     * <code>int32 unread_count = 6;</code>
     * @return The unreadCount.
     */
    @java.lang.Override
    public int getUnreadCount() {
      return unreadCount_;
    }
    /**
     * <code>int32 unread_count = 6;</code>
     * @param value The unreadCount to set.
     * @return This builder for chaining.
     */
    public Builder setUnreadCount(int value) {

      unreadCount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>int32 unread_count = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnreadCount() {
      bitField0_ = (bitField0_ & ~0x00000020);
      unreadCount_ = 0;
      onChanged();
      return this;
    }

    private boolean muted_ ;
    /**
     * <code>bool muted = 7;</code>
     * @return The muted.
     */
    @java.lang.Override
    public boolean getMuted() {
      return muted_;
    }
    /**
     * <code>bool muted = 7;</code>
     * @param value The muted to set.
     * @return This builder for chaining.
     */
    public Builder setMuted(boolean value) {

      muted_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>bool muted = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearMuted() {
      bitField0_ = (bitField0_ & ~0x00000040);
      muted_ = false;
      onChanged();
      return this;
    }

    private boolean pinned_ ;
    /**
     * <code>bool pinned = 8;</code>
     * @return The pinned.
     */
    @java.lang.Override
    public boolean getPinned() {
      return pinned_;
    }
    /**
     * <code>bool pinned = 8;</code>
     * @param value The pinned to set.
     * @return This builder for chaining.
     */
    public Builder setPinned(boolean value) {

      pinned_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>bool pinned = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPinned() {
      bitField0_ = (bitField0_ & ~0x00000080);
      pinned_ = false;
      onChanged();
      return this;
    }

    private long updatedAt_ ;
    /**
     * <code>int64 updated_at = 9;</code>
     * @return The updatedAt.
     */
    @java.lang.Override
    public long getUpdatedAt() {
      return updatedAt_;
    }
    /**
     * <code>int64 updated_at = 9;</code>
     * @param value The updatedAt to set.
     * @return This builder for chaining.
     */
    public Builder setUpdatedAt(long value) {

      updatedAt_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>int64 updated_at = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdatedAt() {
      bitField0_ = (bitField0_ & ~0x00000100);
      updatedAt_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extra_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetExtra() {
      if (extra_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtraDefaultEntryHolder.defaultEntry);
      }
      return extra_;
    }
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetMutableExtra() {
      if (extra_ == null) {
        extra_ = com.google.protobuf.MapField.newMapField(
            ExtraDefaultEntryHolder.defaultEntry);
      }
      if (!extra_.isMutable()) {
        extra_ = extra_.copy();
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return extra_;
    }
    public int getExtraCount() {
      return internalGetExtra().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    @java.lang.Override
    public boolean containsExtra(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetExtra().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtraMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtra() {
      return getExtraMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    @java.lang.Override
    public java.util.Map<java.lang.String, java.lang.String> getExtraMap() {
      return internalGetExtra().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    @java.lang.Override
    public /* nullable */
java.lang.String getExtraOrDefault(
        java.lang.String key,
        /* nullable */
java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtra().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    @java.lang.Override
    public java.lang.String getExtraOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtra().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }
    public Builder clearExtra() {
      bitField0_ = (bitField0_ & ~0x00000200);
      internalGetMutableExtra().getMutableMap()
          .clear();
      return this;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    public Builder removeExtra(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      internalGetMutableExtra().getMutableMap()
          .remove(key);
      return this;
    }
    /**
     * Use alternate mutation accessors instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String>
        getMutableExtra() {
      bitField0_ |= 0x00000200;
      return internalGetMutableExtra().getMutableMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    public Builder putExtra(
        java.lang.String key,
        java.lang.String value) {
      if (key == null) { throw new NullPointerException("map key"); }
      if (value == null) { throw new NullPointerException("map value"); }
      internalGetMutableExtra().getMutableMap()
          .put(key, value);
      bitField0_ |= 0x00000200;
      return this;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 10;</code>
     */
    public Builder putAllExtra(
        java.util.Map<java.lang.String, java.lang.String> values) {
      internalGetMutableExtra().getMutableMap()
          .putAll(values);
      bitField0_ |= 0x00000200;
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.Conversation)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.Conversation)
  private static final com.zlim.message.proto.Conversation DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.Conversation();
  }

  public static com.zlim.message.proto.Conversation getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Conversation>
      PARSER = new com.google.protobuf.AbstractParser<Conversation>() {
    @java.lang.Override
    public Conversation parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Conversation> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Conversation> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.Conversation getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

