// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface LocationContentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.LocationContent)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>double latitude = 1;</code>
   * @return The latitude.
   */
  double getLatitude();

  /**
   * <code>double longitude = 2;</code>
   * @return The longitude.
   */
  double getLongitude();

  /**
   * <code>string address = 3;</code>
   * @return The address.
   */
  java.lang.String getAddress();
  /**
   * <code>string address = 3;</code>
   * @return The bytes for address.
   */
  com.google.protobuf.ByteString
      getAddressBytes();

  /**
   * <code>string title = 4;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <code>string title = 4;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();
}
