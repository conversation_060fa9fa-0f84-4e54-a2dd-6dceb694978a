// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 在线状态类型
 * </pre>
 *
 * Protobuf enum {@code com.zlim.message.PresenceType}
 */
public enum PresenceType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>PRESENCE_TYPE_UNKNOWN = 0;</code>
   */
  PRESENCE_TYPE_UNKNOWN(0),
  /**
   * <code>PRESENCE_TYPE_ONLINE = 1;</code>
   */
  PRESENCE_TYPE_ONLINE(1),
  /**
   * <code>PRESENCE_TYPE_AWAY = 2;</code>
   */
  PRESENCE_TYPE_AWAY(2),
  /**
   * <code>PRESENCE_TYPE_BUSY = 3;</code>
   */
  PRESENCE_TYPE_BUSY(3),
  /**
   * <code>PRESENCE_TYPE_OFFLINE = 4;</code>
   */
  PRESENCE_TYPE_OFFLINE(4),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>PRESENCE_TYPE_UNKNOWN = 0;</code>
   */
  public static final int PRESENCE_TYPE_UNKNOWN_VALUE = 0;
  /**
   * <code>PRESENCE_TYPE_ONLINE = 1;</code>
   */
  public static final int PRESENCE_TYPE_ONLINE_VALUE = 1;
  /**
   * <code>PRESENCE_TYPE_AWAY = 2;</code>
   */
  public static final int PRESENCE_TYPE_AWAY_VALUE = 2;
  /**
   * <code>PRESENCE_TYPE_BUSY = 3;</code>
   */
  public static final int PRESENCE_TYPE_BUSY_VALUE = 3;
  /**
   * <code>PRESENCE_TYPE_OFFLINE = 4;</code>
   */
  public static final int PRESENCE_TYPE_OFFLINE_VALUE = 4;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static PresenceType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static PresenceType forNumber(int value) {
    switch (value) {
      case 0: return PRESENCE_TYPE_UNKNOWN;
      case 1: return PRESENCE_TYPE_ONLINE;
      case 2: return PRESENCE_TYPE_AWAY;
      case 3: return PRESENCE_TYPE_BUSY;
      case 4: return PRESENCE_TYPE_OFFLINE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<PresenceType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      PresenceType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<PresenceType>() {
          public PresenceType findValueByNumber(int number) {
            return PresenceType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.getDescriptor().getEnumTypes().get(3);
  }

  private static final PresenceType[] VALUES = values();

  public static PresenceType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private PresenceType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.zlim.message.PresenceType)
}

