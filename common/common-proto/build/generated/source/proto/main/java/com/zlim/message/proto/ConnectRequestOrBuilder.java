// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface ConnectRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.ConnectRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string access_token = 1;</code>
   * @return The accessToken.
   */
  java.lang.String getAccessToken();
  /**
   * <code>string access_token = 1;</code>
   * @return The bytes for accessToken.
   */
  com.google.protobuf.ByteString
      getAccessTokenBytes();

  /**
   * <code>.com.zlim.common.DeviceInfo device = 2;</code>
   * @return Whether the device field is set.
   */
  boolean hasDevice();
  /**
   * <code>.com.zlim.common.DeviceInfo device = 2;</code>
   * @return The device.
   */
  com.zlim.common.proto.DeviceInfo getDevice();
  /**
   * <code>.com.zlim.common.DeviceInfo device = 2;</code>
   */
  com.zlim.common.proto.DeviceInfoOrBuilder getDeviceOrBuilder();

  /**
   * <code>bool reconnect = 3;</code>
   * @return The reconnect.
   */
  boolean getReconnect();

  /**
   * <code>int64 last_seq = 4;</code>
   * @return The lastSeq.
   */
  long getLastSeq();
}
