// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 消息状态
 * </pre>
 *
 * Protobuf enum {@code com.zlim.message.MessageStatus}
 */
public enum MessageStatus
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>MESSAGE_STATUS_UNKNOWN = 0;</code>
   */
  MESSAGE_STATUS_UNKNOWN(0),
  /**
   * <code>MESSAGE_STATUS_SENDING = 1;</code>
   */
  MESSAGE_STATUS_SENDING(1),
  /**
   * <code>MESSAGE_STATUS_SENT = 2;</code>
   */
  MESSAGE_STATUS_SENT(2),
  /**
   * <code>MESSAGE_STATUS_DELIVERED = 3;</code>
   */
  MESSAGE_STATUS_DELIVERED(3),
  /**
   * <code>MESSAGE_STATUS_READ = 4;</code>
   */
  MESSAGE_STATUS_READ(4),
  /**
   * <code>MESSAGE_STATUS_FAILED = 5;</code>
   */
  MESSAGE_STATUS_FAILED(5),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>MESSAGE_STATUS_UNKNOWN = 0;</code>
   */
  public static final int MESSAGE_STATUS_UNKNOWN_VALUE = 0;
  /**
   * <code>MESSAGE_STATUS_SENDING = 1;</code>
   */
  public static final int MESSAGE_STATUS_SENDING_VALUE = 1;
  /**
   * <code>MESSAGE_STATUS_SENT = 2;</code>
   */
  public static final int MESSAGE_STATUS_SENT_VALUE = 2;
  /**
   * <code>MESSAGE_STATUS_DELIVERED = 3;</code>
   */
  public static final int MESSAGE_STATUS_DELIVERED_VALUE = 3;
  /**
   * <code>MESSAGE_STATUS_READ = 4;</code>
   */
  public static final int MESSAGE_STATUS_READ_VALUE = 4;
  /**
   * <code>MESSAGE_STATUS_FAILED = 5;</code>
   */
  public static final int MESSAGE_STATUS_FAILED_VALUE = 5;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static MessageStatus valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static MessageStatus forNumber(int value) {
    switch (value) {
      case 0: return MESSAGE_STATUS_UNKNOWN;
      case 1: return MESSAGE_STATUS_SENDING;
      case 2: return MESSAGE_STATUS_SENT;
      case 3: return MESSAGE_STATUS_DELIVERED;
      case 4: return MESSAGE_STATUS_READ;
      case 5: return MESSAGE_STATUS_FAILED;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<MessageStatus>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      MessageStatus> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<MessageStatus>() {
          public MessageStatus findValueByNumber(int number) {
            return MessageStatus.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.getDescriptor().getEnumTypes().get(2);
  }

  private static final MessageStatus[] VALUES = values();

  public static MessageStatus valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private MessageStatus(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.zlim.message.MessageStatus)
}

