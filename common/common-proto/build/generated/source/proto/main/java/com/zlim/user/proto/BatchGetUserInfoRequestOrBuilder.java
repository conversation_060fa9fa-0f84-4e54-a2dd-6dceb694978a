// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface BatchGetUserInfoRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.BatchGetUserInfoRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated int64 user_ids = 1;</code>
   * @return A list containing the userIds.
   */
  java.util.List<java.lang.Long> getUserIdsList();
  /**
   * <code>repeated int64 user_ids = 1;</code>
   * @return The count of userIds.
   */
  int getUserIdsCount();
  /**
   * <code>repeated int64 user_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The userIds at the given index.
   */
  long getUserIds(int index);
}
