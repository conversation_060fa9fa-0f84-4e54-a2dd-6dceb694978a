// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 文本消息
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.TextContent}
 */
public final class TextContent extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.TextContent)
    TextContentOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TextContent.newBuilder() to construct.
  private TextContent(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TextContent() {
    text_ = "";
    mentions_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TextContent();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_TextContent_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_TextContent_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.TextContent.class, com.zlim.message.proto.TextContent.Builder.class);
  }

  public static final int TEXT_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object text_ = "";
  /**
   * <code>string text = 1;</code>
   * @return The text.
   */
  @java.lang.Override
  public java.lang.String getText() {
    java.lang.Object ref = text_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      text_ = s;
      return s;
    }
  }
  /**
   * <code>string text = 1;</code>
   * @return The bytes for text.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTextBytes() {
    java.lang.Object ref = text_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      text_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MENTIONS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<com.zlim.message.proto.MentionInfo> mentions_;
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  @java.lang.Override
  public java.util.List<com.zlim.message.proto.MentionInfo> getMentionsList() {
    return mentions_;
  }
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.zlim.message.proto.MentionInfoOrBuilder> 
      getMentionsOrBuilderList() {
    return mentions_;
  }
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  @java.lang.Override
  public int getMentionsCount() {
    return mentions_.size();
  }
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.MentionInfo getMentions(int index) {
    return mentions_.get(index);
  }
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.MentionInfoOrBuilder getMentionsOrBuilder(
      int index) {
    return mentions_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(text_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, text_);
    }
    for (int i = 0; i < mentions_.size(); i++) {
      output.writeMessage(2, mentions_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(text_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, text_);
    }
    for (int i = 0; i < mentions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, mentions_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.TextContent)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.TextContent other = (com.zlim.message.proto.TextContent) obj;

    if (!getText()
        .equals(other.getText())) return false;
    if (!getMentionsList()
        .equals(other.getMentionsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TEXT_FIELD_NUMBER;
    hash = (53 * hash) + getText().hashCode();
    if (getMentionsCount() > 0) {
      hash = (37 * hash) + MENTIONS_FIELD_NUMBER;
      hash = (53 * hash) + getMentionsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.TextContent parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.TextContent parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.TextContent parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.TextContent parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.TextContent parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.TextContent parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.TextContent prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 文本消息
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.TextContent}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.TextContent)
      com.zlim.message.proto.TextContentOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_TextContent_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_TextContent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.TextContent.class, com.zlim.message.proto.TextContent.Builder.class);
    }

    // Construct using com.zlim.message.proto.TextContent.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      text_ = "";
      if (mentionsBuilder_ == null) {
        mentions_ = java.util.Collections.emptyList();
      } else {
        mentions_ = null;
        mentionsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_TextContent_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.TextContent getDefaultInstanceForType() {
      return com.zlim.message.proto.TextContent.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.TextContent build() {
      com.zlim.message.proto.TextContent result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.TextContent buildPartial() {
      com.zlim.message.proto.TextContent result = new com.zlim.message.proto.TextContent(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.zlim.message.proto.TextContent result) {
      if (mentionsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          mentions_ = java.util.Collections.unmodifiableList(mentions_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.mentions_ = mentions_;
      } else {
        result.mentions_ = mentionsBuilder_.build();
      }
    }

    private void buildPartial0(com.zlim.message.proto.TextContent result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.text_ = text_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.TextContent) {
        return mergeFrom((com.zlim.message.proto.TextContent)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.TextContent other) {
      if (other == com.zlim.message.proto.TextContent.getDefaultInstance()) return this;
      if (!other.getText().isEmpty()) {
        text_ = other.text_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (mentionsBuilder_ == null) {
        if (!other.mentions_.isEmpty()) {
          if (mentions_.isEmpty()) {
            mentions_ = other.mentions_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureMentionsIsMutable();
            mentions_.addAll(other.mentions_);
          }
          onChanged();
        }
      } else {
        if (!other.mentions_.isEmpty()) {
          if (mentionsBuilder_.isEmpty()) {
            mentionsBuilder_.dispose();
            mentionsBuilder_ = null;
            mentions_ = other.mentions_;
            bitField0_ = (bitField0_ & ~0x00000002);
            mentionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMentionsFieldBuilder() : null;
          } else {
            mentionsBuilder_.addAllMessages(other.mentions_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              text_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              com.zlim.message.proto.MentionInfo m =
                  input.readMessage(
                      com.zlim.message.proto.MentionInfo.parser(),
                      extensionRegistry);
              if (mentionsBuilder_ == null) {
                ensureMentionsIsMutable();
                mentions_.add(m);
              } else {
                mentionsBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object text_ = "";
    /**
     * <code>string text = 1;</code>
     * @return The text.
     */
    public java.lang.String getText() {
      java.lang.Object ref = text_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        text_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string text = 1;</code>
     * @return The bytes for text.
     */
    public com.google.protobuf.ByteString
        getTextBytes() {
      java.lang.Object ref = text_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        text_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string text = 1;</code>
     * @param value The text to set.
     * @return This builder for chaining.
     */
    public Builder setText(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      text_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string text = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearText() {
      text_ = getDefaultInstance().getText();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string text = 1;</code>
     * @param value The bytes for text to set.
     * @return This builder for chaining.
     */
    public Builder setTextBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      text_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.util.List<com.zlim.message.proto.MentionInfo> mentions_ =
      java.util.Collections.emptyList();
    private void ensureMentionsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        mentions_ = new java.util.ArrayList<com.zlim.message.proto.MentionInfo>(mentions_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.message.proto.MentionInfo, com.zlim.message.proto.MentionInfo.Builder, com.zlim.message.proto.MentionInfoOrBuilder> mentionsBuilder_;

    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public java.util.List<com.zlim.message.proto.MentionInfo> getMentionsList() {
      if (mentionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mentions_);
      } else {
        return mentionsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public int getMentionsCount() {
      if (mentionsBuilder_ == null) {
        return mentions_.size();
      } else {
        return mentionsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public com.zlim.message.proto.MentionInfo getMentions(int index) {
      if (mentionsBuilder_ == null) {
        return mentions_.get(index);
      } else {
        return mentionsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder setMentions(
        int index, com.zlim.message.proto.MentionInfo value) {
      if (mentionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMentionsIsMutable();
        mentions_.set(index, value);
        onChanged();
      } else {
        mentionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder setMentions(
        int index, com.zlim.message.proto.MentionInfo.Builder builderForValue) {
      if (mentionsBuilder_ == null) {
        ensureMentionsIsMutable();
        mentions_.set(index, builderForValue.build());
        onChanged();
      } else {
        mentionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder addMentions(com.zlim.message.proto.MentionInfo value) {
      if (mentionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMentionsIsMutable();
        mentions_.add(value);
        onChanged();
      } else {
        mentionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder addMentions(
        int index, com.zlim.message.proto.MentionInfo value) {
      if (mentionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMentionsIsMutable();
        mentions_.add(index, value);
        onChanged();
      } else {
        mentionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder addMentions(
        com.zlim.message.proto.MentionInfo.Builder builderForValue) {
      if (mentionsBuilder_ == null) {
        ensureMentionsIsMutable();
        mentions_.add(builderForValue.build());
        onChanged();
      } else {
        mentionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder addMentions(
        int index, com.zlim.message.proto.MentionInfo.Builder builderForValue) {
      if (mentionsBuilder_ == null) {
        ensureMentionsIsMutable();
        mentions_.add(index, builderForValue.build());
        onChanged();
      } else {
        mentionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder addAllMentions(
        java.lang.Iterable<? extends com.zlim.message.proto.MentionInfo> values) {
      if (mentionsBuilder_ == null) {
        ensureMentionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mentions_);
        onChanged();
      } else {
        mentionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder clearMentions() {
      if (mentionsBuilder_ == null) {
        mentions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        mentionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public Builder removeMentions(int index) {
      if (mentionsBuilder_ == null) {
        ensureMentionsIsMutable();
        mentions_.remove(index);
        onChanged();
      } else {
        mentionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public com.zlim.message.proto.MentionInfo.Builder getMentionsBuilder(
        int index) {
      return getMentionsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public com.zlim.message.proto.MentionInfoOrBuilder getMentionsOrBuilder(
        int index) {
      if (mentionsBuilder_ == null) {
        return mentions_.get(index);  } else {
        return mentionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public java.util.List<? extends com.zlim.message.proto.MentionInfoOrBuilder> 
         getMentionsOrBuilderList() {
      if (mentionsBuilder_ != null) {
        return mentionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mentions_);
      }
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public com.zlim.message.proto.MentionInfo.Builder addMentionsBuilder() {
      return getMentionsFieldBuilder().addBuilder(
          com.zlim.message.proto.MentionInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public com.zlim.message.proto.MentionInfo.Builder addMentionsBuilder(
        int index) {
      return getMentionsFieldBuilder().addBuilder(
          index, com.zlim.message.proto.MentionInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
     */
    public java.util.List<com.zlim.message.proto.MentionInfo.Builder> 
         getMentionsBuilderList() {
      return getMentionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.zlim.message.proto.MentionInfo, com.zlim.message.proto.MentionInfo.Builder, com.zlim.message.proto.MentionInfoOrBuilder> 
        getMentionsFieldBuilder() {
      if (mentionsBuilder_ == null) {
        mentionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.zlim.message.proto.MentionInfo, com.zlim.message.proto.MentionInfo.Builder, com.zlim.message.proto.MentionInfoOrBuilder>(
                mentions_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        mentions_ = null;
      }
      return mentionsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.TextContent)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.TextContent)
  private static final com.zlim.message.proto.TextContent DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.TextContent();
  }

  public static com.zlim.message.proto.TextContent getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TextContent>
      PARSER = new com.google.protobuf.AbstractParser<TextContent>() {
    @java.lang.Override
    public TextContent parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TextContent> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TextContent> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.TextContent getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

