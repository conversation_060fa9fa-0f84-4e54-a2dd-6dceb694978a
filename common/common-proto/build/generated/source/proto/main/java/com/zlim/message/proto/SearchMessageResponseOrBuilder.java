// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface SearchMessageResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.SearchMessageResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  java.util.List<com.zlim.message.proto.Message> 
      getMessagesList();
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  com.zlim.message.proto.Message getMessages(int index);
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  int getMessagesCount();
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  java.util.List<? extends com.zlim.message.proto.MessageOrBuilder> 
      getMessagesOrBuilderList();
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  com.zlim.message.proto.MessageOrBuilder getMessagesOrBuilder(
      int index);

  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   * @return Whether the page field is set.
   */
  boolean hasPage();
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   * @return The page.
   */
  com.zlim.common.proto.PageResponse getPage();
  /**
   * <code>.com.zlim.common.PageResponse page = 2;</code>
   */
  com.zlim.common.proto.PageResponseOrBuilder getPageOrBuilder();
}
