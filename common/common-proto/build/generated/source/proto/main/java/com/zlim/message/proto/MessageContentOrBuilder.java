// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface MessageContentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.MessageContent)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.com.zlim.message.TextContent text = 1;</code>
   * @return Whether the text field is set.
   */
  boolean hasText();
  /**
   * <code>.com.zlim.message.TextContent text = 1;</code>
   * @return The text.
   */
  com.zlim.message.proto.TextContent getText();
  /**
   * <code>.com.zlim.message.TextContent text = 1;</code>
   */
  com.zlim.message.proto.TextContentOrBuilder getTextOrBuilder();

  /**
   * <code>.com.zlim.message.MediaContent media = 2;</code>
   * @return Whether the media field is set.
   */
  boolean hasMedia();
  /**
   * <code>.com.zlim.message.MediaContent media = 2;</code>
   * @return The media.
   */
  com.zlim.message.proto.MediaContent getMedia();
  /**
   * <code>.com.zlim.message.MediaContent media = 2;</code>
   */
  com.zlim.message.proto.MediaContentOrBuilder getMediaOrBuilder();

  /**
   * <code>.com.zlim.message.LocationContent location = 3;</code>
   * @return Whether the location field is set.
   */
  boolean hasLocation();
  /**
   * <code>.com.zlim.message.LocationContent location = 3;</code>
   * @return The location.
   */
  com.zlim.message.proto.LocationContent getLocation();
  /**
   * <code>.com.zlim.message.LocationContent location = 3;</code>
   */
  com.zlim.message.proto.LocationContentOrBuilder getLocationOrBuilder();

  /**
   * <code>.com.zlim.message.SystemContent system = 4;</code>
   * @return Whether the system field is set.
   */
  boolean hasSystem();
  /**
   * <code>.com.zlim.message.SystemContent system = 4;</code>
   * @return The system.
   */
  com.zlim.message.proto.SystemContent getSystem();
  /**
   * <code>.com.zlim.message.SystemContent system = 4;</code>
   */
  com.zlim.message.proto.SystemContentOrBuilder getSystemOrBuilder();

  com.zlim.message.proto.MessageContent.ContentCase getContentCase();
}
