// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 连接请求
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.ConnectRequest}
 */
public final class ConnectRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.ConnectRequest)
    ConnectRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConnectRequest.newBuilder() to construct.
  private ConnectRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConnectRequest() {
    accessToken_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConnectRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_ConnectRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_ConnectRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.ConnectRequest.class, com.zlim.message.proto.ConnectRequest.Builder.class);
  }

  private int bitField0_;
  public static final int ACCESS_TOKEN_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object accessToken_ = "";
  /**
   * <code>string access_token = 1;</code>
   * @return The accessToken.
   */
  @java.lang.Override
  public java.lang.String getAccessToken() {
    java.lang.Object ref = accessToken_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      accessToken_ = s;
      return s;
    }
  }
  /**
   * <code>string access_token = 1;</code>
   * @return The bytes for accessToken.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAccessTokenBytes() {
    java.lang.Object ref = accessToken_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      accessToken_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DEVICE_FIELD_NUMBER = 2;
  private com.zlim.common.proto.DeviceInfo device_;
  /**
   * <code>.com.zlim.common.DeviceInfo device = 2;</code>
   * @return Whether the device field is set.
   */
  @java.lang.Override
  public boolean hasDevice() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.zlim.common.DeviceInfo device = 2;</code>
   * @return The device.
   */
  @java.lang.Override
  public com.zlim.common.proto.DeviceInfo getDevice() {
    return device_ == null ? com.zlim.common.proto.DeviceInfo.getDefaultInstance() : device_;
  }
  /**
   * <code>.com.zlim.common.DeviceInfo device = 2;</code>
   */
  @java.lang.Override
  public com.zlim.common.proto.DeviceInfoOrBuilder getDeviceOrBuilder() {
    return device_ == null ? com.zlim.common.proto.DeviceInfo.getDefaultInstance() : device_;
  }

  public static final int RECONNECT_FIELD_NUMBER = 3;
  private boolean reconnect_ = false;
  /**
   * <code>bool reconnect = 3;</code>
   * @return The reconnect.
   */
  @java.lang.Override
  public boolean getReconnect() {
    return reconnect_;
  }

  public static final int LAST_SEQ_FIELD_NUMBER = 4;
  private long lastSeq_ = 0L;
  /**
   * <code>int64 last_seq = 4;</code>
   * @return The lastSeq.
   */
  @java.lang.Override
  public long getLastSeq() {
    return lastSeq_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(accessToken_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, accessToken_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getDevice());
    }
    if (reconnect_ != false) {
      output.writeBool(3, reconnect_);
    }
    if (lastSeq_ != 0L) {
      output.writeInt64(4, lastSeq_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(accessToken_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, accessToken_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getDevice());
    }
    if (reconnect_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, reconnect_);
    }
    if (lastSeq_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, lastSeq_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.ConnectRequest)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.ConnectRequest other = (com.zlim.message.proto.ConnectRequest) obj;

    if (!getAccessToken()
        .equals(other.getAccessToken())) return false;
    if (hasDevice() != other.hasDevice()) return false;
    if (hasDevice()) {
      if (!getDevice()
          .equals(other.getDevice())) return false;
    }
    if (getReconnect()
        != other.getReconnect()) return false;
    if (getLastSeq()
        != other.getLastSeq()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACCESS_TOKEN_FIELD_NUMBER;
    hash = (53 * hash) + getAccessToken().hashCode();
    if (hasDevice()) {
      hash = (37 * hash) + DEVICE_FIELD_NUMBER;
      hash = (53 * hash) + getDevice().hashCode();
    }
    hash = (37 * hash) + RECONNECT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getReconnect());
    hash = (37 * hash) + LAST_SEQ_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getLastSeq());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.ConnectRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.ConnectRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.ConnectRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.ConnectRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.ConnectRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 连接请求
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.ConnectRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.ConnectRequest)
      com.zlim.message.proto.ConnectRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_ConnectRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_ConnectRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.ConnectRequest.class, com.zlim.message.proto.ConnectRequest.Builder.class);
    }

    // Construct using com.zlim.message.proto.ConnectRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getDeviceFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      accessToken_ = "";
      device_ = null;
      if (deviceBuilder_ != null) {
        deviceBuilder_.dispose();
        deviceBuilder_ = null;
      }
      reconnect_ = false;
      lastSeq_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageProto.internal_static_com_zlim_message_ConnectRequest_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.ConnectRequest getDefaultInstanceForType() {
      return com.zlim.message.proto.ConnectRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.ConnectRequest build() {
      com.zlim.message.proto.ConnectRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.ConnectRequest buildPartial() {
      com.zlim.message.proto.ConnectRequest result = new com.zlim.message.proto.ConnectRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.message.proto.ConnectRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.accessToken_ = accessToken_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.device_ = deviceBuilder_ == null
            ? device_
            : deviceBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.reconnect_ = reconnect_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.lastSeq_ = lastSeq_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.ConnectRequest) {
        return mergeFrom((com.zlim.message.proto.ConnectRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.ConnectRequest other) {
      if (other == com.zlim.message.proto.ConnectRequest.getDefaultInstance()) return this;
      if (!other.getAccessToken().isEmpty()) {
        accessToken_ = other.accessToken_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasDevice()) {
        mergeDevice(other.getDevice());
      }
      if (other.getReconnect() != false) {
        setReconnect(other.getReconnect());
      }
      if (other.getLastSeq() != 0L) {
        setLastSeq(other.getLastSeq());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              accessToken_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getDeviceFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              reconnect_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              lastSeq_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object accessToken_ = "";
    /**
     * <code>string access_token = 1;</code>
     * @return The accessToken.
     */
    public java.lang.String getAccessToken() {
      java.lang.Object ref = accessToken_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        accessToken_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string access_token = 1;</code>
     * @return The bytes for accessToken.
     */
    public com.google.protobuf.ByteString
        getAccessTokenBytes() {
      java.lang.Object ref = accessToken_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        accessToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string access_token = 1;</code>
     * @param value The accessToken to set.
     * @return This builder for chaining.
     */
    public Builder setAccessToken(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      accessToken_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string access_token = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccessToken() {
      accessToken_ = getDefaultInstance().getAccessToken();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string access_token = 1;</code>
     * @param value The bytes for accessToken to set.
     * @return This builder for chaining.
     */
    public Builder setAccessTokenBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      accessToken_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private com.zlim.common.proto.DeviceInfo device_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.common.proto.DeviceInfo, com.zlim.common.proto.DeviceInfo.Builder, com.zlim.common.proto.DeviceInfoOrBuilder> deviceBuilder_;
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     * @return Whether the device field is set.
     */
    public boolean hasDevice() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     * @return The device.
     */
    public com.zlim.common.proto.DeviceInfo getDevice() {
      if (deviceBuilder_ == null) {
        return device_ == null ? com.zlim.common.proto.DeviceInfo.getDefaultInstance() : device_;
      } else {
        return deviceBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    public Builder setDevice(com.zlim.common.proto.DeviceInfo value) {
      if (deviceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        device_ = value;
      } else {
        deviceBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    public Builder setDevice(
        com.zlim.common.proto.DeviceInfo.Builder builderForValue) {
      if (deviceBuilder_ == null) {
        device_ = builderForValue.build();
      } else {
        deviceBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    public Builder mergeDevice(com.zlim.common.proto.DeviceInfo value) {
      if (deviceBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          device_ != null &&
          device_ != com.zlim.common.proto.DeviceInfo.getDefaultInstance()) {
          getDeviceBuilder().mergeFrom(value);
        } else {
          device_ = value;
        }
      } else {
        deviceBuilder_.mergeFrom(value);
      }
      if (device_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    public Builder clearDevice() {
      bitField0_ = (bitField0_ & ~0x00000002);
      device_ = null;
      if (deviceBuilder_ != null) {
        deviceBuilder_.dispose();
        deviceBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    public com.zlim.common.proto.DeviceInfo.Builder getDeviceBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getDeviceFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    public com.zlim.common.proto.DeviceInfoOrBuilder getDeviceOrBuilder() {
      if (deviceBuilder_ != null) {
        return deviceBuilder_.getMessageOrBuilder();
      } else {
        return device_ == null ?
            com.zlim.common.proto.DeviceInfo.getDefaultInstance() : device_;
      }
    }
    /**
     * <code>.com.zlim.common.DeviceInfo device = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.common.proto.DeviceInfo, com.zlim.common.proto.DeviceInfo.Builder, com.zlim.common.proto.DeviceInfoOrBuilder> 
        getDeviceFieldBuilder() {
      if (deviceBuilder_ == null) {
        deviceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.common.proto.DeviceInfo, com.zlim.common.proto.DeviceInfo.Builder, com.zlim.common.proto.DeviceInfoOrBuilder>(
                getDevice(),
                getParentForChildren(),
                isClean());
        device_ = null;
      }
      return deviceBuilder_;
    }

    private boolean reconnect_ ;
    /**
     * <code>bool reconnect = 3;</code>
     * @return The reconnect.
     */
    @java.lang.Override
    public boolean getReconnect() {
      return reconnect_;
    }
    /**
     * <code>bool reconnect = 3;</code>
     * @param value The reconnect to set.
     * @return This builder for chaining.
     */
    public Builder setReconnect(boolean value) {

      reconnect_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>bool reconnect = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearReconnect() {
      bitField0_ = (bitField0_ & ~0x00000004);
      reconnect_ = false;
      onChanged();
      return this;
    }

    private long lastSeq_ ;
    /**
     * <code>int64 last_seq = 4;</code>
     * @return The lastSeq.
     */
    @java.lang.Override
    public long getLastSeq() {
      return lastSeq_;
    }
    /**
     * <code>int64 last_seq = 4;</code>
     * @param value The lastSeq to set.
     * @return This builder for chaining.
     */
    public Builder setLastSeq(long value) {

      lastSeq_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>int64 last_seq = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastSeq() {
      bitField0_ = (bitField0_ & ~0x00000008);
      lastSeq_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.ConnectRequest)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.ConnectRequest)
  private static final com.zlim.message.proto.ConnectRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.ConnectRequest();
  }

  public static com.zlim.message.proto.ConnectRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConnectRequest>
      PARSER = new com.google.protobuf.AbstractParser<ConnectRequest>() {
    @java.lang.Override
    public ConnectRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ConnectRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConnectRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.ConnectRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

