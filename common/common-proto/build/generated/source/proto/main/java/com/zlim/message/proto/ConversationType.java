// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 会话类型
 * </pre>
 *
 * Protobuf enum {@code com.zlim.message.ConversationType}
 */
public enum ConversationType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>CONVERSATION_TYPE_UNKNOWN = 0;</code>
   */
  CONVERSATION_TYPE_UNKNOWN(0),
  /**
   * <pre>
   * 私聊
   * </pre>
   *
   * <code>CONVERSATION_TYPE_PRIVATE = 1;</code>
   */
  CONVERSATION_TYPE_PRIVATE(1),
  /**
   * <pre>
   * 群聊
   * </pre>
   *
   * <code>CONVERSATION_TYPE_GROUP = 2;</code>
   */
  CONVERSATION_TYPE_GROUP(2),
  /**
   * <pre>
   * 频道
   * </pre>
   *
   * <code>CONVERSATION_TYPE_CHANNEL = 3;</code>
   */
  CONVERSATION_TYPE_CHANNEL(3),
  /**
   * <pre>
   * 系统会话
   * </pre>
   *
   * <code>CONVERSATION_TYPE_SYSTEM = 4;</code>
   */
  CONVERSATION_TYPE_SYSTEM(4),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>CONVERSATION_TYPE_UNKNOWN = 0;</code>
   */
  public static final int CONVERSATION_TYPE_UNKNOWN_VALUE = 0;
  /**
   * <pre>
   * 私聊
   * </pre>
   *
   * <code>CONVERSATION_TYPE_PRIVATE = 1;</code>
   */
  public static final int CONVERSATION_TYPE_PRIVATE_VALUE = 1;
  /**
   * <pre>
   * 群聊
   * </pre>
   *
   * <code>CONVERSATION_TYPE_GROUP = 2;</code>
   */
  public static final int CONVERSATION_TYPE_GROUP_VALUE = 2;
  /**
   * <pre>
   * 频道
   * </pre>
   *
   * <code>CONVERSATION_TYPE_CHANNEL = 3;</code>
   */
  public static final int CONVERSATION_TYPE_CHANNEL_VALUE = 3;
  /**
   * <pre>
   * 系统会话
   * </pre>
   *
   * <code>CONVERSATION_TYPE_SYSTEM = 4;</code>
   */
  public static final int CONVERSATION_TYPE_SYSTEM_VALUE = 4;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ConversationType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ConversationType forNumber(int value) {
    switch (value) {
      case 0: return CONVERSATION_TYPE_UNKNOWN;
      case 1: return CONVERSATION_TYPE_PRIVATE;
      case 2: return CONVERSATION_TYPE_GROUP;
      case 3: return CONVERSATION_TYPE_CHANNEL;
      case 4: return CONVERSATION_TYPE_SYSTEM;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ConversationType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ConversationType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ConversationType>() {
          public ConversationType findValueByNumber(int number) {
            return ConversationType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageServiceProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final ConversationType[] VALUES = values();

  public static ConversationType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ConversationType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.zlim.message.ConversationType)
}

