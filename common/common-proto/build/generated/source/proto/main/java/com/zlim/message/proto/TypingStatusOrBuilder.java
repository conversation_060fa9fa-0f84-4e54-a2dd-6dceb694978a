// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface TypingStatusOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.TypingStatus)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string conversation_id = 1;</code>
   * @return The conversationId.
   */
  java.lang.String getConversationId();
  /**
   * <code>string conversation_id = 1;</code>
   * @return The bytes for conversationId.
   */
  com.google.protobuf.ByteString
      getConversationIdBytes();

  /**
   * <code>int64 user_id = 2;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>bool typing = 3;</code>
   * @return The typing.
   */
  boolean getTyping();
}
