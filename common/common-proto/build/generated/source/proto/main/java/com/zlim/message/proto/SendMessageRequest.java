// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 发送消息请求
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.SendMessageRequest}
 */
public final class SendMessageRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.SendMessageRequest)
    SendMessageRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SendMessageRequest.newBuilder() to construct.
  private SendMessageRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SendMessageRequest() {
    conversationId_ = "";
    type_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SendMessageRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_SendMessageRequest_descriptor;
  }

  @SuppressWarnings({"rawtypes"})
  @java.lang.Override
  protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
      int number) {
    switch (number) {
      case 5:
        return internalGetExtra();
      default:
        throw new RuntimeException(
            "Invalid map field number: " + number);
    }
  }
  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_SendMessageRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.SendMessageRequest.class, com.zlim.message.proto.SendMessageRequest.Builder.class);
  }

  private int bitField0_;
  public static final int CONVERSATION_ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object conversationId_ = "";
  /**
   * <code>string conversation_id = 1;</code>
   * @return The conversationId.
   */
  @java.lang.Override
  public java.lang.String getConversationId() {
    java.lang.Object ref = conversationId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      conversationId_ = s;
      return s;
    }
  }
  /**
   * <code>string conversation_id = 1;</code>
   * @return The bytes for conversationId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConversationIdBytes() {
    java.lang.Object ref = conversationId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      conversationId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SENDER_ID_FIELD_NUMBER = 2;
  private long senderId_ = 0L;
  /**
   * <code>int64 sender_id = 2;</code>
   * @return The senderId.
   */
  @java.lang.Override
  public long getSenderId() {
    return senderId_;
  }

  public static final int TYPE_FIELD_NUMBER = 3;
  private int type_ = 0;
  /**
   * <code>.com.zlim.message.MessageType type = 3;</code>
   * @return The enum numeric value on the wire for type.
   */
  @java.lang.Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.zlim.message.MessageType type = 3;</code>
   * @return The type.
   */
  @java.lang.Override public com.zlim.message.proto.MessageType getType() {
    com.zlim.message.proto.MessageType result = com.zlim.message.proto.MessageType.forNumber(type_);
    return result == null ? com.zlim.message.proto.MessageType.UNRECOGNIZED : result;
  }

  public static final int CONTENT_FIELD_NUMBER = 4;
  private com.zlim.message.proto.MessageContent content_;
  /**
   * <code>.com.zlim.message.MessageContent content = 4;</code>
   * @return Whether the content field is set.
   */
  @java.lang.Override
  public boolean hasContent() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.zlim.message.MessageContent content = 4;</code>
   * @return The content.
   */
  @java.lang.Override
  public com.zlim.message.proto.MessageContent getContent() {
    return content_ == null ? com.zlim.message.proto.MessageContent.getDefaultInstance() : content_;
  }
  /**
   * <code>.com.zlim.message.MessageContent content = 4;</code>
   */
  @java.lang.Override
  public com.zlim.message.proto.MessageContentOrBuilder getContentOrBuilder() {
    return content_ == null ? com.zlim.message.proto.MessageContent.getDefaultInstance() : content_;
  }

  public static final int EXTRA_FIELD_NUMBER = 5;
  private static final class ExtraDefaultEntryHolder {
    static final com.google.protobuf.MapEntry<
        java.lang.String, java.lang.String> defaultEntry =
            com.google.protobuf.MapEntry
            .<java.lang.String, java.lang.String>newDefaultInstance(
                com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_SendMessageRequest_ExtraEntry_descriptor, 
                com.google.protobuf.WireFormat.FieldType.STRING,
                "",
                com.google.protobuf.WireFormat.FieldType.STRING,
                "");
  }
  @SuppressWarnings("serial")
  private com.google.protobuf.MapField<
      java.lang.String, java.lang.String> extra_;
  private com.google.protobuf.MapField<java.lang.String, java.lang.String>
  internalGetExtra() {
    if (extra_ == null) {
      return com.google.protobuf.MapField.emptyMapField(
          ExtraDefaultEntryHolder.defaultEntry);
    }
    return extra_;
  }
  public int getExtraCount() {
    return internalGetExtra().getMap().size();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public boolean containsExtra(
      java.lang.String key) {
    if (key == null) { throw new NullPointerException("map key"); }
    return internalGetExtra().getMap().containsKey(key);
  }
  /**
   * Use {@link #getExtraMap()} instead.
   */
  @java.lang.Override
  @java.lang.Deprecated
  public java.util.Map<java.lang.String, java.lang.String> getExtra() {
    return getExtraMap();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public java.util.Map<java.lang.String, java.lang.String> getExtraMap() {
    return internalGetExtra().getMap();
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public /* nullable */
java.lang.String getExtraOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue) {
    if (key == null) { throw new NullPointerException("map key"); }
    java.util.Map<java.lang.String, java.lang.String> map =
        internalGetExtra().getMap();
    return map.containsKey(key) ? map.get(key) : defaultValue;
  }
  /**
   * <code>map&lt;string, string&gt; extra = 5;</code>
   */
  @java.lang.Override
  public java.lang.String getExtraOrThrow(
      java.lang.String key) {
    if (key == null) { throw new NullPointerException("map key"); }
    java.util.Map<java.lang.String, java.lang.String> map =
        internalGetExtra().getMap();
    if (!map.containsKey(key)) {
      throw new java.lang.IllegalArgumentException();
    }
    return map.get(key);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conversationId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, conversationId_);
    }
    if (senderId_ != 0L) {
      output.writeInt64(2, senderId_);
    }
    if (type_ != com.zlim.message.proto.MessageType.MESSAGE_TYPE_UNKNOWN.getNumber()) {
      output.writeEnum(3, type_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(4, getContent());
    }
    com.google.protobuf.GeneratedMessageV3
      .serializeStringMapTo(
        output,
        internalGetExtra(),
        ExtraDefaultEntryHolder.defaultEntry,
        5);
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(conversationId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, conversationId_);
    }
    if (senderId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, senderId_);
    }
    if (type_ != com.zlim.message.proto.MessageType.MESSAGE_TYPE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, type_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getContent());
    }
    for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
         : internalGetExtra().getMap().entrySet()) {
      com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
      extra__ = ExtraDefaultEntryHolder.defaultEntry.newBuilderForType()
          .setKey(entry.getKey())
          .setValue(entry.getValue())
          .build();
      size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, extra__);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.SendMessageRequest)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.SendMessageRequest other = (com.zlim.message.proto.SendMessageRequest) obj;

    if (!getConversationId()
        .equals(other.getConversationId())) return false;
    if (getSenderId()
        != other.getSenderId()) return false;
    if (type_ != other.type_) return false;
    if (hasContent() != other.hasContent()) return false;
    if (hasContent()) {
      if (!getContent()
          .equals(other.getContent())) return false;
    }
    if (!internalGetExtra().equals(
        other.internalGetExtra())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CONVERSATION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getConversationId().hashCode();
    hash = (37 * hash) + SENDER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSenderId());
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    if (hasContent()) {
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
    }
    if (!internalGetExtra().getMap().isEmpty()) {
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + internalGetExtra().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.SendMessageRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.SendMessageRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.SendMessageRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.SendMessageRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 发送消息请求
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.SendMessageRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.SendMessageRequest)
      com.zlim.message.proto.SendMessageRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_SendMessageRequest_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
        int number) {
      switch (number) {
        case 5:
          return internalGetExtra();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
        int number) {
      switch (number) {
        case 5:
          return internalGetMutableExtra();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_SendMessageRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.SendMessageRequest.class, com.zlim.message.proto.SendMessageRequest.Builder.class);
    }

    // Construct using com.zlim.message.proto.SendMessageRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getContentFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      conversationId_ = "";
      senderId_ = 0L;
      type_ = 0;
      content_ = null;
      if (contentBuilder_ != null) {
        contentBuilder_.dispose();
        contentBuilder_ = null;
      }
      internalGetMutableExtra().clear();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_SendMessageRequest_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.SendMessageRequest getDefaultInstanceForType() {
      return com.zlim.message.proto.SendMessageRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.SendMessageRequest build() {
      com.zlim.message.proto.SendMessageRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.SendMessageRequest buildPartial() {
      com.zlim.message.proto.SendMessageRequest result = new com.zlim.message.proto.SendMessageRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.message.proto.SendMessageRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.conversationId_ = conversationId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.senderId_ = senderId_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.type_ = type_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.content_ = contentBuilder_ == null
            ? content_
            : contentBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.extra_ = internalGetExtra();
        result.extra_.makeImmutable();
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.SendMessageRequest) {
        return mergeFrom((com.zlim.message.proto.SendMessageRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.SendMessageRequest other) {
      if (other == com.zlim.message.proto.SendMessageRequest.getDefaultInstance()) return this;
      if (!other.getConversationId().isEmpty()) {
        conversationId_ = other.conversationId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.getSenderId() != 0L) {
        setSenderId(other.getSenderId());
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (other.hasContent()) {
        mergeContent(other.getContent());
      }
      internalGetMutableExtra().mergeFrom(
          other.internalGetExtra());
      bitField0_ |= 0x00000010;
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              conversationId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              senderId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              type_ = input.readEnum();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              input.readMessage(
                  getContentFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extra__ = input.readMessage(
                  ExtraDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              internalGetMutableExtra().getMutableMap().put(
                  extra__.getKey(), extra__.getValue());
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object conversationId_ = "";
    /**
     * <code>string conversation_id = 1;</code>
     * @return The conversationId.
     */
    public java.lang.String getConversationId() {
      java.lang.Object ref = conversationId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        conversationId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @return The bytes for conversationId.
     */
    public com.google.protobuf.ByteString
        getConversationIdBytes() {
      java.lang.Object ref = conversationId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        conversationId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @param value The conversationId to set.
     * @return This builder for chaining.
     */
    public Builder setConversationId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      conversationId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConversationId() {
      conversationId_ = getDefaultInstance().getConversationId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string conversation_id = 1;</code>
     * @param value The bytes for conversationId to set.
     * @return This builder for chaining.
     */
    public Builder setConversationIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      conversationId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private long senderId_ ;
    /**
     * <code>int64 sender_id = 2;</code>
     * @return The senderId.
     */
    @java.lang.Override
    public long getSenderId() {
      return senderId_;
    }
    /**
     * <code>int64 sender_id = 2;</code>
     * @param value The senderId to set.
     * @return This builder for chaining.
     */
    public Builder setSenderId(long value) {

      senderId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int64 sender_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSenderId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      senderId_ = 0L;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.zlim.message.MessageType type = 3;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.zlim.message.MessageType type = 3;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      type_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.MessageType type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public com.zlim.message.proto.MessageType getType() {
      com.zlim.message.proto.MessageType result = com.zlim.message.proto.MessageType.forNumber(type_);
      return result == null ? com.zlim.message.proto.MessageType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.zlim.message.MessageType type = 3;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(com.zlim.message.proto.MessageType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000004;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.MessageType type = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000004);
      type_ = 0;
      onChanged();
      return this;
    }

    private com.zlim.message.proto.MessageContent content_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.MessageContent, com.zlim.message.proto.MessageContent.Builder, com.zlim.message.proto.MessageContentOrBuilder> contentBuilder_;
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     * @return Whether the content field is set.
     */
    public boolean hasContent() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     * @return The content.
     */
    public com.zlim.message.proto.MessageContent getContent() {
      if (contentBuilder_ == null) {
        return content_ == null ? com.zlim.message.proto.MessageContent.getDefaultInstance() : content_;
      } else {
        return contentBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    public Builder setContent(com.zlim.message.proto.MessageContent value) {
      if (contentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        content_ = value;
      } else {
        contentBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    public Builder setContent(
        com.zlim.message.proto.MessageContent.Builder builderForValue) {
      if (contentBuilder_ == null) {
        content_ = builderForValue.build();
      } else {
        contentBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    public Builder mergeContent(com.zlim.message.proto.MessageContent value) {
      if (contentBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          content_ != null &&
          content_ != com.zlim.message.proto.MessageContent.getDefaultInstance()) {
          getContentBuilder().mergeFrom(value);
        } else {
          content_ = value;
        }
      } else {
        contentBuilder_.mergeFrom(value);
      }
      if (content_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    public Builder clearContent() {
      bitField0_ = (bitField0_ & ~0x00000008);
      content_ = null;
      if (contentBuilder_ != null) {
        contentBuilder_.dispose();
        contentBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    public com.zlim.message.proto.MessageContent.Builder getContentBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return getContentFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    public com.zlim.message.proto.MessageContentOrBuilder getContentOrBuilder() {
      if (contentBuilder_ != null) {
        return contentBuilder_.getMessageOrBuilder();
      } else {
        return content_ == null ?
            com.zlim.message.proto.MessageContent.getDefaultInstance() : content_;
      }
    }
    /**
     * <code>.com.zlim.message.MessageContent content = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.zlim.message.proto.MessageContent, com.zlim.message.proto.MessageContent.Builder, com.zlim.message.proto.MessageContentOrBuilder> 
        getContentFieldBuilder() {
      if (contentBuilder_ == null) {
        contentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.zlim.message.proto.MessageContent, com.zlim.message.proto.MessageContent.Builder, com.zlim.message.proto.MessageContentOrBuilder>(
                getContent(),
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      return contentBuilder_;
    }

    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extra_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetExtra() {
      if (extra_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtraDefaultEntryHolder.defaultEntry);
      }
      return extra_;
    }
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetMutableExtra() {
      if (extra_ == null) {
        extra_ = com.google.protobuf.MapField.newMapField(
            ExtraDefaultEntryHolder.defaultEntry);
      }
      if (!extra_.isMutable()) {
        extra_ = extra_.copy();
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return extra_;
    }
    public int getExtraCount() {
      return internalGetExtra().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public boolean containsExtra(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetExtra().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtraMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtra() {
      return getExtraMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public java.util.Map<java.lang.String, java.lang.String> getExtraMap() {
      return internalGetExtra().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public /* nullable */
java.lang.String getExtraOrDefault(
        java.lang.String key,
        /* nullable */
java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtra().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    @java.lang.Override
    public java.lang.String getExtraOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtra().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }
    public Builder clearExtra() {
      bitField0_ = (bitField0_ & ~0x00000010);
      internalGetMutableExtra().getMutableMap()
          .clear();
      return this;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    public Builder removeExtra(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      internalGetMutableExtra().getMutableMap()
          .remove(key);
      return this;
    }
    /**
     * Use alternate mutation accessors instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String>
        getMutableExtra() {
      bitField0_ |= 0x00000010;
      return internalGetMutableExtra().getMutableMap();
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    public Builder putExtra(
        java.lang.String key,
        java.lang.String value) {
      if (key == null) { throw new NullPointerException("map key"); }
      if (value == null) { throw new NullPointerException("map value"); }
      internalGetMutableExtra().getMutableMap()
          .put(key, value);
      bitField0_ |= 0x00000010;
      return this;
    }
    /**
     * <code>map&lt;string, string&gt; extra = 5;</code>
     */
    public Builder putAllExtra(
        java.util.Map<java.lang.String, java.lang.String> values) {
      internalGetMutableExtra().getMutableMap()
          .putAll(values);
      bitField0_ |= 0x00000010;
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.SendMessageRequest)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.SendMessageRequest)
  private static final com.zlim.message.proto.SendMessageRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.SendMessageRequest();
  }

  public static com.zlim.message.proto.SendMessageRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SendMessageRequest>
      PARSER = new com.google.protobuf.AbstractParser<SendMessageRequest>() {
    @java.lang.Override
    public SendMessageRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SendMessageRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SendMessageRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.SendMessageRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

