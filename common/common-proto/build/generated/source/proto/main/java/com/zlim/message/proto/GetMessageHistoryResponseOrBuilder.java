// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface GetMessageHistoryResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.GetMessageHistoryResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  java.util.List<com.zlim.message.proto.Message> 
      getMessagesList();
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  com.zlim.message.proto.Message getMessages(int index);
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  int getMessagesCount();
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  java.util.List<? extends com.zlim.message.proto.MessageOrBuilder> 
      getMessagesOrBuilderList();
  /**
   * <code>repeated .com.zlim.message.Message messages = 1;</code>
   */
  com.zlim.message.proto.MessageOrBuilder getMessagesOrBuilder(
      int index);

  /**
   * <code>string next_cursor = 2;</code>
   * @return The nextCursor.
   */
  java.lang.String getNextCursor();
  /**
   * <code>string next_cursor = 2;</code>
   * @return The bytes for nextCursor.
   */
  com.google.protobuf.ByteString
      getNextCursorBytes();

  /**
   * <code>bool has_more = 3;</code>
   * @return The hasMore.
   */
  boolean getHasMore();
}
