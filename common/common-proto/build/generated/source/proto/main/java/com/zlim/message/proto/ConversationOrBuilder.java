// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface ConversationOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.Conversation)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string conversation_id = 1;</code>
   * @return The conversationId.
   */
  java.lang.String getConversationId();
  /**
   * <code>string conversation_id = 1;</code>
   * @return The bytes for conversationId.
   */
  com.google.protobuf.ByteString
      getConversationIdBytes();

  /**
   * <code>.com.zlim.message.ConversationType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.zlim.message.ConversationType type = 2;</code>
   * @return The type.
   */
  com.zlim.message.proto.ConversationType getType();

  /**
   * <code>string title = 3;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <code>string title = 3;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <code>string avatar = 4;</code>
   * @return The avatar.
   */
  java.lang.String getAvatar();
  /**
   * <code>string avatar = 4;</code>
   * @return The bytes for avatar.
   */
  com.google.protobuf.ByteString
      getAvatarBytes();

  /**
   * <code>.com.zlim.message.Message last_message = 5;</code>
   * @return Whether the lastMessage field is set.
   */
  boolean hasLastMessage();
  /**
   * <code>.com.zlim.message.Message last_message = 5;</code>
   * @return The lastMessage.
   */
  com.zlim.message.proto.Message getLastMessage();
  /**
   * <code>.com.zlim.message.Message last_message = 5;</code>
   */
  com.zlim.message.proto.MessageOrBuilder getLastMessageOrBuilder();

  /**
   * <code>int32 unread_count = 6;</code>
   * @return The unreadCount.
   */
  int getUnreadCount();

  /**
   * <code>bool muted = 7;</code>
   * @return The muted.
   */
  boolean getMuted();

  /**
   * <code>bool pinned = 8;</code>
   * @return The pinned.
   */
  boolean getPinned();

  /**
   * <code>int64 updated_at = 9;</code>
   * @return The updatedAt.
   */
  long getUpdatedAt();

  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  int getExtraCount();
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  boolean containsExtra(
      java.lang.String key);
  /**
   * Use {@link #getExtraMap()} instead.
   */
  @java.lang.Deprecated
  java.util.Map<java.lang.String, java.lang.String>
  getExtra();
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  java.util.Map<java.lang.String, java.lang.String>
  getExtraMap();
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  /* nullable */
java.lang.String getExtraOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue);
  /**
   * <code>map&lt;string, string&gt; extra = 10;</code>
   */
  java.lang.String getExtraOrThrow(
      java.lang.String key);
}
