// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface LoginResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.LoginResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>string access_token = 3;</code>
   * @return The accessToken.
   */
  java.lang.String getAccessToken();
  /**
   * <code>string access_token = 3;</code>
   * @return The bytes for accessToken.
   */
  com.google.protobuf.ByteString
      getAccessTokenBytes();

  /**
   * <code>string refresh_token = 4;</code>
   * @return The refreshToken.
   */
  java.lang.String getRefreshToken();
  /**
   * <code>string refresh_token = 4;</code>
   * @return The bytes for refreshToken.
   */
  com.google.protobuf.ByteString
      getRefreshTokenBytes();

  /**
   * <code>int64 expires_in = 5;</code>
   * @return The expiresIn.
   */
  long getExpiresIn();

  /**
   * <code>.com.zlim.common.UserInfo user_info = 6;</code>
   * @return Whether the userInfo field is set.
   */
  boolean hasUserInfo();
  /**
   * <code>.com.zlim.common.UserInfo user_info = 6;</code>
   * @return The userInfo.
   */
  com.zlim.common.proto.UserInfo getUserInfo();
  /**
   * <code>.com.zlim.common.UserInfo user_info = 6;</code>
   */
  com.zlim.common.proto.UserInfoOrBuilder getUserInfoOrBuilder();

  /**
   * <pre>
   * 错误码，客户端可用此进行国际化
   * </pre>
   *
   * <code>int32 error_code = 7;</code>
   * @return The errorCode.
   */
  int getErrorCode();
}
