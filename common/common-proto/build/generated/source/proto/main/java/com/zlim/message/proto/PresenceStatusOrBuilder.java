// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface PresenceStatusOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.PresenceStatus)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>.com.zlim.message.PresenceType status = 2;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.zlim.message.PresenceType status = 2;</code>
   * @return The status.
   */
  com.zlim.message.proto.PresenceType getStatus();

  /**
   * <code>int64 last_seen = 3;</code>
   * @return The lastSeen.
   */
  long getLastSeen();
}
