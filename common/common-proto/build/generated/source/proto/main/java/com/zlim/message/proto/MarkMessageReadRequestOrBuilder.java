// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface MarkMessageReadRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.MarkMessageReadRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string conversation_id = 1;</code>
   * @return The conversationId.
   */
  java.lang.String getConversationId();
  /**
   * <code>string conversation_id = 1;</code>
   * @return The bytes for conversationId.
   */
  com.google.protobuf.ByteString
      getConversationIdBytes();

  /**
   * <code>int64 user_id = 2;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <pre>
   * 最后一条已读消息ID
   * </pre>
   *
   * <code>string message_id = 3;</code>
   * @return The messageId.
   */
  java.lang.String getMessageId();
  /**
   * <pre>
   * 最后一条已读消息ID
   * </pre>
   *
   * <code>string message_id = 3;</code>
   * @return The bytes for messageId.
   */
  com.google.protobuf.ByteString
      getMessageIdBytes();
}
