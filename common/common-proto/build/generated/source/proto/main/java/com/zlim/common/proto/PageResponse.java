// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

/**
 * <pre>
 * 分页响应
 * </pre>
 *
 * Protobuf type {@code com.zlim.common.PageResponse}
 */
public final class PageResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.common.PageResponse)
    PageResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PageResponse.newBuilder() to construct.
  private PageResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PageResponse() {
    content_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PageResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_PageResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_PageResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.common.proto.PageResponse.class, com.zlim.common.proto.PageResponse.Builder.class);
  }

  public static final int TOTAL_FIELD_NUMBER = 1;
  private long total_ = 0L;
  /**
   * <code>int64 total = 1;</code>
   * @return The total.
   */
  @java.lang.Override
  public long getTotal() {
    return total_;
  }

  public static final int PAGE_FIELD_NUMBER = 2;
  private int page_ = 0;
  /**
   * <code>int32 page = 2;</code>
   * @return The page.
   */
  @java.lang.Override
  public int getPage() {
    return page_;
  }

  public static final int SIZE_FIELD_NUMBER = 3;
  private int size_ = 0;
  /**
   * <code>int32 size = 3;</code>
   * @return The size.
   */
  @java.lang.Override
  public int getSize() {
    return size_;
  }

  public static final int CONTENT_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<com.google.protobuf.Any> content_;
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  @java.lang.Override
  public java.util.List<com.google.protobuf.Any> getContentList() {
    return content_;
  }
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
      getContentOrBuilderList() {
    return content_;
  }
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  @java.lang.Override
  public int getContentCount() {
    return content_.size();
  }
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  @java.lang.Override
  public com.google.protobuf.Any getContent(int index) {
    return content_.get(index);
  }
  /**
   * <code>repeated .google.protobuf.Any content = 4;</code>
   */
  @java.lang.Override
  public com.google.protobuf.AnyOrBuilder getContentOrBuilder(
      int index) {
    return content_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (total_ != 0L) {
      output.writeInt64(1, total_);
    }
    if (page_ != 0) {
      output.writeInt32(2, page_);
    }
    if (size_ != 0) {
      output.writeInt32(3, size_);
    }
    for (int i = 0; i < content_.size(); i++) {
      output.writeMessage(4, content_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (total_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, total_);
    }
    if (page_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, page_);
    }
    if (size_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, size_);
    }
    for (int i = 0; i < content_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, content_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.common.proto.PageResponse)) {
      return super.equals(obj);
    }
    com.zlim.common.proto.PageResponse other = (com.zlim.common.proto.PageResponse) obj;

    if (getTotal()
        != other.getTotal()) return false;
    if (getPage()
        != other.getPage()) return false;
    if (getSize()
        != other.getSize()) return false;
    if (!getContentList()
        .equals(other.getContentList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TOTAL_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTotal());
    hash = (37 * hash) + PAGE_FIELD_NUMBER;
    hash = (53 * hash) + getPage();
    hash = (37 * hash) + SIZE_FIELD_NUMBER;
    hash = (53 * hash) + getSize();
    if (getContentCount() > 0) {
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContentList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.common.proto.PageResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.common.proto.PageResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.common.proto.PageResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.common.proto.PageResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.common.proto.PageResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 分页响应
   * </pre>
   *
   * Protobuf type {@code com.zlim.common.PageResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.common.PageResponse)
      com.zlim.common.proto.PageResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_PageResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_PageResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.common.proto.PageResponse.class, com.zlim.common.proto.PageResponse.Builder.class);
    }

    // Construct using com.zlim.common.proto.PageResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      total_ = 0L;
      page_ = 0;
      size_ = 0;
      if (contentBuilder_ == null) {
        content_ = java.util.Collections.emptyList();
      } else {
        content_ = null;
        contentBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.common.proto.CommonProto.internal_static_com_zlim_common_PageResponse_descriptor;
    }

    @java.lang.Override
    public com.zlim.common.proto.PageResponse getDefaultInstanceForType() {
      return com.zlim.common.proto.PageResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.common.proto.PageResponse build() {
      com.zlim.common.proto.PageResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.common.proto.PageResponse buildPartial() {
      com.zlim.common.proto.PageResponse result = new com.zlim.common.proto.PageResponse(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.zlim.common.proto.PageResponse result) {
      if (contentBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          content_ = java.util.Collections.unmodifiableList(content_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.content_ = content_;
      } else {
        result.content_ = contentBuilder_.build();
      }
    }

    private void buildPartial0(com.zlim.common.proto.PageResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.total_ = total_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.page_ = page_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.size_ = size_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.common.proto.PageResponse) {
        return mergeFrom((com.zlim.common.proto.PageResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.common.proto.PageResponse other) {
      if (other == com.zlim.common.proto.PageResponse.getDefaultInstance()) return this;
      if (other.getTotal() != 0L) {
        setTotal(other.getTotal());
      }
      if (other.getPage() != 0) {
        setPage(other.getPage());
      }
      if (other.getSize() != 0) {
        setSize(other.getSize());
      }
      if (contentBuilder_ == null) {
        if (!other.content_.isEmpty()) {
          if (content_.isEmpty()) {
            content_ = other.content_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureContentIsMutable();
            content_.addAll(other.content_);
          }
          onChanged();
        }
      } else {
        if (!other.content_.isEmpty()) {
          if (contentBuilder_.isEmpty()) {
            contentBuilder_.dispose();
            contentBuilder_ = null;
            content_ = other.content_;
            bitField0_ = (bitField0_ & ~0x00000008);
            contentBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getContentFieldBuilder() : null;
          } else {
            contentBuilder_.addAllMessages(other.content_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              total_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              page_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              size_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              com.google.protobuf.Any m =
                  input.readMessage(
                      com.google.protobuf.Any.parser(),
                      extensionRegistry);
              if (contentBuilder_ == null) {
                ensureContentIsMutable();
                content_.add(m);
              } else {
                contentBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long total_ ;
    /**
     * <code>int64 total = 1;</code>
     * @return The total.
     */
    @java.lang.Override
    public long getTotal() {
      return total_;
    }
    /**
     * <code>int64 total = 1;</code>
     * @param value The total to set.
     * @return This builder for chaining.
     */
    public Builder setTotal(long value) {

      total_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int64 total = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotal() {
      bitField0_ = (bitField0_ & ~0x00000001);
      total_ = 0L;
      onChanged();
      return this;
    }

    private int page_ ;
    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }
    /**
     * <code>int32 page = 2;</code>
     * @param value The page to set.
     * @return This builder for chaining.
     */
    public Builder setPage(int value) {

      page_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int32 page = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPage() {
      bitField0_ = (bitField0_ & ~0x00000002);
      page_ = 0;
      onChanged();
      return this;
    }

    private int size_ ;
    /**
     * <code>int32 size = 3;</code>
     * @return The size.
     */
    @java.lang.Override
    public int getSize() {
      return size_;
    }
    /**
     * <code>int32 size = 3;</code>
     * @param value The size to set.
     * @return This builder for chaining.
     */
    public Builder setSize(int value) {

      size_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>int32 size = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSize() {
      bitField0_ = (bitField0_ & ~0x00000004);
      size_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<com.google.protobuf.Any> content_ =
      java.util.Collections.emptyList();
    private void ensureContentIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        content_ = new java.util.ArrayList<com.google.protobuf.Any>(content_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> contentBuilder_;

    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public java.util.List<com.google.protobuf.Any> getContentList() {
      if (contentBuilder_ == null) {
        return java.util.Collections.unmodifiableList(content_);
      } else {
        return contentBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public int getContentCount() {
      if (contentBuilder_ == null) {
        return content_.size();
      } else {
        return contentBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public com.google.protobuf.Any getContent(int index) {
      if (contentBuilder_ == null) {
        return content_.get(index);
      } else {
        return contentBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder setContent(
        int index, com.google.protobuf.Any value) {
      if (contentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureContentIsMutable();
        content_.set(index, value);
        onChanged();
      } else {
        contentBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder setContent(
        int index, com.google.protobuf.Any.Builder builderForValue) {
      if (contentBuilder_ == null) {
        ensureContentIsMutable();
        content_.set(index, builderForValue.build());
        onChanged();
      } else {
        contentBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder addContent(com.google.protobuf.Any value) {
      if (contentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureContentIsMutable();
        content_.add(value);
        onChanged();
      } else {
        contentBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder addContent(
        int index, com.google.protobuf.Any value) {
      if (contentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureContentIsMutable();
        content_.add(index, value);
        onChanged();
      } else {
        contentBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder addContent(
        com.google.protobuf.Any.Builder builderForValue) {
      if (contentBuilder_ == null) {
        ensureContentIsMutable();
        content_.add(builderForValue.build());
        onChanged();
      } else {
        contentBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder addContent(
        int index, com.google.protobuf.Any.Builder builderForValue) {
      if (contentBuilder_ == null) {
        ensureContentIsMutable();
        content_.add(index, builderForValue.build());
        onChanged();
      } else {
        contentBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder addAllContent(
        java.lang.Iterable<? extends com.google.protobuf.Any> values) {
      if (contentBuilder_ == null) {
        ensureContentIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, content_);
        onChanged();
      } else {
        contentBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder clearContent() {
      if (contentBuilder_ == null) {
        content_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        contentBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public Builder removeContent(int index) {
      if (contentBuilder_ == null) {
        ensureContentIsMutable();
        content_.remove(index);
        onChanged();
      } else {
        contentBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public com.google.protobuf.Any.Builder getContentBuilder(
        int index) {
      return getContentFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public com.google.protobuf.AnyOrBuilder getContentOrBuilder(
        int index) {
      if (contentBuilder_ == null) {
        return content_.get(index);  } else {
        return contentBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public java.util.List<? extends com.google.protobuf.AnyOrBuilder> 
         getContentOrBuilderList() {
      if (contentBuilder_ != null) {
        return contentBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(content_);
      }
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public com.google.protobuf.Any.Builder addContentBuilder() {
      return getContentFieldBuilder().addBuilder(
          com.google.protobuf.Any.getDefaultInstance());
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public com.google.protobuf.Any.Builder addContentBuilder(
        int index) {
      return getContentFieldBuilder().addBuilder(
          index, com.google.protobuf.Any.getDefaultInstance());
    }
    /**
     * <code>repeated .google.protobuf.Any content = 4;</code>
     */
    public java.util.List<com.google.protobuf.Any.Builder> 
         getContentBuilderList() {
      return getContentFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder> 
        getContentFieldBuilder() {
      if (contentBuilder_ == null) {
        contentBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.google.protobuf.Any, com.google.protobuf.Any.Builder, com.google.protobuf.AnyOrBuilder>(
                content_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      return contentBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.common.PageResponse)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.common.PageResponse)
  private static final com.zlim.common.proto.PageResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.common.proto.PageResponse();
  }

  public static com.zlim.common.proto.PageResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PageResponse>
      PARSER = new com.google.protobuf.AbstractParser<PageResponse>() {
    @java.lang.Override
    public PageResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PageResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PageResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.common.proto.PageResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

