// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface TextContentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.TextContent)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string text = 1;</code>
   * @return The text.
   */
  java.lang.String getText();
  /**
   * <code>string text = 1;</code>
   * @return The bytes for text.
   */
  com.google.protobuf.ByteString
      getTextBytes();

  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  java.util.List<com.zlim.message.proto.MentionInfo> 
      getMentionsList();
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  com.zlim.message.proto.MentionInfo getMentions(int index);
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  int getMentionsCount();
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  java.util.List<? extends com.zlim.message.proto.MentionInfoOrBuilder> 
      getMentionsOrBuilderList();
  /**
   * <code>repeated .com.zlim.message.MentionInfo mentions = 2;</code>
   */
  com.zlim.message.proto.MentionInfoOrBuilder getMentionsOrBuilder(
      int index);
}
