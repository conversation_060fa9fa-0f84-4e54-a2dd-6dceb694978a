// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface GetUserInfoResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.GetUserInfoResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.com.zlim.common.UserInfo user_info = 1;</code>
   * @return Whether the userInfo field is set.
   */
  boolean hasUserInfo();
  /**
   * <code>.com.zlim.common.UserInfo user_info = 1;</code>
   * @return The userInfo.
   */
  com.zlim.common.proto.UserInfo getUserInfo();
  /**
   * <code>.com.zlim.common.UserInfo user_info = 1;</code>
   */
  com.zlim.common.proto.UserInfoOrBuilder getUserInfoOrBuilder();
}
