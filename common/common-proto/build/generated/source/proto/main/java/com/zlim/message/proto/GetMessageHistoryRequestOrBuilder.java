// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface GetMessageHistoryRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.GetMessageHistoryRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string conversation_id = 1;</code>
   * @return The conversationId.
   */
  java.lang.String getConversationId();
  /**
   * <code>string conversation_id = 1;</code>
   * @return The bytes for conversationId.
   */
  com.google.protobuf.ByteString
      getConversationIdBytes();

  /**
   * <code>int64 user_id = 2;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <pre>
   * 分页游标
   * </pre>
   *
   * <code>string cursor = 3;</code>
   * @return The cursor.
   */
  java.lang.String getCursor();
  /**
   * <pre>
   * 分页游标
   * </pre>
   *
   * <code>string cursor = 3;</code>
   * @return The bytes for cursor.
   */
  com.google.protobuf.ByteString
      getCursorBytes();

  /**
   * <code>int32 limit = 4;</code>
   * @return The limit.
   */
  int getLimit();

  /**
   * <pre>
   * 是否倒序
   * </pre>
   *
   * <code>bool reverse = 5;</code>
   * @return The reverse.
   */
  boolean getReverse();
}
