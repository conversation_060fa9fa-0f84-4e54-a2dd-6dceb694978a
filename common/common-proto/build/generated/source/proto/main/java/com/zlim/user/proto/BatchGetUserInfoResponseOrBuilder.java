// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface BatchGetUserInfoResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.BatchGetUserInfoResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  java.util.List<com.zlim.common.proto.UserInfo> 
      getUserInfosList();
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  com.zlim.common.proto.UserInfo getUserInfos(int index);
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  int getUserInfosCount();
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  java.util.List<? extends com.zlim.common.proto.UserInfoOrBuilder> 
      getUserInfosOrBuilderList();
  /**
   * <code>repeated .com.zlim.common.UserInfo user_infos = 1;</code>
   */
  com.zlim.common.proto.UserInfoOrBuilder getUserInfosOrBuilder(
      int index);
}
