// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

public interface ResultOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.common.Result)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 code = 1;</code>
   * @return The code.
   */
  int getCode();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>.google.protobuf.Any data = 3;</code>
   * @return Whether the data field is set.
   */
  boolean hasData();
  /**
   * <code>.google.protobuf.Any data = 3;</code>
   * @return The data.
   */
  com.google.protobuf.Any getData();
  /**
   * <code>.google.protobuf.Any data = 3;</code>
   */
  com.google.protobuf.AnyOrBuilder getDataOrBuilder();

  /**
   * <code>int64 timestamp = 4;</code>
   * @return The timestamp.
   */
  long getTimestamp();
}
