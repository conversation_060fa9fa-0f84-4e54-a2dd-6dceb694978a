// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

public interface UserInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.common.UserInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>string username = 2;</code>
   * @return The username.
   */
  java.lang.String getUsername();
  /**
   * <code>string username = 2;</code>
   * @return The bytes for username.
   */
  com.google.protobuf.ByteString
      getUsernameBytes();

  /**
   * <code>string nickname = 3;</code>
   * @return The nickname.
   */
  java.lang.String getNickname();
  /**
   * <code>string nickname = 3;</code>
   * @return The bytes for nickname.
   */
  com.google.protobuf.ByteString
      getNicknameBytes();

  /**
   * <code>string avatar = 4;</code>
   * @return The avatar.
   */
  java.lang.String getAvatar();
  /**
   * <code>string avatar = 4;</code>
   * @return The bytes for avatar.
   */
  com.google.protobuf.ByteString
      getAvatarBytes();

  /**
   * <code>string email = 5;</code>
   * @return The email.
   */
  java.lang.String getEmail();
  /**
   * <code>string email = 5;</code>
   * @return The bytes for email.
   */
  com.google.protobuf.ByteString
      getEmailBytes();

  /**
   * <code>string phone = 6;</code>
   * @return The phone.
   */
  java.lang.String getPhone();
  /**
   * <code>string phone = 6;</code>
   * @return The bytes for phone.
   */
  com.google.protobuf.ByteString
      getPhoneBytes();

  /**
   * <code>.com.zlim.common.UserStatus status = 7;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.zlim.common.UserStatus status = 7;</code>
   * @return The status.
   */
  com.zlim.common.proto.UserStatus getStatus();

  /**
   * <code>int64 created_at = 8;</code>
   * @return The createdAt.
   */
  long getCreatedAt();

  /**
   * <code>int64 updated_at = 9;</code>
   * @return The updatedAt.
   */
  long getUpdatedAt();
}
