// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.user.proto;

public interface VerifyTokenResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.user.VerifyTokenResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool valid = 1;</code>
   * @return The valid.
   */
  boolean getValid();

  /**
   * <code>int64 user_id = 2;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>string username = 3;</code>
   * @return The username.
   */
  java.lang.String getUsername();
  /**
   * <code>string username = 3;</code>
   * @return The bytes for username.
   */
  com.google.protobuf.ByteString
      getUsernameBytes();

  /**
   * <code>int64 expires_at = 4;</code>
   * @return The expiresAt.
   */
  long getExpiresAt();
}
