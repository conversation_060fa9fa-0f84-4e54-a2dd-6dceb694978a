// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface FrameOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.Frame)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.com.zlim.message.FrameType type = 1;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.zlim.message.FrameType type = 1;</code>
   * @return The type.
   */
  com.zlim.message.proto.FrameType getType();

  /**
   * <code>int64 seq = 2;</code>
   * @return The seq.
   */
  long getSeq();

  /**
   * <code>bytes payload = 3;</code>
   * @return The payload.
   */
  com.google.protobuf.ByteString getPayload();

  /**
   * <code>int64 timestamp = 4;</code>
   * @return The timestamp.
   */
  long getTimestamp();
}
