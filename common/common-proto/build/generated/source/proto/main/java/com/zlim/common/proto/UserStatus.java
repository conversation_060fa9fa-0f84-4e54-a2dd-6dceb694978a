// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

/**
 * <pre>
 * 用户状态枚举
 * </pre>
 *
 * Protobuf enum {@code com.zlim.common.UserStatus}
 */
public enum UserStatus
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>USER_STATUS_UNKNOWN = 0;</code>
   */
  USER_STATUS_UNKNOWN(0),
  /**
   * <code>USER_STATUS_ACTIVE = 1;</code>
   */
  USER_STATUS_ACTIVE(1),
  /**
   * <code>USER_STATUS_INACTIVE = 2;</code>
   */
  USER_STATUS_INACTIVE(2),
  /**
   * <code>USER_STATUS_BANNED = 3;</code>
   */
  USER_STATUS_BANNED(3),
  /**
   * <code>USER_STATUS_DELETED = 4;</code>
   */
  USER_STATUS_DELETED(4),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>USER_STATUS_UNKNOWN = 0;</code>
   */
  public static final int USER_STATUS_UNKNOWN_VALUE = 0;
  /**
   * <code>USER_STATUS_ACTIVE = 1;</code>
   */
  public static final int USER_STATUS_ACTIVE_VALUE = 1;
  /**
   * <code>USER_STATUS_INACTIVE = 2;</code>
   */
  public static final int USER_STATUS_INACTIVE_VALUE = 2;
  /**
   * <code>USER_STATUS_BANNED = 3;</code>
   */
  public static final int USER_STATUS_BANNED_VALUE = 3;
  /**
   * <code>USER_STATUS_DELETED = 4;</code>
   */
  public static final int USER_STATUS_DELETED_VALUE = 4;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static UserStatus valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static UserStatus forNumber(int value) {
    switch (value) {
      case 0: return USER_STATUS_UNKNOWN;
      case 1: return USER_STATUS_ACTIVE;
      case 2: return USER_STATUS_INACTIVE;
      case 3: return USER_STATUS_BANNED;
      case 4: return USER_STATUS_DELETED;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<UserStatus>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      UserStatus> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<UserStatus>() {
          public UserStatus findValueByNumber(int number) {
            return UserStatus.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.zlim.common.proto.CommonProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final UserStatus[] VALUES = values();

  public static UserStatus valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private UserStatus(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.zlim.common.UserStatus)
}

