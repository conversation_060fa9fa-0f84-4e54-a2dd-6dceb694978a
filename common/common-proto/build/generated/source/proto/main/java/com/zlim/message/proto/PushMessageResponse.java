// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

/**
 * <pre>
 * 推送消息响应
 * </pre>
 *
 * Protobuf type {@code com.zlim.message.PushMessageResponse}
 */
public final class PushMessageResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zlim.message.PushMessageResponse)
    PushMessageResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PushMessageResponse.newBuilder() to construct.
  private PushMessageResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PushMessageResponse() {
    gatewayId_ = "";
    failedUserIds_ = emptyLongList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PushMessageResponse();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_PushMessageResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_PushMessageResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zlim.message.proto.PushMessageResponse.class, com.zlim.message.proto.PushMessageResponse.Builder.class);
  }

  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_ = false;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int GATEWAY_ID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object gatewayId_ = "";
  /**
   * <code>string gateway_id = 2;</code>
   * @return The gatewayId.
   */
  @java.lang.Override
  public java.lang.String getGatewayId() {
    java.lang.Object ref = gatewayId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gatewayId_ = s;
      return s;
    }
  }
  /**
   * <code>string gateway_id = 2;</code>
   * @return The bytes for gatewayId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGatewayIdBytes() {
    java.lang.Object ref = gatewayId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gatewayId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVERED_COUNT_FIELD_NUMBER = 3;
  private int deliveredCount_ = 0;
  /**
   * <code>int32 delivered_count = 3;</code>
   * @return The deliveredCount.
   */
  @java.lang.Override
  public int getDeliveredCount() {
    return deliveredCount_;
  }

  public static final int FAILED_USER_IDS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList failedUserIds_ =
      emptyLongList();
  /**
   * <code>repeated int64 failed_user_ids = 4;</code>
   * @return A list containing the failedUserIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getFailedUserIdsList() {
    return failedUserIds_;
  }
  /**
   * <code>repeated int64 failed_user_ids = 4;</code>
   * @return The count of failedUserIds.
   */
  public int getFailedUserIdsCount() {
    return failedUserIds_.size();
  }
  /**
   * <code>repeated int64 failed_user_ids = 4;</code>
   * @param index The index of the element to return.
   * @return The failedUserIds at the given index.
   */
  public long getFailedUserIds(int index) {
    return failedUserIds_.getLong(index);
  }
  private int failedUserIdsMemoizedSerializedSize = -1;

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gatewayId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, gatewayId_);
    }
    if (deliveredCount_ != 0) {
      output.writeInt32(3, deliveredCount_);
    }
    if (getFailedUserIdsList().size() > 0) {
      output.writeUInt32NoTag(34);
      output.writeUInt32NoTag(failedUserIdsMemoizedSerializedSize);
    }
    for (int i = 0; i < failedUserIds_.size(); i++) {
      output.writeInt64NoTag(failedUserIds_.getLong(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gatewayId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, gatewayId_);
    }
    if (deliveredCount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, deliveredCount_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < failedUserIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(failedUserIds_.getLong(i));
      }
      size += dataSize;
      if (!getFailedUserIdsList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      failedUserIdsMemoizedSerializedSize = dataSize;
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zlim.message.proto.PushMessageResponse)) {
      return super.equals(obj);
    }
    com.zlim.message.proto.PushMessageResponse other = (com.zlim.message.proto.PushMessageResponse) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getGatewayId()
        .equals(other.getGatewayId())) return false;
    if (getDeliveredCount()
        != other.getDeliveredCount()) return false;
    if (!getFailedUserIdsList()
        .equals(other.getFailedUserIdsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + GATEWAY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getGatewayId().hashCode();
    hash = (37 * hash) + DELIVERED_COUNT_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveredCount();
    if (getFailedUserIdsCount() > 0) {
      hash = (37 * hash) + FAILED_USER_IDS_FIELD_NUMBER;
      hash = (53 * hash) + getFailedUserIdsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zlim.message.proto.PushMessageResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zlim.message.proto.PushMessageResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zlim.message.proto.PushMessageResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zlim.message.proto.PushMessageResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 推送消息响应
   * </pre>
   *
   * Protobuf type {@code com.zlim.message.PushMessageResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zlim.message.PushMessageResponse)
      com.zlim.message.proto.PushMessageResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_PushMessageResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_PushMessageResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zlim.message.proto.PushMessageResponse.class, com.zlim.message.proto.PushMessageResponse.Builder.class);
    }

    // Construct using com.zlim.message.proto.PushMessageResponse.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      success_ = false;
      gatewayId_ = "";
      deliveredCount_ = 0;
      failedUserIds_ = emptyLongList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zlim.message.proto.MessageServiceProto.internal_static_com_zlim_message_PushMessageResponse_descriptor;
    }

    @java.lang.Override
    public com.zlim.message.proto.PushMessageResponse getDefaultInstanceForType() {
      return com.zlim.message.proto.PushMessageResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.zlim.message.proto.PushMessageResponse build() {
      com.zlim.message.proto.PushMessageResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zlim.message.proto.PushMessageResponse buildPartial() {
      com.zlim.message.proto.PushMessageResponse result = new com.zlim.message.proto.PushMessageResponse(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zlim.message.proto.PushMessageResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.success_ = success_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.gatewayId_ = gatewayId_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.deliveredCount_ = deliveredCount_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        failedUserIds_.makeImmutable();
        result.failedUserIds_ = failedUserIds_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zlim.message.proto.PushMessageResponse) {
        return mergeFrom((com.zlim.message.proto.PushMessageResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zlim.message.proto.PushMessageResponse other) {
      if (other == com.zlim.message.proto.PushMessageResponse.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getGatewayId().isEmpty()) {
        gatewayId_ = other.gatewayId_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.getDeliveredCount() != 0) {
        setDeliveredCount(other.getDeliveredCount());
      }
      if (!other.failedUserIds_.isEmpty()) {
        if (failedUserIds_.isEmpty()) {
          failedUserIds_ = other.failedUserIds_;
          failedUserIds_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureFailedUserIdsIsMutable();
          failedUserIds_.addAll(other.failedUserIds_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              success_ = input.readBool();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              gatewayId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              deliveredCount_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              long v = input.readInt64();
              ensureFailedUserIdsIsMutable();
              failedUserIds_.addLong(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureFailedUserIdsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                failedUserIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {

      success_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      bitField0_ = (bitField0_ & ~0x00000001);
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object gatewayId_ = "";
    /**
     * <code>string gateway_id = 2;</code>
     * @return The gatewayId.
     */
    public java.lang.String getGatewayId() {
      java.lang.Object ref = gatewayId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gatewayId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string gateway_id = 2;</code>
     * @return The bytes for gatewayId.
     */
    public com.google.protobuf.ByteString
        getGatewayIdBytes() {
      java.lang.Object ref = gatewayId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gatewayId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string gateway_id = 2;</code>
     * @param value The gatewayId to set.
     * @return This builder for chaining.
     */
    public Builder setGatewayId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      gatewayId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string gateway_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGatewayId() {
      gatewayId_ = getDefaultInstance().getGatewayId();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string gateway_id = 2;</code>
     * @param value The bytes for gatewayId to set.
     * @return This builder for chaining.
     */
    public Builder setGatewayIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      gatewayId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int deliveredCount_ ;
    /**
     * <code>int32 delivered_count = 3;</code>
     * @return The deliveredCount.
     */
    @java.lang.Override
    public int getDeliveredCount() {
      return deliveredCount_;
    }
    /**
     * <code>int32 delivered_count = 3;</code>
     * @param value The deliveredCount to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveredCount(int value) {

      deliveredCount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>int32 delivered_count = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveredCount() {
      bitField0_ = (bitField0_ & ~0x00000004);
      deliveredCount_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList failedUserIds_ = emptyLongList();
    private void ensureFailedUserIdsIsMutable() {
      if (!failedUserIds_.isModifiable()) {
        failedUserIds_ = makeMutableCopy(failedUserIds_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @return A list containing the failedUserIds.
     */
    public java.util.List<java.lang.Long>
        getFailedUserIdsList() {
      failedUserIds_.makeImmutable();
      return failedUserIds_;
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @return The count of failedUserIds.
     */
    public int getFailedUserIdsCount() {
      return failedUserIds_.size();
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @param index The index of the element to return.
     * @return The failedUserIds at the given index.
     */
    public long getFailedUserIds(int index) {
      return failedUserIds_.getLong(index);
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @param index The index to set the value at.
     * @param value The failedUserIds to set.
     * @return This builder for chaining.
     */
    public Builder setFailedUserIds(
        int index, long value) {

      ensureFailedUserIdsIsMutable();
      failedUserIds_.setLong(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @param value The failedUserIds to add.
     * @return This builder for chaining.
     */
    public Builder addFailedUserIds(long value) {

      ensureFailedUserIdsIsMutable();
      failedUserIds_.addLong(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @param values The failedUserIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllFailedUserIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureFailedUserIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, failedUserIds_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 failed_user_ids = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFailedUserIds() {
      failedUserIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zlim.message.PushMessageResponse)
  }

  // @@protoc_insertion_point(class_scope:com.zlim.message.PushMessageResponse)
  private static final com.zlim.message.proto.PushMessageResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zlim.message.proto.PushMessageResponse();
  }

  public static com.zlim.message.proto.PushMessageResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PushMessageResponse>
      PARSER = new com.google.protobuf.AbstractParser<PushMessageResponse>() {
    @java.lang.Override
    public PushMessageResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PushMessageResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PushMessageResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zlim.message.proto.PushMessageResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

