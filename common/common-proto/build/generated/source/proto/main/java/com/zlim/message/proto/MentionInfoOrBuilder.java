// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public interface MentionInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.message.MentionInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 user_id = 1;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <code>string username = 2;</code>
   * @return The username.
   */
  java.lang.String getUsername();
  /**
   * <code>string username = 2;</code>
   * @return The bytes for username.
   */
  com.google.protobuf.ByteString
      getUsernameBytes();

  /**
   * <code>int32 offset = 3;</code>
   * @return The offset.
   */
  int getOffset();

  /**
   * <code>int32 length = 4;</code>
   * @return The length.
   */
  int getLength();
}
