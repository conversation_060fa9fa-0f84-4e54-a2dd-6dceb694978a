// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: message_service.proto

// Protobuf Java Version: 3.25.3
package com.zlim.message.proto;

public final class MessageServiceProto {
  private MessageServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SendMessageRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SendMessageRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SendMessageRequest_ExtraEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SendMessageRequest_ExtraEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SendMessageResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SendMessageResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_GetMessageHistoryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_GetMessageHistoryRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_GetMessageHistoryResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_GetMessageHistoryResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_MarkMessageReadRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_MarkMessageReadRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_MarkMessageReadResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_MarkMessageReadResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_RecallMessageRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_RecallMessageRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_RecallMessageResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_RecallMessageResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_DeleteMessageRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_DeleteMessageRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_DeleteMessageResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_DeleteMessageResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SearchMessageRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SearchMessageRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_SearchMessageResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_SearchMessageResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_GetConversationsRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_GetConversationsRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_GetConversationsResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_GetConversationsResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_Conversation_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_Conversation_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_Conversation_ExtraEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_Conversation_ExtraEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_PushMessageRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_PushMessageRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zlim_message_PushMessageResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zlim_message_PushMessageResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025message_service.proto\022\020com.zlim.messag" +
      "e\032\014common.proto\032\rmessage.proto\"\216\002\n\022SendM" +
      "essageRequest\022\027\n\017conversation_id\030\001 \001(\t\022\021" +
      "\n\tsender_id\030\002 \001(\003\022+\n\004type\030\003 \001(\0162\035.com.zl" +
      "im.message.MessageType\0221\n\007content\030\004 \001(\0132" +
      " .com.zlim.message.MessageContent\022>\n\005ext" +
      "ra\030\005 \003(\0132/.com.zlim.message.SendMessageR" +
      "equest.ExtraEntry\032,\n\nExtraEntry\022\013\n\003key\030\001" +
      " \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"d\n\023SendMessageRe" +
      "sponse\022\017\n\007success\030\001 \001(\010\022\022\n\nmessage_id\030\002 " +
      "\001(\t\022\021\n\ttimestamp\030\003 \001(\003\022\025\n\rerror_message\030" +
      "\004 \001(\t\"t\n\030GetMessageHistoryRequest\022\027\n\017con" +
      "versation_id\030\001 \001(\t\022\017\n\007user_id\030\002 \001(\003\022\016\n\006c" +
      "ursor\030\003 \001(\t\022\r\n\005limit\030\004 \001(\005\022\017\n\007reverse\030\005 " +
      "\001(\010\"o\n\031GetMessageHistoryResponse\022+\n\010mess" +
      "ages\030\001 \003(\0132\031.com.zlim.message.Message\022\023\n" +
      "\013next_cursor\030\002 \001(\t\022\020\n\010has_more\030\003 \001(\010\"V\n\026" +
      "MarkMessageReadRequest\022\027\n\017conversation_i" +
      "d\030\001 \001(\t\022\017\n\007user_id\030\002 \001(\003\022\022\n\nmessage_id\030\003" +
      " \001(\t\";\n\027MarkMessageReadResponse\022\017\n\007succe" +
      "ss\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\";\n\024RecallMessa" +
      "geRequest\022\022\n\nmessage_id\030\001 \001(\t\022\017\n\007user_id" +
      "\030\002 \001(\003\"9\n\025RecallMessageResponse\022\017\n\007succe" +
      "ss\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\"S\n\024DeleteMessa" +
      "geRequest\022\022\n\nmessage_id\030\001 \001(\t\022\017\n\007user_id" +
      "\030\002 \001(\003\022\026\n\016delete_for_all\030\003 \001(\010\"9\n\025Delete" +
      "MessageResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007mess" +
      "age\030\002 \001(\t\"\320\001\n\024SearchMessageRequest\022\017\n\007us" +
      "er_id\030\001 \001(\003\022\017\n\007keyword\030\002 \001(\t\022\027\n\017conversa" +
      "tion_id\030\003 \001(\t\022+\n\004type\030\004 \001(\0162\035.com.zlim.m" +
      "essage.MessageType\022\022\n\nstart_time\030\005 \001(\003\022\020" +
      "\n\010end_time\030\006 \001(\003\022*\n\004page\030\007 \001(\0132\034.com.zli" +
      "m.common.PageRequest\"q\n\025SearchMessageRes" +
      "ponse\022+\n\010messages\030\001 \003(\0132\031.com.zlim.messa" +
      "ge.Message\022+\n\004page\030\002 \001(\0132\035.com.zlim.comm" +
      "on.PageResponse\"V\n\027GetConversationsReque" +
      "st\022\017\n\007user_id\030\001 \001(\003\022*\n\004page\030\002 \001(\0132\034.com." +
      "zlim.common.PageRequest\"~\n\030GetConversati" +
      "onsResponse\0225\n\rconversations\030\001 \003(\0132\036.com" +
      ".zlim.message.Conversation\022+\n\004page\030\002 \001(\013" +
      "2\035.com.zlim.common.PageResponse\"\332\002\n\014Conv" +
      "ersation\022\027\n\017conversation_id\030\001 \001(\t\0220\n\004typ" +
      "e\030\002 \001(\0162\".com.zlim.message.ConversationT" +
      "ype\022\r\n\005title\030\003 \001(\t\022\016\n\006avatar\030\004 \001(\t\022/\n\014la" +
      "st_message\030\005 \001(\0132\031.com.zlim.message.Mess" +
      "age\022\024\n\014unread_count\030\006 \001(\005\022\r\n\005muted\030\007 \001(\010" +
      "\022\016\n\006pinned\030\010 \001(\010\022\022\n\nupdated_at\030\t \001(\003\0228\n\005" +
      "extra\030\n \003(\0132).com.zlim.message.Conversat" +
      "ion.ExtraEntry\032,\n\nExtraEntry\022\013\n\003key\030\001 \001(" +
      "\t\022\r\n\005value\030\002 \001(\t:\0028\001\"f\n\022PushMessageReque" +
      "st\022\022\n\ngateway_id\030\001 \001(\t\022\020\n\010user_ids\030\002 \003(\003" +
      "\022*\n\007message\030\003 \001(\0132\031.com.zlim.message.Mes" +
      "sage\"l\n\023PushMessageResponse\022\017\n\007success\030\001" +
      " \001(\010\022\022\n\ngateway_id\030\002 \001(\t\022\027\n\017delivered_co" +
      "unt\030\003 \001(\005\022\027\n\017failed_user_ids\030\004 \003(\003*\252\001\n\020C" +
      "onversationType\022\035\n\031CONVERSATION_TYPE_UNK" +
      "NOWN\020\000\022\035\n\031CONVERSATION_TYPE_PRIVATE\020\001\022\033\n" +
      "\027CONVERSATION_TYPE_GROUP\020\002\022\035\n\031CONVERSATI" +
      "ON_TYPE_CHANNEL\020\003\022\034\n\030CONVERSATION_TYPE_S" +
      "YSTEM\020\0042\263\006\n\016MessageService\022Z\n\013SendMessag" +
      "e\022$.com.zlim.message.SendMessageRequest\032" +
      "%.com.zlim.message.SendMessageResponse\022l" +
      "\n\021GetMessageHistory\022*.com.zlim.message.G" +
      "etMessageHistoryRequest\032+.com.zlim.messa" +
      "ge.GetMessageHistoryResponse\022f\n\017MarkMess" +
      "ageRead\022(.com.zlim.message.MarkMessageRe" +
      "adRequest\032).com.zlim.message.MarkMessage" +
      "ReadResponse\022`\n\rRecallMessage\022&.com.zlim" +
      ".message.RecallMessageRequest\032\'.com.zlim" +
      ".message.RecallMessageResponse\022`\n\rDelete" +
      "Message\022&.com.zlim.message.DeleteMessage" +
      "Request\032\'.com.zlim.message.DeleteMessage" +
      "Response\022`\n\rSearchMessage\022&.com.zlim.mes" +
      "sage.SearchMessageRequest\032\'.com.zlim.mes" +
      "sage.SearchMessageResponse\022i\n\020GetConvers" +
      "ations\022).com.zlim.message.GetConversatio" +
      "nsRequest\032*.com.zlim.message.GetConversa" +
      "tionsResponse\022^\n\013PushMessage\022$.com.zlim." +
      "message.PushMessageRequest\032%.com.zlim.me" +
      "ssage.PushMessageResponse(\0010\001B/\n\026com.zli" +
      "m.message.protoB\023MessageServiceProtoP\001b\006" +
      "proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.zlim.common.proto.CommonProto.getDescriptor(),
          com.zlim.message.proto.MessageProto.getDescriptor(),
        });
    internal_static_com_zlim_message_SendMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_zlim_message_SendMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SendMessageRequest_descriptor,
        new java.lang.String[] { "ConversationId", "SenderId", "Type", "Content", "Extra", });
    internal_static_com_zlim_message_SendMessageRequest_ExtraEntry_descriptor =
      internal_static_com_zlim_message_SendMessageRequest_descriptor.getNestedTypes().get(0);
    internal_static_com_zlim_message_SendMessageRequest_ExtraEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SendMessageRequest_ExtraEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_zlim_message_SendMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_zlim_message_SendMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SendMessageResponse_descriptor,
        new java.lang.String[] { "Success", "MessageId", "Timestamp", "ErrorMessage", });
    internal_static_com_zlim_message_GetMessageHistoryRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_zlim_message_GetMessageHistoryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_GetMessageHistoryRequest_descriptor,
        new java.lang.String[] { "ConversationId", "UserId", "Cursor", "Limit", "Reverse", });
    internal_static_com_zlim_message_GetMessageHistoryResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_zlim_message_GetMessageHistoryResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_GetMessageHistoryResponse_descriptor,
        new java.lang.String[] { "Messages", "NextCursor", "HasMore", });
    internal_static_com_zlim_message_MarkMessageReadRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_zlim_message_MarkMessageReadRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_MarkMessageReadRequest_descriptor,
        new java.lang.String[] { "ConversationId", "UserId", "MessageId", });
    internal_static_com_zlim_message_MarkMessageReadResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_zlim_message_MarkMessageReadResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_MarkMessageReadResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_zlim_message_RecallMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_zlim_message_RecallMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_RecallMessageRequest_descriptor,
        new java.lang.String[] { "MessageId", "UserId", });
    internal_static_com_zlim_message_RecallMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_zlim_message_RecallMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_RecallMessageResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_zlim_message_DeleteMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_zlim_message_DeleteMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_DeleteMessageRequest_descriptor,
        new java.lang.String[] { "MessageId", "UserId", "DeleteForAll", });
    internal_static_com_zlim_message_DeleteMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_zlim_message_DeleteMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_DeleteMessageResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_zlim_message_SearchMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_zlim_message_SearchMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SearchMessageRequest_descriptor,
        new java.lang.String[] { "UserId", "Keyword", "ConversationId", "Type", "StartTime", "EndTime", "Page", });
    internal_static_com_zlim_message_SearchMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_zlim_message_SearchMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_SearchMessageResponse_descriptor,
        new java.lang.String[] { "Messages", "Page", });
    internal_static_com_zlim_message_GetConversationsRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_zlim_message_GetConversationsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_GetConversationsRequest_descriptor,
        new java.lang.String[] { "UserId", "Page", });
    internal_static_com_zlim_message_GetConversationsResponse_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_zlim_message_GetConversationsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_GetConversationsResponse_descriptor,
        new java.lang.String[] { "Conversations", "Page", });
    internal_static_com_zlim_message_Conversation_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_zlim_message_Conversation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_Conversation_descriptor,
        new java.lang.String[] { "ConversationId", "Type", "Title", "Avatar", "LastMessage", "UnreadCount", "Muted", "Pinned", "UpdatedAt", "Extra", });
    internal_static_com_zlim_message_Conversation_ExtraEntry_descriptor =
      internal_static_com_zlim_message_Conversation_descriptor.getNestedTypes().get(0);
    internal_static_com_zlim_message_Conversation_ExtraEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_Conversation_ExtraEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_zlim_message_PushMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_zlim_message_PushMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_PushMessageRequest_descriptor,
        new java.lang.String[] { "GatewayId", "UserIds", "Message", });
    internal_static_com_zlim_message_PushMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_zlim_message_PushMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zlim_message_PushMessageResponse_descriptor,
        new java.lang.String[] { "Success", "GatewayId", "DeliveredCount", "FailedUserIds", });
    com.zlim.common.proto.CommonProto.getDescriptor();
    com.zlim.message.proto.MessageProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
