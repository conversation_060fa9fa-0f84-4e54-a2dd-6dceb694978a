// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

// Protobuf Java Version: 3.25.3
package com.zlim.common.proto;

public interface DeviceInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zlim.common.DeviceInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string device_id = 1;</code>
   * @return The deviceId.
   */
  java.lang.String getDeviceId();
  /**
   * <code>string device_id = 1;</code>
   * @return The bytes for deviceId.
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <pre>
   * ios, android, web, desktop
   * </pre>
   *
   * <code>string device_type = 2;</code>
   * @return The deviceType.
   */
  java.lang.String getDeviceType();
  /**
   * <pre>
   * ios, android, web, desktop
   * </pre>
   *
   * <code>string device_type = 2;</code>
   * @return The bytes for deviceType.
   */
  com.google.protobuf.ByteString
      getDeviceTypeBytes();

  /**
   * <code>string app_version = 3;</code>
   * @return The appVersion.
   */
  java.lang.String getAppVersion();
  /**
   * <code>string app_version = 3;</code>
   * @return The bytes for appVersion.
   */
  com.google.protobuf.ByteString
      getAppVersionBytes();

  /**
   * <code>string os_version = 4;</code>
   * @return The osVersion.
   */
  java.lang.String getOsVersion();
  /**
   * <code>string os_version = 4;</code>
   * @return The bytes for osVersion.
   */
  com.google.protobuf.ByteString
      getOsVersionBytes();

  /**
   * <code>string push_token = 5;</code>
   * @return The pushToken.
   */
  java.lang.String getPushToken();
  /**
   * <code>string push_token = 5;</code>
   * @return The bytes for pushToken.
   */
  com.google.protobuf.ByteString
      getPushTokenBytes();
}
