package com.zlim.user.proto

import com.zlim.user.proto.UserServiceGrpc.getServiceDescriptor
import io.grpc.CallOptions
import io.grpc.CallOptions.DEFAULT
import io.grpc.Channel
import io.grpc.Metadata
import io.grpc.MethodDescriptor
import io.grpc.ServerServiceDefinition
import io.grpc.ServerServiceDefinition.builder
import io.grpc.ServiceDescriptor
import io.grpc.Status.UNIMPLEMENTED
import io.grpc.StatusException
import io.grpc.kotlin.AbstractCoroutineServerImpl
import io.grpc.kotlin.AbstractCoroutineStub
import io.grpc.kotlin.ClientCalls.unaryRpc
import io.grpc.kotlin.ServerCalls.unaryServerMethodDefinition
import io.grpc.kotlin.StubFor
import kotlin.String
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.jvm.JvmOverloads
import kotlin.jvm.JvmStatic

/**
 * Holder for Kotlin coroutine-based client and server APIs for com.zlim.user.UserService.
 */
public object UserServiceGrpcKt {
  public const val SERVICE_NAME: String = UserServiceGrpc.SERVICE_NAME

  @JvmStatic
  public val serviceDescriptor: ServiceDescriptor
    get() = getServiceDescriptor()

  public val registerMethod: MethodDescriptor<RegisterRequest, RegisterResponse>
    @JvmStatic
    get() = UserServiceGrpc.getRegisterMethod()

  public val loginMethod: MethodDescriptor<LoginRequest, LoginResponse>
    @JvmStatic
    get() = UserServiceGrpc.getLoginMethod()

  public val refreshTokenMethod: MethodDescriptor<RefreshTokenRequest, RefreshTokenResponse>
    @JvmStatic
    get() = UserServiceGrpc.getRefreshTokenMethod()

  public val getUserInfoMethod: MethodDescriptor<GetUserInfoRequest, GetUserInfoResponse>
    @JvmStatic
    get() = UserServiceGrpc.getGetUserInfoMethod()

  public val updateUserInfoMethod: MethodDescriptor<UpdateUserInfoRequest, UpdateUserInfoResponse>
    @JvmStatic
    get() = UserServiceGrpc.getUpdateUserInfoMethod()

  public val verifyTokenMethod: MethodDescriptor<VerifyTokenRequest, VerifyTokenResponse>
    @JvmStatic
    get() = UserServiceGrpc.getVerifyTokenMethod()

  public val logoutMethod: MethodDescriptor<LogoutRequest, LogoutResponse>
    @JvmStatic
    get() = UserServiceGrpc.getLogoutMethod()

  public val batchGetUserInfoMethod:
      MethodDescriptor<BatchGetUserInfoRequest, BatchGetUserInfoResponse>
    @JvmStatic
    get() = UserServiceGrpc.getBatchGetUserInfoMethod()

  /**
   * A stub for issuing RPCs to a(n) com.zlim.user.UserService service as suspending coroutines.
   */
  @StubFor(UserServiceGrpc::class)
  public class UserServiceCoroutineStub @JvmOverloads constructor(
    channel: Channel,
    callOptions: CallOptions = DEFAULT,
  ) : AbstractCoroutineStub<UserServiceCoroutineStub>(channel, callOptions) {
    override fun build(channel: Channel, callOptions: CallOptions): UserServiceCoroutineStub =
        UserServiceCoroutineStub(channel, callOptions)

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun register(request: RegisterRequest, headers: Metadata = Metadata()):
        RegisterResponse = unaryRpc(
      channel,
      UserServiceGrpc.getRegisterMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun login(request: LoginRequest, headers: Metadata = Metadata()): LoginResponse =
        unaryRpc(
      channel,
      UserServiceGrpc.getLoginMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun refreshToken(request: RefreshTokenRequest, headers: Metadata = Metadata()):
        RefreshTokenResponse = unaryRpc(
      channel,
      UserServiceGrpc.getRefreshTokenMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun getUserInfo(request: GetUserInfoRequest, headers: Metadata = Metadata()):
        GetUserInfoResponse = unaryRpc(
      channel,
      UserServiceGrpc.getGetUserInfoMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun updateUserInfo(request: UpdateUserInfoRequest, headers: Metadata =
        Metadata()): UpdateUserInfoResponse = unaryRpc(
      channel,
      UserServiceGrpc.getUpdateUserInfoMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun verifyToken(request: VerifyTokenRequest, headers: Metadata = Metadata()):
        VerifyTokenResponse = unaryRpc(
      channel,
      UserServiceGrpc.getVerifyTokenMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun logout(request: LogoutRequest, headers: Metadata = Metadata()):
        LogoutResponse = unaryRpc(
      channel,
      UserServiceGrpc.getLogoutMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun batchGetUserInfo(request: BatchGetUserInfoRequest, headers: Metadata =
        Metadata()): BatchGetUserInfoResponse = unaryRpc(
      channel,
      UserServiceGrpc.getBatchGetUserInfoMethod(),
      request,
      callOptions,
      headers
    )
  }

  /**
   * Skeletal implementation of the com.zlim.user.UserService service based on Kotlin coroutines.
   */
  public abstract class UserServiceCoroutineImplBase(
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
  ) : AbstractCoroutineServerImpl(coroutineContext) {
    /**
     * Returns the response to an RPC for com.zlim.user.UserService.Register.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun register(request: RegisterRequest): RegisterResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.Register is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.Login.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun login(request: LoginRequest): LoginResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.Login is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.RefreshToken.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun refreshToken(request: RefreshTokenRequest): RefreshTokenResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.RefreshToken is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.GetUserInfo.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun getUserInfo(request: GetUserInfoRequest): GetUserInfoResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.GetUserInfo is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.UpdateUserInfo.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun updateUserInfo(request: UpdateUserInfoRequest): UpdateUserInfoResponse =
        throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.UpdateUserInfo is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.VerifyToken.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun verifyToken(request: VerifyTokenRequest): VerifyTokenResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.VerifyToken is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.Logout.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun logout(request: LogoutRequest): LogoutResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.Logout is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.user.UserService.BatchGetUserInfo.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun batchGetUserInfo(request: BatchGetUserInfoRequest):
        BatchGetUserInfoResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.user.UserService.BatchGetUserInfo is unimplemented"))

    final override fun bindService(): ServerServiceDefinition = builder(getServiceDescriptor())
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getRegisterMethod(),
      implementation = ::register
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getLoginMethod(),
      implementation = ::login
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getRefreshTokenMethod(),
      implementation = ::refreshToken
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getGetUserInfoMethod(),
      implementation = ::getUserInfo
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getUpdateUserInfoMethod(),
      implementation = ::updateUserInfo
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getVerifyTokenMethod(),
      implementation = ::verifyToken
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getLogoutMethod(),
      implementation = ::logout
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = UserServiceGrpc.getBatchGetUserInfoMethod(),
      implementation = ::batchGetUserInfo
    )).build()
  }
}
