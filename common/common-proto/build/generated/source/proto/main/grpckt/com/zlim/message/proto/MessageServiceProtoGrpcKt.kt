package com.zlim.message.proto

import com.zlim.message.proto.MessageServiceGrpc.getServiceDescriptor
import io.grpc.CallOptions
import io.grpc.CallOptions.DEFAULT
import io.grpc.Channel
import io.grpc.Metadata
import io.grpc.MethodDescriptor
import io.grpc.ServerServiceDefinition
import io.grpc.ServerServiceDefinition.builder
import io.grpc.ServiceDescriptor
import io.grpc.Status.UNIMPLEMENTED
import io.grpc.StatusException
import io.grpc.kotlin.AbstractCoroutineServerImpl
import io.grpc.kotlin.AbstractCoroutineStub
import io.grpc.kotlin.ClientCalls.bidiStreamingRpc
import io.grpc.kotlin.ClientCalls.unaryRpc
import io.grpc.kotlin.ServerCalls.bidiStreamingServerMethodDefinition
import io.grpc.kotlin.ServerCalls.unaryServerMethodDefinition
import io.grpc.kotlin.StubFor
import kotlin.String
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.jvm.JvmOverloads
import kotlin.jvm.JvmStatic
import kotlinx.coroutines.flow.Flow

/**
 * Holder for Kotlin coroutine-based client and server APIs for com.zlim.message.MessageService.
 */
public object MessageServiceGrpcKt {
  public const val SERVICE_NAME: String = MessageServiceGrpc.SERVICE_NAME

  @JvmStatic
  public val serviceDescriptor: ServiceDescriptor
    get() = getServiceDescriptor()

  public val sendMessageMethod: MethodDescriptor<SendMessageRequest, SendMessageResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getSendMessageMethod()

  public val getMessageHistoryMethod:
      MethodDescriptor<GetMessageHistoryRequest, GetMessageHistoryResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getGetMessageHistoryMethod()

  public val markMessageReadMethod:
      MethodDescriptor<MarkMessageReadRequest, MarkMessageReadResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getMarkMessageReadMethod()

  public val recallMessageMethod: MethodDescriptor<RecallMessageRequest, RecallMessageResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getRecallMessageMethod()

  public val deleteMessageMethod: MethodDescriptor<DeleteMessageRequest, DeleteMessageResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getDeleteMessageMethod()

  public val searchMessageMethod: MethodDescriptor<SearchMessageRequest, SearchMessageResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getSearchMessageMethod()

  public val getConversationsMethod:
      MethodDescriptor<GetConversationsRequest, GetConversationsResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getGetConversationsMethod()

  public val pushMessageMethod: MethodDescriptor<PushMessageRequest, PushMessageResponse>
    @JvmStatic
    get() = MessageServiceGrpc.getPushMessageMethod()

  /**
   * A stub for issuing RPCs to a(n) com.zlim.message.MessageService service as suspending
   * coroutines.
   */
  @StubFor(MessageServiceGrpc::class)
  public class MessageServiceCoroutineStub @JvmOverloads constructor(
    channel: Channel,
    callOptions: CallOptions = DEFAULT,
  ) : AbstractCoroutineStub<MessageServiceCoroutineStub>(channel, callOptions) {
    override fun build(channel: Channel, callOptions: CallOptions): MessageServiceCoroutineStub =
        MessageServiceCoroutineStub(channel, callOptions)

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun sendMessage(request: SendMessageRequest, headers: Metadata = Metadata()):
        SendMessageResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getSendMessageMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun getMessageHistory(request: GetMessageHistoryRequest, headers: Metadata =
        Metadata()): GetMessageHistoryResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getGetMessageHistoryMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun markMessageRead(request: MarkMessageReadRequest, headers: Metadata =
        Metadata()): MarkMessageReadResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getMarkMessageReadMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun recallMessage(request: RecallMessageRequest, headers: Metadata = Metadata()):
        RecallMessageResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getRecallMessageMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun deleteMessage(request: DeleteMessageRequest, headers: Metadata = Metadata()):
        DeleteMessageResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getDeleteMessageMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun searchMessage(request: SearchMessageRequest, headers: Metadata = Metadata()):
        SearchMessageResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getSearchMessageMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Executes this RPC and returns the response message, suspending until the RPC completes
     * with [`Status.OK`][io.grpc.Status].  If the RPC completes with another status, a
     * corresponding
     * [StatusException] is thrown.  If this coroutine is cancelled, the RPC is also cancelled
     * with the corresponding exception as a cause.
     *
     * @param request The request message to send to the server.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return The single response from the server.
     */
    public suspend fun getConversations(request: GetConversationsRequest, headers: Metadata =
        Metadata()): GetConversationsResponse = unaryRpc(
      channel,
      MessageServiceGrpc.getGetConversationsMethod(),
      request,
      callOptions,
      headers
    )

    /**
     * Returns a [Flow] that, when collected, executes this RPC and emits responses from the
     * server as they arrive.  That flow finishes normally if the server closes its response with
     * [`Status.OK`][io.grpc.Status], and fails by throwing a [StatusException] otherwise.  If
     * collecting the flow downstream fails exceptionally (including via cancellation), the RPC
     * is cancelled with that exception as a cause.
     *
     * The [Flow] of requests is collected once each time the [Flow] of responses is
     * collected. If collection of the [Flow] of responses completes normally or
     * exceptionally before collection of `requests` completes, the collection of
     * `requests` is cancelled.  If the collection of `requests` completes
     * exceptionally for any other reason, then the collection of the [Flow] of responses
     * completes exceptionally for the same reason and the RPC is cancelled with that reason.
     *
     * @param requests A [Flow] of request messages.
     *
     * @param headers Metadata to attach to the request.  Most users will not need this.
     *
     * @return A flow that, when collected, emits the responses from the server.
     */
    public fun pushMessage(requests: Flow<PushMessageRequest>, headers: Metadata = Metadata()):
        Flow<PushMessageResponse> = bidiStreamingRpc(
      channel,
      MessageServiceGrpc.getPushMessageMethod(),
      requests,
      callOptions,
      headers
    )
  }

  /**
   * Skeletal implementation of the com.zlim.message.MessageService service based on Kotlin
   * coroutines.
   */
  public abstract class MessageServiceCoroutineImplBase(
    coroutineContext: CoroutineContext = EmptyCoroutineContext,
  ) : AbstractCoroutineServerImpl(coroutineContext) {
    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.SendMessage.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun sendMessage(request: SendMessageRequest): SendMessageResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.SendMessage is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.GetMessageHistory.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun getMessageHistory(request: GetMessageHistoryRequest):
        GetMessageHistoryResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.GetMessageHistory is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.MarkMessageRead.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun markMessageRead(request: MarkMessageReadRequest):
        MarkMessageReadResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.MarkMessageRead is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.RecallMessage.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun recallMessage(request: RecallMessageRequest): RecallMessageResponse =
        throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.RecallMessage is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.DeleteMessage.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun deleteMessage(request: DeleteMessageRequest): DeleteMessageResponse =
        throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.DeleteMessage is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.SearchMessage.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun searchMessage(request: SearchMessageRequest): SearchMessageResponse =
        throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.SearchMessage is unimplemented"))

    /**
     * Returns the response to an RPC for com.zlim.message.MessageService.GetConversations.
     *
     * If this method fails with a [StatusException], the RPC will fail with the corresponding
     * [io.grpc.Status].  If this method fails with a [java.util.concurrent.CancellationException],
     * the RPC will fail
     * with status `Status.CANCELLED`.  If this method fails for any other reason, the RPC will
     * fail with `Status.UNKNOWN` with the exception as a cause.
     *
     * @param request The request from the client.
     */
    public open suspend fun getConversations(request: GetConversationsRequest):
        GetConversationsResponse = throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.GetConversations is unimplemented"))

    /**
     * Returns a [Flow] of responses to an RPC for com.zlim.message.MessageService.PushMessage.
     *
     * If creating or collecting the returned flow fails with a [StatusException], the RPC
     * will fail with the corresponding [io.grpc.Status].  If it fails with a
     * [java.util.concurrent.CancellationException], the RPC will fail with status
     * `Status.CANCELLED`.  If creating
     * or collecting the returned flow fails for any other reason, the RPC will fail with
     * `Status.UNKNOWN` with the exception as a cause.
     *
     * @param requests A [Flow] of requests from the client.  This flow can be
     *        collected only once and throws [java.lang.IllegalStateException] on attempts to
     * collect
     *        it more than once.
     */
    public open fun pushMessage(requests: Flow<PushMessageRequest>): Flow<PushMessageResponse> =
        throw
        StatusException(UNIMPLEMENTED.withDescription("Method com.zlim.message.MessageService.PushMessage is unimplemented"))

    final override fun bindService(): ServerServiceDefinition = builder(getServiceDescriptor())
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getSendMessageMethod(),
      implementation = ::sendMessage
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getGetMessageHistoryMethod(),
      implementation = ::getMessageHistory
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getMarkMessageReadMethod(),
      implementation = ::markMessageRead
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getRecallMessageMethod(),
      implementation = ::recallMessage
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getDeleteMessageMethod(),
      implementation = ::deleteMessage
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getSearchMessageMethod(),
      implementation = ::searchMessage
    ))
      .addMethod(unaryServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getGetConversationsMethod(),
      implementation = ::getConversations
    ))
      .addMethod(bidiStreamingServerMethodDefinition(
      context = this.context,
      descriptor = MessageServiceGrpc.getPushMessageMethod(),
      implementation = ::pushMessage
    )).build()
  }
}
