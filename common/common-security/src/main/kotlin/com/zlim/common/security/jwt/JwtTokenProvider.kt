package com.zlim.common.security.jwt

import io.jsonwebtoken.*
import io.jsonwebtoken.security.Keys
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component
import java.security.Key
import java.time.Instant
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * JWT Token提供者
 */
@Component
class JwtTokenProvider(
    private val redisTemplate: RedisTemplate<String, String>
) {
    
    @Value("\${jwt.secret}")
    private lateinit var jwtSecret: String
    
    @Value("\${jwt.access-token-expiration}")
    private var accessTokenExpiration: Long = 1800000 // 30分钟
    
    @Value("\${jwt.refresh-token-expiration}")
    private var refreshTokenExpiration: Long = 604800000 // 7天
    
    private val key: Key by lazy {
        Keys.hmacShaKeyFor(jwtSecret.toByteArray())
    }
    
    /**
     * 生成访问令牌
     */
    fun generateAccessToken(userId: Long, username: String): String {
        val now = Instant.now()
        val expiryDate = now.plusMillis(accessTokenExpiration)
        
        return Jwts.builder()
            .setSubject(userId.toString())
            .claim("username", username)
            .claim("type", "access")
            .setIssuedAt(Date.from(now))
            .setExpirationTime(Date.from(expiryDate))
            .signWith(key, SignatureAlgorithm.HS512)
            .compact()
    }
    
    /**
     * 生成刷新令牌
     */
    fun generateRefreshToken(userId: Long): String {
        val now = Instant.now()
        val expiryDate = now.plusMillis(refreshTokenExpiration)
        val tokenId = UUID.randomUUID().toString()
        
        val token = Jwts.builder()
            .setSubject(userId.toString())
            .setId(tokenId)
            .claim("type", "refresh")
            .setIssuedAt(Date.from(now))
            .setExpirationTime(Date.from(expiryDate))
            .signWith(key, SignatureAlgorithm.HS512)
            .compact()
        
        // 将刷新令牌存储到Redis
        val redisKey = "refresh_token:$userId:$tokenId"
        redisTemplate.opsForValue().set(
            redisKey, 
            token, 
            refreshTokenExpiration, 
            TimeUnit.MILLISECONDS
        )
        
        return token
    }
    
    /**
     * 从令牌中获取用户ID
     */
    fun getUserIdFromToken(token: String): Long? {
        return try {
            val claims = parseToken(token)
            claims.subject.toLong()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从令牌中获取用户名
     */
    fun getUsernameFromToken(token: String): String? {
        return try {
            val claims = parseToken(token)
            claims["username"] as? String
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 验证令牌
     */
    fun validateToken(token: String): Boolean {
        return try {
            val claims = parseToken(token)
            val tokenType = claims["type"] as? String
            
            when (tokenType) {
                "access" -> true
                "refresh" -> validateRefreshToken(token, claims)
                else -> false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 验证刷新令牌
     */
    private fun validateRefreshToken(token: String, claims: Claims): Boolean {
        val userId = claims.subject.toLong()
        val tokenId = claims.id
        val redisKey = "refresh_token:$userId:$tokenId"
        
        val storedToken = redisTemplate.opsForValue().get(redisKey)
        return storedToken == token
    }
    
    /**
     * 解析令牌
     */
    private fun parseToken(token: String): Claims {
        return Jwts.parserBuilder()
            .setSigningKey(key)
            .build()
            .parseClaimsJws(token)
            .body
    }
    
    /**
     * 获取令牌过期时间
     */
    fun getExpirationFromToken(token: String): Date? {
        return try {
            val claims = parseToken(token)
            claims.expiration
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 撤销刷新令牌
     */
    fun revokeRefreshToken(token: String): Boolean {
        return try {
            val claims = parseToken(token)
            val userId = claims.subject.toLong()
            val tokenId = claims.id
            val redisKey = "refresh_token:$userId:$tokenId"
            
            redisTemplate.delete(redisKey)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 撤销用户的所有刷新令牌
     */
    fun revokeAllRefreshTokens(userId: Long) {
        val pattern = "refresh_token:$userId:*"
        val keys = redisTemplate.keys(pattern)
        if (keys.isNotEmpty()) {
            redisTemplate.delete(keys)
        }
    }
    
    /**
     * 将令牌加入黑名单
     */
    fun blacklistToken(token: String) {
        try {
            val claims = parseToken(token)
            val expiration = claims.expiration
            val now = Date()
            
            if (expiration.after(now)) {
                val ttl = expiration.time - now.time
                val blacklistKey = "blacklist_token:${token.hashCode()}"
                redisTemplate.opsForValue().set(
                    blacklistKey, 
                    "true", 
                    ttl, 
                    TimeUnit.MILLISECONDS
                )
            }
        } catch (e: Exception) {
            // 忽略解析错误
        }
    }
    
    /**
     * 检查令牌是否在黑名单中
     */
    fun isTokenBlacklisted(token: String): Boolean {
        val blacklistKey = "blacklist_token:${token.hashCode()}"
        return redisTemplate.hasKey(blacklistKey)
    }
}
