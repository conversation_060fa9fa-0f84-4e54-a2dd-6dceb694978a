dependencies {
    api(project(":common:common-core"))
    
    // Spring Security
    api("org.springframework.boot:spring-boot-starter-security")
    api("org.springframework.security:spring-security-crypto")
    
    // JWT
    api("io.jsonwebtoken:jjwt-api:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-impl:${property("jjwtVersion")}")
    api("io.jsonwebtoken:jjwt-jackson:${property("jjwtVersion")}")
    
    // Redis for token storage
    api("org.springframework.boot:spring-boot-starter-data-redis")
}
